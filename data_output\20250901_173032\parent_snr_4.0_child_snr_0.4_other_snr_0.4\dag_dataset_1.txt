parents: ['X1', 'X3', 'X10'] -> child: X0
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.37840423559064884

parents: ['X18'] -> child: X5
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.028140186847114505

parents: ['X1'] -> child: X6
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.6102285032193739

parents: ['X18'] -> child: X7
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 1.3062315159270155

parents: ['X5', 'X14', 'X16'] -> child: X8
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.5936920112601509

parents: ['X1', 'X2', 'X4', 'X11', 'X13', 'X17', 'X18'] -> child: X9
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 1.1274403092555363

parents: ['X1'] -> child: X12
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 1.0539789292122224

parents: ['X12'] -> child: X15
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.12357351306968828

parents: ['X5'] -> child: X19
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.0787961488538442