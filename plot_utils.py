
import numpy as np
import matplotlib.pyplot as plt
import os
from scm_utils import get_exclusive_node_relationships
import networkx as nx

def apply_better_layout(title=None, xlabel=None, ylabel=None, rotation=45,
                       bottom=0.2, top=0.9, left=0.1, right=0.95, pad_inches=0.2):
    """
    应用更好的图表布局，避免大面积白色空间

    Args:
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        rotation: x轴标签旋转角度
        bottom, top, left, right: 子图边距
        pad_inches: 保存时的边距
    """
    if title:
        plt.title(title, pad=20)
    if xlabel:
        plt.xlabel(xlabel)
    if ylabel:
        plt.ylabel(ylabel)

    plt.grid(True, alpha=0.3)

    if rotation:
        plt.xticks(rotation=rotation, ha='right')

    # 调整子图布局
    plt.subplots_adjust(bottom=bottom, top=top, left=left, right=right)

    return pad_inches


#----------------------------------------mutual information and correlation---------------------------------------------------
def generate_correlation_mi_visualizations(correlation_results, mutual_info_results, custom_functions_configs, image_dir, enable_plots=True):
    """
    生成所有配置的相关系数和互信息可视化，按数据集类型分别绘制

    Args:
        correlation_results: 相关系数结果列表
        mutual_info_results: 互信息结果列表
        custom_functions_configs: 自定义函数配置字典
        image_dir: 图像保存目录
        enable_plots: 是否启用图表生成，默认为True
    """
    if not enable_plots:
        print("相关系数和互信息可视化已禁用（enable_plots=False）")
        return

    if not correlation_results or not mutual_info_results:
        print("没有相关系数或互信息数据，跳过可视化")
        return

    # 按配置和数据集类型分组数据
    config_correlation_data = {}  # {config_key: {dataset_type: [dataset_data]}}
    config_mutual_info_data = {}

    # 收集相关系数数据
    for corr_result in correlation_results:
        config_key = corr_result['config_key']
        if config_key not in config_correlation_data:
            config_correlation_data[config_key] = {'train': [], 'test': [], 'intervention': []}

        # 提取目标节点相关的分类数据（三种数据集分别处理）
        if corr_result['target_correlations']:
            target_correlations = corr_result['target_correlations']

            # 为每种数据集类型分别收集数据
            for split_name in ['train', 'test', 'intervention']:
                if split_name in target_correlations:
                    split_data = target_correlations[split_name]
                    # 转换数据格式以适应可视化函数
                    dataset_data = {
                        'parents': {},
                        'children': {},
                        'spouses': {},
                        'others': {}
                    }

                    for category in ['parents', 'children', 'spouses', 'others']:
                        if category in split_data:
                            for node_name, value in split_data[category].items():
                                dataset_data[category][node_name] = [value]  # 包装成列表

                    config_correlation_data[config_key][split_name].append(dataset_data)

    # 收集互信息数据
    for mi_result in mutual_info_results:
        config_key = mi_result['config_key']
        if config_key not in config_mutual_info_data:
            config_mutual_info_data[config_key] = {'train': [], 'test': [], 'intervention': []}

        # 提取目标节点相关的分类数据（三种数据集分别处理）
        if mi_result['target_mutual_infos']:
            target_mutual_infos = mi_result['target_mutual_infos']

            # 为每种数据集类型分别收集数据
            for split_name in ['train', 'test', 'intervention']:
                if split_name in target_mutual_infos:
                    split_data = target_mutual_infos[split_name]
                    # 转换数据格式以适应可视化函数
                    dataset_data = {
                        'parents': {},
                        'children': {},
                        'spouses': {},
                        'others': {}
                    }

                    for category in ['parents', 'children', 'spouses', 'others']:
                        if category in split_data:
                            for node_name, value in split_data[category].items():
                                dataset_data[category][node_name] = [value]  # 包装成列表

                    config_mutual_info_data[config_key][split_name].append(dataset_data)

    # 为每个配置生成可视化
    for config_key in config_correlation_data.keys():
        if config_key in config_mutual_info_data:
            # 获取配置名称
            config_name = config_key
            if custom_functions_configs and config_key in custom_functions_configs:
                config_name = custom_functions_configs[config_key]['name']

            # 在一张画布上绘制三种数据集类型的箱线图
            dataset_types = ['train', 'test', 'intervention']
            dataset_type_names = ['orig_train', 'orig_test', 'intervention_test']

            # 收集三种数据集类型的数据
            all_corr_data = {}
            all_mi_data = {}

            for dataset_type in dataset_types:
                all_corr_data[dataset_type] = config_correlation_data[config_key][dataset_type]
                all_mi_data[dataset_type] = config_mutual_info_data[config_key][dataset_type]

            # 绘制相关系数的三子图
            _plot_correlation_subplots(all_corr_data, config_name, dataset_type_names, image_dir)

            # 绘制互信息的三子图
            _plot_mi_subplots(all_mi_data, config_name, dataset_type_names, image_dir)

    print(f"已为 {len(config_correlation_data)} 个配置生成按数据集类型分组的相关系数和互信息可视化")


def _plot_correlation_subplots(all_corr_data, config_name, dataset_type_names, image_dir):
    """在一张画布上绘制三种数据集类型的相关系数箱线图"""
    dataset_types = ['train', 'test', 'intervention']

    # 创建1行3列的子图
    fig, axes = plt.subplots(1, 3, figsize=(20, 6))
    fig.suptitle(f'{config_name} - Correlation between Features and Target', fontsize=16, fontweight='bold')

    for idx, (dataset_type, dataset_type_name) in enumerate(zip(dataset_types, dataset_type_names)):
        ax = axes[idx]
        plt.sca(ax)  # 设置当前轴

        # 收集该数据集类型的所有节点对数据
        all_node_pairs_corr = {}
        target_node = None

        for corr_info in all_corr_data[dataset_type]:
            if corr_info is None:
                continue

            for category in ['parents', 'children', 'spouses', 'others']:
                if category in corr_info and corr_info[category]:
                    # 获取类别前缀
                    if category == 'parents':
                        prefix = 'pa'
                    elif category == 'children':
                        prefix = 'ch'
                    elif category == 'spouses':
                        prefix = 'sp'
                    else:
                        prefix = 'ot'

                    for node_name, values in corr_info[category].items():
                        if target_node is None:
                            target_node = 'Y'

                        node_pair_label = f"{prefix}_{node_name}&target_{target_node}"

                        if node_pair_label not in all_node_pairs_corr:
                            all_node_pairs_corr[node_pair_label] = []

                        if isinstance(values, list):
                            all_node_pairs_corr[node_pair_label].extend(values)
                        else:
                            all_node_pairs_corr[node_pair_label].append(values)

        # 绘制箱线图
        if all_node_pairs_corr:
            corr_labels = list(all_node_pairs_corr.keys())
            corr_data_to_plot = [all_node_pairs_corr[label] for label in corr_labels]

            # 过滤掉空数据
            filtered_corr_data = []
            filtered_corr_labels = []
            for data, label in zip(corr_data_to_plot, corr_labels):
                if data:
                    filtered_corr_data.append(data)
                    filtered_corr_labels.append(label)

            if filtered_corr_data:
                box_plot = ax.boxplot(filtered_corr_data, labels=filtered_corr_labels, patch_artist=True)

                # 设置箱线图颜色
                colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink', 'lightgray']
                for i, patch in enumerate(box_plot['boxes']):
                    patch.set_facecolor(colors[i % len(colors)])

                # 添加均值和标准差标注，紧贴上须线
                for i, data in enumerate(filtered_corr_data):
                    if data:
                        mean_val = np.mean(data)
                        std_val = np.std(data)

                        # 获取上须线位置，更精确地贴近上须线
                        try:
                            upper_whisker = box_plot['whiskers'][i*2+1].get_ydata()[1]
                            # 标注位置紧贴上须线，使用更小的间距
                            y_range = ax.get_ylim()[1] - ax.get_ylim()[0]
                            y_pos = upper_whisker + (y_range * 0.002)  # 使用y轴范围的0.2%作为间距
                        except:
                            # 如果获取上须线失败，使用数据最大值
                            data_max = np.max(data)
                            y_range = ax.get_ylim()[1] - ax.get_ylim()[0]
                            y_pos = data_max + (y_range * 0.002)

                        ax.scatter(i+1, mean_val, color='red', s=100, marker='D', zorder=5)
                        ax.text(i+1, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                               ha='center', va='bottom', fontsize=8, fontweight='bold')

        # 设置子图标题和标签
        ax.set_title(f'{dataset_type_name}', fontsize=12, fontweight='bold')
        ax.set_xlabel('Node Pairs', fontsize=10)
        ax.set_ylabel('Correlation Coefficient', fontsize=10)
        ax.tick_params(axis='x', rotation=45, labelsize=8)
        ax.grid(True, alpha=0.3)

    # 调整布局并保存
    plt.tight_layout()
    plt.savefig(os.path.join(image_dir, f'{config_name}_correlation_boxplot_combined.png'),
               dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()


def _plot_mi_subplots(all_mi_data, config_name, dataset_type_names, image_dir):
    """在一张画布上绘制三种数据集类型的互信息箱线图"""
    dataset_types = ['train', 'test', 'intervention']

    # 创建1行3列的子图
    fig, axes = plt.subplots(1, 3, figsize=(20, 6))
    fig.suptitle(f'{config_name} - Mutual Information between Features and Target', fontsize=16, fontweight='bold')

    for idx, (dataset_type, dataset_type_name) in enumerate(zip(dataset_types, dataset_type_names)):
        ax = axes[idx]
        plt.sca(ax)  # 设置当前轴

        # 收集该数据集类型的所有节点对数据
        all_node_pairs_mi = {}
        target_node = None

        for mi_info in all_mi_data[dataset_type]:
            if mi_info is None:
                continue

            for category in ['parents', 'children', 'spouses', 'others']:
                if category in mi_info and mi_info[category]:
                    # 获取类别前缀
                    if category == 'parents':
                        prefix = 'pa'
                    elif category == 'children':
                        prefix = 'ch'
                    elif category == 'spouses':
                        prefix = 'sp'
                    else:
                        prefix = 'ot'

                    for node_name, values in mi_info[category].items():
                        if target_node is None:
                            target_node = 'Y'

                        node_pair_label = f"{prefix}_{node_name}&target_{target_node}"

                        if node_pair_label not in all_node_pairs_mi:
                            all_node_pairs_mi[node_pair_label] = []

                        if isinstance(values, list):
                            all_node_pairs_mi[node_pair_label].extend(values)
                        else:
                            all_node_pairs_mi[node_pair_label].append(values)

        # 绘制箱线图
        if all_node_pairs_mi:
            mi_labels = list(all_node_pairs_mi.keys())
            mi_data_to_plot = [all_node_pairs_mi[label] for label in mi_labels]

            # 过滤掉空数据
            filtered_mi_data = []
            filtered_mi_labels = []
            for data, label in zip(mi_data_to_plot, mi_labels):
                if data:
                    filtered_mi_data.append(data)
                    filtered_mi_labels.append(label)

            if filtered_mi_data:
                box_plot = ax.boxplot(filtered_mi_data, labels=filtered_mi_labels, patch_artist=True)

                # 设置箱线图颜色
                colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink', 'lightgray']
                for i, patch in enumerate(box_plot['boxes']):
                    patch.set_facecolor(colors[i % len(colors)])

                # 添加均值和标准差标注，紧贴上须线
                for i, data in enumerate(filtered_mi_data):
                    if data:
                        mean_val = np.mean(data)
                        std_val = np.std(data)

                        # 获取上须线位置，更精确地贴近上须线
                        try:
                            upper_whisker = box_plot['whiskers'][i*2+1].get_ydata()[1]
                            # 标注位置紧贴上须线，使用更小的间距
                            y_range = ax.get_ylim()[1] - ax.get_ylim()[0]
                            y_pos = upper_whisker + (y_range * 0.002)  # 使用y轴范围的0.2%作为间距
                        except:
                            # 如果获取上须线失败，使用数据最大值
                            data_max = np.max(data)
                            y_range = ax.get_ylim()[1] - ax.get_ylim()[0]
                            y_pos = data_max + (y_range * 0.002)

                        ax.scatter(i+1, mean_val, color='red', s=100, marker='D', zorder=5)
                        ax.text(i+1, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                               ha='center', va='bottom', fontsize=8, fontweight='bold')

        # 设置子图标题和标签
        ax.set_title(f'{dataset_type_name}', fontsize=12, fontweight='bold')
        ax.set_xlabel('Node Pairs', fontsize=10)
        ax.set_ylabel('Mutual Information', fontsize=10)
        ax.tick_params(axis='x', rotation=45, labelsize=8)
        ax.grid(True, alpha=0.3)

    # 调整布局并保存
    plt.tight_layout()
    plt.savefig(os.path.join(image_dir, f'{config_name}_mutual_info_boxplot_combined.png'),
               dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()

#-------------------------------------------------------------snr comparison---------------------------------------------------
def plot_snr_comparison(snr_results, image_dir, structure_analysis=None, enable_structure_grouped_plots=True):
    """
    自适应SNR对比图生成函数

    Args:
        snr_results: SNR验证结果列表
        image_dir: 图片保存目录
        structure_analysis: 图结构分析结果，用于决定是否合并可视化
        enable_structure_grouped_plots: 是否生成按图结构分组的可视化结果
    """
    if not snr_results:
        return

    # 检查是否是custom_functions模式（use_snr字段为字符串）还是传统SNR模式（use_snr字段为布尔值）
    is_custom_functions_mode = isinstance(snr_results[0]['use_snr'], str)

    if is_custom_functions_mode:
        # Custom functions模式：自适应处理多个配置
        plot_adaptive_custom_functions_snr_comparison(snr_results, image_dir, structure_analysis, enable_structure_grouped_plots)
    else:
        # 传统SNR vs 非SNR模式
        plot_traditional_snr_comparison(snr_results, image_dir)

def plot_adaptive_custom_functions_snr_comparison(snr_results, image_dir, structure_analysis, enable_structure_grouped_plots=True):
    """
    自适应的自定义函数配置SNR对比图
    """
    # 获取所有配置类型
    config_types = sorted(list(set(r['use_snr'] for r in snr_results)))
    n_configs = len(config_types)

    # 总是生成合并版本（忽略图结构差异）
    plot_merged_snr_comparison(snr_results, config_types, image_dir)

    # 根据参数决定是否生成按图结构分组的可视化
    if enable_structure_grouped_plots and structure_analysis and not structure_analysis['same_structure']:
        # 图结构不同且启用分组可视化，按结构分组生成可视化
        plot_grouped_snr_comparison(snr_results, config_types, image_dir, structure_analysis)

def plot_traditional_snr_comparison(snr_results, image_dir):
    """
    传统SNR vs 非SNR模式的对比图
    """
    snr_true = [r['snr_mean'] for r in snr_results if r['use_snr']]
    snr_false = [r['snr_mean'] for r in snr_results if not r['use_snr']]
    x = np.arange(max(len(snr_true), len(snr_false)))

    plt.figure(figsize=(10, 6))
    plt.plot(x[:len(snr_true)], snr_true, marker='o', label='SNR', linewidth=2, markersize=8)
    plt.plot(x[:len(snr_false)], snr_false, marker='s', label='Random', linewidth=2, markersize=8)

    for i, v in enumerate(snr_true):
        plt.text(i, v + 0.1, f'{v:.2f}', ha='center', va='bottom', fontsize=10)
    for i, v in enumerate(snr_false):
        plt.text(i, v - 0.1, f'{v:.2f}', ha='center', va='top', fontsize=10)

    plt.xlabel('Dataset ID')
    plt.ylabel('Real Mean SNR')
    plt.legend()
    plt.title('Real Mean SNR Comparison (SNR vs Random)')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(image_dir, 'snr_mean_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

def plot_merged_snr_comparison(snr_results, config_types, image_dir):
    """
    生成合并的SNR对比图（当所有数据集图结构相同时）- 箱线图版本，显示均值和标准差
    """
    fig_width = max(12, len(config_types) * 3)
    fig_height = 8  # 增加高度
    plt.figure(figsize=(fig_width, fig_height))

    # 准备箱线图数据
    box_data = []
    box_labels = []
    colors = plt.cm.Set3(np.linspace(0, 1, len(config_types)))

    for i, config_type in enumerate(config_types):
        config_data = [r['snr_mean'] for r in snr_results if r['use_snr'] == config_type]
        if config_data:
            box_data.append(config_data)
            box_labels.append(config_type)

    if box_data:
        # 绘制箱线图
        bp = plt.boxplot(box_data, labels=box_labels, patch_artist=True,
                        showmeans=True, meanline=True)

        # 设置颜色
        for i, patch in enumerate(bp['boxes']):
            patch.set_facecolor(colors[i])
            patch.set_alpha(0.7)

        # 添加均值和标准差标注，紧贴100%分位数（最大值）上方
        for i, values in enumerate(box_data):
            if values:
                mean_val = np.mean(values)
                std_val = np.std(values)
                # 获取100%分位数（最大值）并紧贴其上方标注
                max_val = max(np.max(values), 0.2)
                y_pos = max_val + (max_val * 0.01)  # 紧贴最大值上方，只留1%的微小间距
                plt.text(i+1, y_pos,
                        f'{mean_val:.2f}±{std_val:.2f}',
                        ha='center', va='bottom', fontsize=8)

    plt.ylabel('Real Mean SNR')
    plt.title(f'SNR Comparison Across {len(config_types)} Custom Function Configurations\n(Same Graph Structure - Boxplot with Mean and Std)',
             pad=20)
    plt.grid(True, alpha=0.3)

    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45, ha='right')

    # 调整布局
    plt.subplots_adjust(bottom=0.2, top=0.9)
    plt.savefig(os.path.join(image_dir, 'merged_custom_functions_snr_comparison.png'),
               dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()

def plot_grouped_snr_comparison(snr_results, config_types, image_dir, structure_analysis):
    """
    生成分组的SNR对比图（当数据集图结构不同时）
    """
    if not structure_analysis or not structure_analysis['structure_groups']:
        # 如果没有结构分析信息，回退到简单模式
        plot_simple_custom_functions_snr_comparison(snr_results, config_types, image_dir)
        return

    # 按结构组生成分别的可视化
    for group_idx, group in enumerate(structure_analysis['structure_groups']):
        # 筛选属于当前结构组的结果
        group_datasets = {(dataset_idx, config_key) for dataset_idx, config_key in group}
        group_results = []

        for result in snr_results:
            # 需要根据数据集名称匹配到group中的数据集
            for dataset_idx, config_key in group_datasets:
                if result.get('dataset', '').endswith(f'_{dataset_idx}') and result.get('use_snr') == config_key:
                    group_results.append(result)

        if group_results:
            plt.figure(figsize=(max(10, len(config_types) * 2.5), 6))
            colors = plt.cm.Set3(np.linspace(0, 1, len(config_types)))
            markers = ['o', 's', '^', 'D', 'v', 'p', '*', 'h', '+', 'x']

            for i, config_type in enumerate(config_types):
                config_data = [r['snr_mean'] for r in group_results if r['use_snr'] == config_type]
                if config_data:  # 只绘制有数据的配置
                    x = np.arange(len(config_data))
                    plt.plot(x, config_data, marker=markers[i % len(markers)],
                            color=colors[i], label=config_type, linewidth=2, markersize=8)

                    # 添加数值标签
                    for j, v in enumerate(config_data):
                        plt.text(j, v + 0.1, f'{v:.2f}', ha='center', va='bottom', fontsize=9)

            plt.xlabel('Dataset ID')
            plt.ylabel('Real Mean SNR')
            plt.legend()
            plt.title(f'SNR Comparison for Structure Group {group_idx + 1}\n({len(group)} datasets)')
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(os.path.join(image_dir, f'group_{group_idx + 1}_snr_comparison.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

def plot_simple_custom_functions_snr_comparison(snr_results, config_types, image_dir):
    """
    简单的自定义函数配置SNR对比图（回退模式）
    """
    fig_width = max(12, len(config_types) * 3)
    fig_height = 8  # 增加高度
    plt.figure(figsize=(fig_width, fig_height))
    colors = plt.cm.Set3(np.linspace(0, 1, len(config_types)))
    markers = ['o', 's', '^', 'D', 'v', 'p', '*', 'h', '+', 'x']

    for i, config_type in enumerate(config_types):
        config_data = [r['snr_mean'] for r in snr_results if r['use_snr'] == config_type]
        x = np.arange(len(config_data))
        plt.plot(x, config_data, marker=markers[i % len(markers)],
                color=colors[i], label=config_type, linewidth=2, markersize=8)

        # 添加数值标签
        for j, v in enumerate(config_data):
            plt.text(j, v + 0.1, f'{v:.2f}', ha='center', va='bottom', fontsize=9)

    plt.xlabel('Dataset ID')
    plt.ylabel('Real Mean SNR')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.title(f'SNR Comparison Across {len(config_types)} Custom Function Configurations',
             pad=20)
    plt.grid(True, alpha=0.3)
    # 调整布局
    plt.subplots_adjust(bottom=0.15, top=0.9, right=0.85)
    plt.savefig(os.path.join(image_dir, 'custom_functions_snr_comparison.png'),
               dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()

#-------------------------------------------------------------feature importance---------------------------------------------------
def collect_node_type_data_for_config(config_type, config_model_data, scm_objects, preferred_model_order):
    """
    收集指定配置下所有数据集的节点类型数据

    Returns:
        dict: {
            'markov_vs_others': {model_type: {'markov': [values], 'others': [values]}},
            'detailed_types': {model_type: {'parents': [values], 'children': [values], 'spouses': [values], 'others': [values]}}
        }
    """
    markov_vs_others_data = {}
    detailed_types_data = {}

    # 获取该配置下可用的模型类型
    available_models = [model for model in preferred_model_order if model in config_model_data]

    for model_type in available_models:
        markov_vs_others_data[model_type] = {'markov': [], 'others': []}
        detailed_types_data[model_type] = {'parents': [], 'children': [], 'spouses': [], 'others': []}

        model_data = config_model_data[model_type]

        # 遍历该模型下的所有数据集
        for dataset_idx, feature_importance in model_data.items():
            if dataset_idx not in scm_objects:
                continue

            dataset_scm_configs = scm_objects[dataset_idx]
            if config_type not in dataset_scm_configs:
                continue

            scm_info = dataset_scm_configs[config_type]

            # 获取目标节点和节点关系
            target_node = scm_info.get('selected_target', None)
            if not target_node or 'dag_nodes' not in scm_info or 'dag_edges' not in scm_info:
                continue

            # 构建图并获取节点关系
            temp_dag = nx.DiGraph()
            temp_dag.add_nodes_from(scm_info['dag_nodes'])
            temp_dag.add_edges_from(scm_info['dag_edges'])

            if target_node not in temp_dag.nodes():
                continue


            relationships = get_exclusive_node_relationships(temp_dag, target_node)

            # 分类节点并收集特征重要性
            for feature_name, importance_value in feature_importance.items():
                if feature_name == target_node:
                    continue  # 跳过目标节点本身

                # 确定节点类型（按优先级）
                if feature_name in relationships['parents']:
                    node_type = 'parents'
                    is_markov = True
                elif feature_name in relationships['children']:
                    node_type = 'children'
                    is_markov = True
                elif feature_name in relationships['spouses']:
                    node_type = 'spouses'
                    is_markov = True
                else:
                    node_type = 'others'
                    is_markov = False

                # 添加到详细分类数据
                detailed_types_data[model_type][node_type].append(importance_value)

                # 添加到马尔科夫分类数据
                if is_markov:
                    markov_vs_others_data[model_type]['markov'].append(importance_value)
                else:
                    markov_vs_others_data[model_type]['others'].append(importance_value)

    return {
        'markov_vs_others': markov_vs_others_data,
        'detailed_types': detailed_types_data
    }


def plot_permutation_importance_comparison(importance_results, image_dir, group_by_node_type=False, scm_objects=None, custom_functions_configs=None,
                                         results=None, r2_threshold=0.5, annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    绘制不同SNR配置不同模型下permutation_importance的箱线图

    Args:
        importance_results: 特征重要性结果列表
        image_dir: 图像保存目录
        group_by_node_type: 是否按节点类型分组（默认False使用原有逻辑）
        scm_objects: SCM对象字典（当group_by_node_type=True时需要）
        custom_functions_configs: 自定义函数配置字典（当group_by_node_type=True时需要）
        results: 模型结果列表，用于计算有效数据集
        r2_threshold: R2阈值，用于筛选有效数据集
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息

    当group_by_node_type=False时：
        横轴是预测模型类型，纵轴是特征重要性，颜色区分节点，每个SNR配置画一张子图
    当group_by_node_type=True时：
        按节点类型分组，每个配置单独一张画布，包含两个子图：
        1. 马尔科夫节点 vs 其他节点
        2. 父节点、子节点、配偶节点、其他节点
    """
    if not importance_results:
        print("没有特征重要性数据，跳过permutation importance对比图生成")
        return

    # 根据group_by_node_type参数选择不同的处理逻辑
    if group_by_node_type:
        if scm_objects is None:
            print("警告: group_by_node_type=True但未提供scm_objects，使用原有逻辑")
            group_by_node_type = False
        else:
            plot_permutation_importance_by_node_type(
                importance_results, image_dir, scm_objects, custom_functions_configs,
                results, r2_threshold, annotate_all_datasets, annotate_effective_datasets
            )
            return

    # 计算有效数据集（如果提供了results）
    effective_datasets = {}
    if results is not None:
        _, effective_datasets = calculate_effective_datasets(results, r2_threshold)

    # 按配置类型和模型类型分组收集permutation importance数据
    importance_data = {}  # {config_type: {feature_name: {model_type: [values]}}}

    for result in importance_results:
        config_type = result.get('use_snr', 'unknown')
        model_type = result.get('model_type', 'unknown')

        if config_type not in importance_data:
            importance_data[config_type] = {}

        # 提取permutation importance
        if 'importance' in result and 'permutation' in result['importance']:
            perm_imp = result['importance']['permutation']
            for feature_name, importance_value in perm_imp.items():
                if feature_name not in importance_data[config_type]:
                    importance_data[config_type][feature_name] = {}
                if model_type not in importance_data[config_type][feature_name]:
                    importance_data[config_type][feature_name][model_type] = []
                importance_data[config_type][feature_name][model_type].append(importance_value)

    if not importance_data:
        print("没有有效的permutation importance数据")
        return

    # 获取所有配置类型、模型类型和特征名称
    config_types = sorted(importance_data.keys())
    all_models = set()
    all_features = set()

    for config_data in importance_data.values():
        all_features.update(config_data.keys())
        for feature_data in config_data.values():
            all_models.update(feature_data.keys())

    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]
    feature_names = sorted(all_features)

    print(f"发现配置类型: {config_types}")
    print(f"发现模型类型: {model_types}")
    print(f"发现特征名称: {feature_names}")

    # 为每个配置创建一个子图
    n_configs = len(config_types)
    if n_configs == 0:
        print("没有配置数据")
        return

    # 计算子图布局
    cols = min(2, n_configs)
    rows = (n_configs + cols - 1) // cols

    fig, axes = plt.subplots(rows, cols, figsize=(8*cols, 6*rows))

    # 确保axes是一维数组
    if n_configs == 1:
        axes = [axes]
    elif rows == 1 and cols == 1:
        axes = [axes]
    elif rows == 1 or cols == 1:
        if not isinstance(axes, (list, np.ndarray)):
            axes = [axes]
        elif hasattr(axes, 'flatten'):
            axes = axes.flatten()
    else:
        axes = axes.flatten()

    # 特征颜色映射
    feature_colors = ['lightcoral', 'lightblue', 'lightgreen', 'plum', 'lightyellow', 'lightpink']

    for config_idx, config_type in enumerate(config_types):
        # 安全地获取子图
        if config_idx < len(axes):
            ax = axes[config_idx]
        else:
            print(f"警告: 配置 {config_type} 的索引 {config_idx} 超出了axes范围 {len(axes)}")
            continue

        # 为每个模型和特征组合准备数据
        all_box_data = []
        all_box_labels = []
        all_box_colors = []

        # 按模型类型分组，每个模型显示所有特征的箱线图
        for model_type in model_types:
            for feat_idx, feature_name in enumerate(feature_names):
                if (feature_name in importance_data[config_type] and
                    model_type in importance_data[config_type][feature_name] and
                    importance_data[config_type][feature_name][model_type]):

                    values = importance_data[config_type][feature_name][model_type]
                    all_box_data.append(values)
                    all_box_labels.append(f'{model_type}\n{feature_name}')
                    all_box_colors.append(feature_colors[feat_idx % len(feature_colors)])

        if all_box_data:
            # 绘制箱线图
            bp = ax.boxplot(all_box_data, labels=all_box_labels, patch_artist=True,
                           showmeans=True, meanline=True)

            # 设置颜色
            for patch, color in zip(bp['boxes'], all_box_colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)

            # 添加均值和标准差标注，紧贴100%分位数（最大值）上方
            for k, values in enumerate(all_box_data):
                if values:
                    mean_val = np.mean(values)
                    std_val = np.std(values)
                    # 获取100%分位数（最大值）并紧贴其上方标注
                    max_val = max(np.max(values), 0.2)
                    y_pos = max_val + (max_val * 0.01)  # 紧贴最大值上方，只留1%的微小间距
                    ax.text(k+1, y_pos,
                           f'{mean_val:.3f}±{std_val:.3f}',
                           ha='center', va='bottom', fontsize=7)

        ax.set_ylabel('Permutation Importance')
        # ax.set_xlabel('Model Type')
        # 在标题中添加有效数据集个数信息
        title = f'{config_type}'
        if annotate_effective_datasets and config_type in effective_datasets:
            eff_count = len(effective_datasets[config_type])
            title += f' (Eff: {eff_count})'
        ax.set_title(title)
        ax.grid(True, alpha=0.3)

        # 旋转x轴标签
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    # 隐藏多余的子图
    for i in range(n_configs, len(axes)):
        axes[i].set_visible(False)

    # 创建图例（按特征区分颜色）
    legend_elements = []
    for i, feature_name in enumerate(feature_names):
        legend_elements.append(plt.Rectangle((0,0),1,1, facecolor=feature_colors[i % len(feature_colors)],
                                           alpha=0.7, label=feature_name))

    fig.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))

    # plt.suptitle('Permutation Importance Comparison Across Configurations and Models\n(Boxplots with Mean and Std)',
    #            fontsize=16, weight='bold')
    # 调整布局
    plt.subplots_adjust(top=0.95, bottom=0.1, left=0.08, right=0.92, hspace=0.3, wspace=0.3)
    plt.savefig(os.path.join(image_dir, 'permutation_importance_comparison.png'),
               dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()

    print("已保存permutation importance对比图")

def plot_permutation_importance_by_node_type(importance_results, image_dir, scm_objects, custom_functions_configs,
                                           results=None, r2_threshold=0.5, annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    按节点类型分组绘制特征重要性箱线图
    每个配置单独一张画布，包含两个子图：
    1. 马尔科夫节点 vs 其他节点
    2. 父节点、子节点、配偶节点、其他节点

    Args:
        importance_results: 特征重要性结果列表
        image_dir: 图像保存目录
        scm_objects: SCM对象字典
        custom_functions_configs: 自定义函数配置字典
        results: 模型结果列表，用于计算有效数据集
        r2_threshold: R2阈值，用于筛选有效数据集
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    if not importance_results:
        print("没有特征重要性数据，跳过按节点类型分组的permutation importance对比图生成")
        return

    # 计算有效数据集（如果提供了results）
    effective_datasets = {}
    if results is not None:
        _, effective_datasets = calculate_effective_datasets(results, r2_threshold)

    # 按配置类型分组收集数据
    config_data = {}  # {config_type: {model_type: {dataset_idx: {feature_name: importance_value}}}}
    config_data_effective = {}  # 有效数据集的数据

    for result in importance_results:
        config_type = result.get('use_snr', 'unknown')
        model_type = result.get('model_type', 'unknown')
        dataset_name = result.get('dataset', 'unknown')

        # 从dataset_name中提取dataset_idx
        try:
            dataset_idx = int(dataset_name.split('_')[1])
        except:
            continue

        if config_type not in config_data:
            config_data[config_type] = {}
            config_data_effective[config_type] = {}
        if model_type not in config_data[config_type]:
            config_data[config_type][model_type] = {}
            config_data_effective[config_type][model_type] = {}
        if dataset_idx not in config_data[config_type][model_type]:
            config_data[config_type][model_type][dataset_idx] = {}
        if dataset_idx not in config_data_effective[config_type][model_type]:
            config_data_effective[config_type][model_type][dataset_idx] = {}

        # 提取permutation importance
        if 'importance' in result and 'permutation' in result['importance']:
            perm_imp = result['importance']['permutation']
            config_data[config_type][model_type][dataset_idx].update(perm_imp)

            # 如果是有效数据集，也添加到有效数据集
            if config_type in effective_datasets and dataset_name in effective_datasets[config_type]:
                config_data_effective[config_type][model_type][dataset_idx].update(perm_imp)

    if not config_data:
        print("没有有效的配置数据")
        return

    # 定义模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 为每个配置生成单独的图
    for config_type in sorted(config_data.keys()):
        config_name = config_type
        if custom_functions_configs and config_type in custom_functions_configs:
            config_name = custom_functions_configs[config_type]['name']

        # print(f"生成配置 {config_name} 的按节点类型分组的特征重要性图...")

        # 收集该配置下所有数据集的节点类型信息
        node_type_data = collect_node_type_data_for_config(
            config_type, config_data[config_type], scm_objects, preferred_model_order
        )

        # 收集该配置下有效数据集的节点类型信息
        node_type_data_effective = None
        if config_type in config_data_effective:
            node_type_data_effective = collect_node_type_data_for_config(
                config_type, config_data_effective[config_type], scm_objects, preferred_model_order
            )

        if not node_type_data:
            print(f"配置 {config_name} 没有有效的节点类型数据，跳过")
            continue

        # 生成两个子图的可视化
        plot_config_node_type_importance(node_type_data, config_name, image_dir,
                                        node_type_data_effective, effective_datasets, config_type,
                                        annotate_all_datasets, annotate_effective_datasets)

    print("已完成所有配置的按节点类型分组的特征重要性可视化")

def plot_config_node_type_importance(node_type_data, config_name, image_dir,
                                    node_type_data_effective=None, effective_datasets=None, config_type=None,
                                    annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    为单个配置绘制按节点类型分组的特征重要性图
    分别保存两个独立的图：马尔科夫 vs 其他，以及详细的四种类型

    Args:
        node_type_data: 所有数据集的节点类型数据
        config_name: 配置名称
        image_dir: 图像保存目录
        node_type_data_effective: 有效数据集的节点类型数据
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    markov_data = node_type_data['markov_vs_others']
    detailed_data = node_type_data['detailed_types']

    # 获取有效数据集的数据（如果提供）
    markov_data_effective = node_type_data_effective['markov_vs_others'] if node_type_data_effective else {}
    detailed_data_effective = node_type_data_effective['detailed_types'] if node_type_data_effective else {}

    # 获取可用的模型类型
    available_models = list(markov_data.keys())
    if not available_models:
        return

    # 修复文件名生成逻辑，保留小数点并替换为下划线，避免文件名冲突
    safe_config_name = "".join(c if c.isalnum() or c in (' ', '-', '_') else '_' if c == '.' else '' for c in config_name).rstrip()

    # 图1: 马尔科夫节点 vs 其他节点 - 独立画布
    plot_markov_vs_others_standalone(markov_data, available_models, config_name, safe_config_name, image_dir,
                                    markov_data_effective, effective_datasets, config_type,
                                    annotate_all_datasets, annotate_effective_datasets)

    # 图2: 详细的四种节点类型 - 独立画布
    plot_detailed_types_standalone(detailed_data, available_models, config_name, safe_config_name, image_dir,
                                  detailed_data_effective, effective_datasets, config_type,
                                  annotate_all_datasets, annotate_effective_datasets)

def plot_markov_vs_others_standalone(markov_data, available_models, config_name, safe_config_name, image_dir,
                                    markov_data_effective=None, effective_datasets=None, config_type=None,
                                    annotate_all_datasets=True, annotate_effective_datasets=True):
    """绘制马尔科夫节点 vs 其他节点的独立图"""
    # 动态计算图形尺寸，适应更多模型
    num_models = len(available_models)
    # 基础宽度 + 每个模型额外宽度，最小宽度保证
    fig_width = max(8, 3 + num_models * 1.5)
    fig_height = 8

    fig, ax = plt.subplots(1, 1, figsize=(fig_width, fig_height))

    all_box_data = []
    all_box_labels = []
    all_box_colors = []
    colors = {'markov': 'lightcoral', 'others': 'lightblue'}

    # 记录每个模型的位置范围和分隔线位置
    model_positions = {}
    separator_positions = []
    current_position = 1

    # 增加箱子之间的间距，适应更多模型
    box_spacing = max(0.8, 1.2 - num_models * 0.05)  # 模型越多，间距稍微减小但保持合理

    for i, model_type in enumerate(available_models):
        model_data = markov_data[model_type]
        start_pos = current_position

        for node_category in ['markov', 'others']:
            if model_data[node_category]:  # 只有当有数据时才添加
                all_box_data.append(model_data[node_category])
                all_box_labels.append(node_category)  # 只显示节点类型
                all_box_colors.append(colors[node_category])
                current_position += box_spacing  # 使用动态间距

        if current_position > start_pos:
            model_positions[model_type] = (start_pos, current_position - box_spacing)
            # 添加分隔线位置（除了最后一个模型）
            if i < len(available_models) - 1:
                separator_positions.append(current_position - box_spacing/2)

    if all_box_data:
        # 动态调整箱子宽度，适应更多模型
        box_width = max(0.4, 0.8 - num_models * 0.02)

        # 绘制箱线图，增加箱子之间的间距
        positions = [i * box_spacing + 1 for i in range(len(all_box_data))]
        bp = ax.boxplot(all_box_data, positions=positions, labels=all_box_labels, patch_artist=True,
                       showmeans=True, meanline=True, widths=box_width)

        # 设置颜色
        for patch, color in zip(bp['boxes'], all_box_colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        # 计算所有数据的最大值，用于确定注释位置
        all_max_values = []
        for values in all_box_data:
            if values:
                all_max_values.append(np.max(values))

        if all_max_values:
            global_max = max(all_max_values)
            # 为注释预留适中的空间
            annotation_offset = global_max * 0.12

            # 添加均值和标准差标注，避免与分隔线和纵轴重叠
            for k, values in enumerate(all_box_data):
                if values:
                    max_val = np.max(values)

                    # 检查是否靠近分隔线，如果是则稍微偏移x位置
                    x_pos = positions[k]
                    for sep_pos in separator_positions:
                        if abs(x_pos - sep_pos) < box_spacing * 0.4:  # 如果靠近分隔线
                            if x_pos < sep_pos:
                                x_pos -= box_spacing * 0.1  # 向左偏移
                            else:
                                x_pos += box_spacing * 0.1  # 向右偏移

                    # 动态调整字体大小
                    font_size = max(6, 9 - num_models * 0.2)

                    # 标注所有数据集的统计信息
                    if annotate_all_datasets:
                        mean_val = np.mean(values)
                        std_val = np.std(values)
                        y_pos = max_val + annotation_offset * 0.4
                        ax.text(x_pos, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                               ha='center', va='bottom', fontsize=font_size, color='blue',
                               bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.95, edgecolor='lightgray'))

                    # 标注有效数据集的统计信息
                    if annotate_effective_datasets and markov_data_effective:
                        # 获取对应的有效数据集数据
                        model_idx = k // 2  # 每个模型有2个箱子（markov和others）
                        node_category_idx = k % 2
                        node_category = ['markov', 'others'][node_category_idx]

                        if (model_idx < len(available_models) and
                            available_models[model_idx] in markov_data_effective and
                            node_category in markov_data_effective[available_models[model_idx]]):

                            eff_values = markov_data_effective[available_models[model_idx]][node_category]
                            if eff_values:
                                eff_mean_val = np.mean(eff_values)
                                eff_std_val = np.std(eff_values)
                                # 有效数据集标注位置稍微上移
                                eff_y_pos = max_val + annotation_offset * (0.8 if annotate_all_datasets else 0.4)
                                ax.text(x_pos, eff_y_pos, f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                                       ha='center', va='bottom', fontsize=font_size-1, color='red',
                                       bbox=dict(boxstyle='round,pad=0.1', facecolor='lightyellow', alpha=0.95, edgecolor='orange'))

            # 调整y轴上限以容纳注释（考虑双重标注的情况）
            current_ylim = ax.get_ylim()
            # 如果同时标注两种数据，需要更多空间
            multiplier = 1.4 if (annotate_all_datasets and annotate_effective_datasets) else 1.0
            new_ymax = global_max + annotation_offset * multiplier
            ax.set_ylim(current_ylim[0], max(current_ylim[1], new_ymax))

        # 绘制垂直分隔线，顶着上方横轴
        y_min, y_max = ax.get_ylim()
        for sep_pos in separator_positions:
            ax.axvline(x=sep_pos, ymin=0, ymax=1, color='gray', linestyle='--', linewidth=1.2, alpha=0.6)

        # 在底部添加模型名称
        for model_type, (start, end) in model_positions.items():
            center_x = (start + end) / 2
            # 动态调整模型名称字体大小和位置
            model_font_size = max(8, 12 - num_models * 0.3)
            ax.text(center_x, y_min - (y_max - y_min) * 0.04, model_type,
                   ha='center', va='top', fontsize=model_font_size, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.15', facecolor='lightgray', alpha=0.7))

    ax.set_ylabel('Permutation Importance', fontsize=12)
    # 在标题中添加有效数据集个数信息
    title = f'{config_name} - Markov vs Others'
    if annotate_effective_datasets and effective_datasets and config_type and config_type in effective_datasets:
        eff_count = len(effective_datasets[config_type])
        title += f' (Eff: {eff_count})'
    ax.set_title(title, fontsize=13, fontweight='bold', pad=15)
    ax.grid(True, alpha=0.3)

    # 动态调整x轴标签
    label_font_size = max(7, 10 - num_models * 0.2)
    ax.tick_params(axis='x', labelsize=label_font_size)

    # 增加图的边距
    ax.margins(x=0.02, y=0.05)

    # 保存图像
    filename = f'permutation_importance_markov_vs_others_{safe_config_name}.png'
    plt.tight_layout(pad=1.5)
    plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()

def plot_detailed_types_standalone(detailed_data, available_models, config_name, safe_config_name, image_dir,
                                  detailed_data_effective=None, effective_datasets=None, config_type=None,
                                  annotate_all_datasets=True, annotate_effective_datasets=True):
    """绘制详细四种节点类型的独立图"""
    # 动态计算图形尺寸，适应更多模型
    num_models = len(available_models)
    # 基础宽度 + 每个模型额外宽度，详细类型需要更多空间
    fig_width = max(12, 4 + num_models * 2.0)
    fig_height = 8

    fig, ax = plt.subplots(1, 1, figsize=(fig_width, fig_height))

    all_box_data = []
    all_box_labels = []
    all_box_colors = []
    colors = {'parents': 'lightcoral', 'children': 'lightgreen', 'spouses': 'plum', 'others': 'lightblue'}

    # 记录每个模型的位置范围和分隔线位置
    model_positions = {}
    separator_positions = []
    current_position = 1

    # 增加箱子之间的间距，适应更多模型
    box_spacing = max(0.6, 1.0 - num_models * 0.03)  # 模型越多，间距稍微减小但保持合理

    for i, model_type in enumerate(available_models):
        model_data = detailed_data[model_type]
        start_pos = current_position

        # 按优先级顺序：父节点 > 子节点 > 配偶节点 > 其他节点
        for node_type in ['parents', 'children', 'spouses', 'others']:
            if model_data[node_type]:  # 只有当有数据时才添加
                all_box_data.append(model_data[node_type])
                all_box_labels.append(node_type)  # 只显示节点类型
                all_box_colors.append(colors[node_type])
                current_position += box_spacing  # 使用动态间距

        if current_position > start_pos:
            model_positions[model_type] = (start_pos, current_position - box_spacing)
            # 添加分隔线位置（除了最后一个模型）
            if i < len(available_models) - 1:
                separator_positions.append(current_position - box_spacing/2)

    if all_box_data:
        # 动态调整箱子宽度，适应更多模型
        box_width = max(0.3, 0.6 - num_models * 0.015)

        # 绘制箱线图，增加箱子之间的间距
        positions = [i * box_spacing + 1 for i in range(len(all_box_data))]
        bp = ax.boxplot(all_box_data, positions=positions, labels=all_box_labels, patch_artist=True,
                       showmeans=True, meanline=True, widths=box_width)

        # 设置颜色
        for patch, color in zip(bp['boxes'], all_box_colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        # 计算所有数据的最大值，用于确定注释位置
        all_max_values = []
        for values in all_box_data:
            if values:
                all_max_values.append(np.max(values))

        if all_max_values:
            global_max = max(all_max_values)
            # 为注释预留适中的空间
            annotation_offset = global_max * 0.12

            # 添加均值和标准差标注，避免与分隔线和纵轴重叠
            for k, values in enumerate(all_box_data):
                if values:
                    max_val = np.max(values)

                    # 检查是否靠近分隔线，如果是则稍微偏移x位置
                    x_pos = positions[k]
                    for sep_pos in separator_positions:
                        if abs(x_pos - sep_pos) < box_spacing * 0.4:  # 如果靠近分隔线
                            if x_pos < sep_pos:
                                x_pos -= box_spacing * 0.1  # 向左偏移
                            else:
                                x_pos += box_spacing * 0.1  # 向右偏移

                    # 动态调整字体大小
                    font_size = max(6, 8 - num_models * 0.15)

                    # 标注所有数据集的统计信息
                    if annotate_all_datasets:
                        mean_val = np.mean(values)
                        std_val = np.std(values)
                        y_pos = max_val + annotation_offset * 0.4
                        ax.text(x_pos, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                               ha='center', va='bottom', fontsize=font_size, color='blue',
                               bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.95, edgecolor='lightgray'))

                    # 标注有效数据集的统计信息
                    if annotate_effective_datasets and detailed_data_effective:
                        # 计算对应的模型和节点类型（每个模型有4个箱子：parents, children, spouses, others）
                        model_idx = k // 4
                        node_category_idx = k % 4
                        node_categories = ['parents', 'children', 'spouses', 'others']  # 使用复数形式

                        if (model_idx < len(available_models) and
                            node_category_idx < len(node_categories)):

                            model_type = available_models[model_idx]
                            node_category = node_categories[node_category_idx]

                            if (model_type in detailed_data_effective and
                                node_category in detailed_data_effective[model_type]):

                                eff_values = detailed_data_effective[model_type][node_category]
                                if eff_values:
                                    eff_mean_val = np.mean(eff_values)
                                    eff_std_val = np.std(eff_values)
                                    # 有效数据集标注位置稍微上移
                                    eff_y_pos = max_val + annotation_offset * (0.8 if annotate_all_datasets else 0.4)
                                    ax.text(x_pos, eff_y_pos, f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                                           ha='center', va='bottom', fontsize=font_size-1, color='red',
                                           bbox=dict(boxstyle='round,pad=0.1', facecolor='lightyellow', alpha=0.95, edgecolor='orange'))

            # 调整y轴上限以容纳注释（考虑双重标注的情况）
            current_ylim = ax.get_ylim()
            # 如果同时标注两种数据，需要更多空间
            multiplier = 1.4 if (annotate_all_datasets and annotate_effective_datasets) else 1.0
            new_ymax = global_max + annotation_offset * multiplier
            ax.set_ylim(current_ylim[0], max(current_ylim[1], new_ymax))

        # 绘制垂直分隔线，顶着上方横轴
        y_min, y_max = ax.get_ylim()
        for sep_pos in separator_positions:
            ax.axvline(x=sep_pos, ymin=0, ymax=1, color='gray', linestyle='--', linewidth=1.2, alpha=0.6)

        # 在底部添加模型名称
        for model_type, (start, end) in model_positions.items():
            center_x = (start + end) / 2
            # 动态调整模型名称字体大小和位置
            model_font_size = max(7, 11 - num_models * 0.25)
            ax.text(center_x, y_min - (y_max - y_min) * 0.06, model_type,
                   ha='center', va='top', fontsize=model_font_size, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.15', facecolor='lightgray', alpha=0.7))

    ax.set_ylabel('Permutation Importance', fontsize=12)
    # 在标题中添加有效数据集个数信息
    title = f'{config_name} - Detailed Node Types'
    if annotate_effective_datasets and effective_datasets and config_type and config_type in effective_datasets:
        eff_count = len(effective_datasets[config_type])
        title += f' (Eff: {eff_count})'
    ax.set_title(title, fontsize=13, fontweight='bold', pad=15)
    ax.grid(True, alpha=0.3)

    # 动态调整x轴标签，适中的旋转角度
    label_font_size = max(6, 9 - num_models * 0.15)
    rotation_angle = min(45, 15 + num_models * 2)  # 模型越多，旋转角度越大
    plt.setp(ax.get_xticklabels(), rotation=rotation_angle, ha='right', fontsize=label_font_size)

    # 增加图的边距
    ax.margins(x=0.02, y=0.05)

    # 保存图像
    filename = f'permutation_importance_detailed_types_{safe_config_name}.png'
    plt.tight_layout(pad=1.5)
    plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()


#-------------------------------------------------------------model evaluation---------------------------------------------------
def calculate_effective_datasets(results, r2_threshold=0.5, outlier_threshold=3.0):
    """
    基于R2阈值和异常值检测计算有效数据集

    Args:
        results: 模型结果列表，每个元素包含dataset, model_type, use_snr, r2_train等字段
        r2_threshold: R2阈值，默认0.5
        outlier_threshold: 异常值检测阈值（Z分数），默认3.0

    Returns:
        dict: {config_key: {dataset_name: {model_type: r2_train_value}}} 格式的有效数据集信息
        dict: {config_key: set(effective_dataset_names)} 格式的有效数据集集合
    """
    import pandas as pd
    import numpy as np

    # 按配置分组收集数据
    config_data = {}  # {config_key: {dataset_name: {model_type: r2_train}}}

    for result in results:
        config_key = result.get('use_snr', 'unknown')
        dataset_name = result.get('dataset', 'unknown')
        model_type = result.get('model_type', 'unknown')
        r2_train = result.get('r2_train', None)  # 保持None值以便后续处理

        if config_key not in config_data:
            config_data[config_key] = {}
        if dataset_name not in config_data[config_key]:
            config_data[config_key][dataset_name] = {}

        config_data[config_key][dataset_name][model_type] = r2_train

    # 计算每个配置下的有效数据集
    effective_datasets = {}  # {config_key: set(effective_dataset_names)}

    print(f"DEBUG: 计算有效数据集，R2阈值={r2_threshold}")

    for config_key, datasets in config_data.items():
        effective_datasets[config_key] = set()
        print(f"DEBUG: 处理配置 {config_key}")

        # 为当前配置创建DataFrame以便进行统计分析
        dataset_records = []
        for dataset_name, models in datasets.items():
            record = {
                'dataset': dataset_name,
                'xgb_r2': models.get('xgboost', None),
                'tabpfn_default_r2': models.get('tabpfn_default', None),
                'tabpfn_mse_r2': models.get('tabpfn_mse', None),
                'tabpfn_muzero_r2': models.get('tabpfn_muzero', None)
            }
            dataset_records.append(record)

        if not dataset_records:
            continue

        df = pd.DataFrame(dataset_records)

        # 首先过滤掉包含None值的行
        # 检查XGBoost是否有有效值
        xgb_valid_mask = df['xgb_r2'].notna()

        # 检查TabPFN是否有任何有效值（不为None且大于0.001）
        tabpfn_valid_mask = (
            (df['tabpfn_default_r2'].notna() & (df['tabpfn_default_r2'] > 0.001)) |
            (df['tabpfn_mse_r2'].notna() & (df['tabpfn_mse_r2'] > 0.001)) |
            (df['tabpfn_muzero_r2'].notna() & (df['tabpfn_muzero_r2'] > 0.001))
        )

        # 计算每个数据集的最佳TabPFN R2值
        df['best_tabpfn_r2'] = df[['tabpfn_default_r2', 'tabpfn_mse_r2', 'tabpfn_muzero_r2']].max(axis=1, skipna=True)

        # 处理没有有效TabPFN结果的情况
        df.loc[~tabpfn_valid_mask, 'best_tabpfn_r2'] = None

        print(f"DEBUG: 总数据集数: {len(df)}")
        print(f"DEBUG: XGBoost有效数据集数: {xgb_valid_mask.sum()}")
        print(f"DEBUG: TabPFN有效数据集数: {tabpfn_valid_mask.sum()}")

        # 对于有TabPFN结果的数据集，要求两个模型都满足阈值
        both_valid_mask = xgb_valid_mask & tabpfn_valid_mask
        both_valid_df = df[both_valid_mask]

        if len(both_valid_df) > 0:
            both_r2_mask = (both_valid_df['xgb_r2'] > r2_threshold) & (both_valid_df['best_tabpfn_r2'] > r2_threshold)
            both_effective = both_valid_df[both_r2_mask]['dataset'].tolist()
            effective_datasets[config_key].update(both_effective)
            print(f"DEBUG: 双模型有效数据集: {both_effective}")

        # 对于只有XGBoost结果的数据集，只检查XGBoost
        only_xgb_mask = xgb_valid_mask & ~tabpfn_valid_mask
        only_xgb_df = df[only_xgb_mask]

        if len(only_xgb_df) > 0:
            xgb_r2_mask = only_xgb_df['xgb_r2'] > r2_threshold
            xgb_effective = only_xgb_df[xgb_r2_mask]['dataset'].tolist()
            effective_datasets[config_key].update(xgb_effective)
            print(f"DEBUG: 仅XGBoost有效数据集: {xgb_effective}")

        print(f"DEBUG: 配置 {config_key} 的有效数据集: {list(effective_datasets[config_key])}")

    return config_data, effective_datasets


def plot_r2_comparison(r2_results, image_dir, structure_analysis=None, enable_structure_grouped_plots=True,
                      r2_threshold=0.5, annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    自适应R²性能对比图生成函数

    Args:
        r2_results: R²性能结果列表（这里实际上是完整的results，包含model_type和dataset信息）
        image_dir: 图片保存目录
        structure_analysis: 图结构分析结果，用于决定是否合并可视化
        enable_structure_grouped_plots: 是否生成按图结构分组的可视化结果
        r2_threshold: R2阈值，用于筛选有效数据集
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    if not r2_results:
        return

    # 检查是否是custom_functions模式
    is_custom_functions_mode = isinstance(r2_results[0]['use_snr'], str)

    if is_custom_functions_mode:
        # Custom functions模式：自适应处理多个配置
        plot_r2_custom_functions_comparison(r2_results, image_dir, structure_analysis, enable_structure_grouped_plots,
                                          r2_threshold, annotate_all_datasets, annotate_effective_datasets)
    else:
        # 传统SNR vs 非SNR模式
        plot_r2_traditional_comparison(r2_results, image_dir)

def plot_r2_traditional_comparison(r2_results, image_dir):
    """绘制传统SNR vs 非SNR的R²性能对比图"""
    # 按数据集和模型分组
    datasets = {}
    for r in r2_results:
        dataset_name = r['dataset']
        model_type = r['model_type']

        if dataset_name not in datasets:
            datasets[dataset_name] = {}
        if model_type not in datasets[dataset_name]:
            datasets[dataset_name][model_type] = {'snr': None, 'no_snr': None}

        if r['use_snr']:
            datasets[dataset_name][model_type]['snr'] = r
        else:
            datasets[dataset_name][model_type]['no_snr'] = r

    # 只保留同时有SNR和非SNR结果的数据集
    complete_datasets = {}
    for dataset_name, models in datasets.items():
        complete_datasets[dataset_name] = {}
        for model_type, data in models.items():
            if data['snr'] is not None and data['no_snr'] is not None:
                complete_datasets[dataset_name][model_type] = data

    if not complete_datasets:
        print("没有完整的SNR/非SNR对比数据，跳过R2对比图生成")
        return

    # 生成汇总箱线图
    plot_r2_summary_boxplot_multi_model(complete_datasets, image_dir)

    # 生成详细对比图
    plot_r2_detailed_comparison_multi_model(complete_datasets, image_dir)

    # 生成模型间对比图
    plot_model_comparison(complete_datasets, image_dir)

def plot_r2_detailed_comparison_multi_model(complete_datasets, image_dir):
    """生成多模型详细对比图"""
    dataset_names = list(complete_datasets.keys())
    n_datasets = len(dataset_names)
    
    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 获取所有可用的模型类型
    all_models = set()
    for dataset_data in complete_datasets.values():
        all_models.update(dataset_data.keys())

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]
    
    # 计算子图布局
    cols = min(3, n_datasets)
    rows = (n_datasets + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(8*cols, 6*rows))
    
    # 确保axes是二维数组
    if rows == 1 and cols == 1:
        axes = [[axes]]
    elif rows == 1:
        axes = [axes]
    elif cols == 1:
        axes = [[ax] for ax in axes]
    
    # 扁平化axes用于索引
    axes_flat = [ax for row in axes for ax in row]
    
    for i, (dataset_name, models_data) in enumerate(complete_datasets.items()):
        ax = axes_flat[i]
        
        # 准备数据
        metrics = ['Train', 'Test', 'Intervention']
        n_metrics = len(metrics)
        n_models = len(model_types)
        
        x = np.arange(n_metrics)
        width = 0.35 / n_models
        
        colors = ['skyblue', 'lightcoral', 'lightgreen', 'plum']
        
        for j, model_type in enumerate(model_types):
            if model_type in models_data:
                data = models_data[model_type]
                snr_values = [data['snr']['r2_train'], data['snr']['r2_test'], data['snr']['r2_intv']]
                no_snr_values = [data['no_snr']['r2_train'], data['no_snr']['r2_test'], data['no_snr']['r2_intv']]
                
                # 绘制柱状图
                offset = (j - n_models/2 + 0.5) * width
                bars1 = ax.bar(x + offset - width/4, snr_values, width/2, 
                              label=f'{model_type.upper()} SNR' if i == 0 else "", 
                              color=colors[j % len(colors)], alpha=0.8)
                bars2 = ax.bar(x + offset + width/4, no_snr_values, width/2, 
                              label=f'{model_type.upper()} Random' if i == 0 else "", 
                              color=colors[j % len(colors)], alpha=0.5)
        
        # 设置图表属性
        # ax.set_xlabel('Evaluation Metrics')
        ax.set_ylabel('R² Score')
        ax.set_title(f'{dataset_name}')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics)
        if i == 0:
            ax.legend(fontsize=8, loc='upper left', bbox_to_anchor=(1, 1))
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1.1)
    
    # 隐藏多余的子图
    for i in range(n_datasets, len(axes_flat)):
        axes_flat[i].set_visible(False)
    
    plt.suptitle('Multi-Model SNR vs No-SNR Detailed Comparison', fontsize=16, weight='bold')
    plt.tight_layout()
    plt.savefig(os.path.join(image_dir, 'multi_model_r2_detailed_comparison.png'), 
               dpi=300, bbox_inches='tight')
    plt.close()

def plot_r2_custom_functions_comparison(r2_results, image_dir, structure_analysis=None, enable_structure_grouped_plots=True,
                                      r2_threshold=0.5, annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    自适应的custom_functions配置R²性能对比图

    Args:
        r2_results: R²性能结果列表（实际上是完整的results，包含model_type和dataset信息）
        image_dir: 图片保存目录
        structure_analysis: 图结构分析结果
        enable_structure_grouped_plots: 是否生成按图结构分组的可视化结果
        r2_threshold: R2阈值，用于筛选有效数据集
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    # 按数据集和模型分组
    datasets = {}
    config_types = set()

    for r in r2_results:
        dataset_name = r['dataset']
        model_type = r['model_type']
        config_type = r['use_snr']  # 现在是配置类型字符串
        config_types.add(config_type)

        if dataset_name not in datasets:
            datasets[dataset_name] = {}
        if model_type not in datasets[dataset_name]:
            datasets[dataset_name][model_type] = {}

        datasets[dataset_name][model_type][config_type] = r

    config_types = sorted(list(config_types))

    # 总是生成合并版本（忽略图结构差异）
    plot_merged_r2_comparison(datasets, config_types, image_dir, r2_results, r2_threshold,
                             annotate_all_datasets, annotate_effective_datasets)

    # 根据参数决定是否生成按图结构分组的可视化
    if enable_structure_grouped_plots and structure_analysis and not structure_analysis['same_structure']:
        # 图结构不同且启用分组可视化，按结构分组生成可视化
        plot_grouped_r2_comparison(datasets, config_types, image_dir, structure_analysis)

    # 生成模型性能对比图（始终生成，包含汇总统计功能）
    # 传递完整的r2_results（实际上是results），因为需要model_type和dataset信息来计算有效数据集
    # plot_custom_functions_model_comparison(datasets, config_types, image_dir, r2_results,
    #                                     r2_threshold, annotate_all_datasets, annotate_effective_datasets)

def plot_r2_summary_boxplot_multi_model(complete_datasets, image_dir):
    """生成多模型汇总箱线图"""
    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 获取所有可用的模型类型
    all_models = set()
    for dataset_data in complete_datasets.values():
        all_models.update(dataset_data.keys())

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]
    
    # 为每个模型计算改进幅度
    model_improvements = {}
    for model_type in model_types:
        model_improvements[model_type] = {
            'train': [], 'test': [], 'intv': []
        }
        
        for dataset_name, models_data in complete_datasets.items():
            if model_type in models_data:
                data = models_data[model_type]
                snr_data = data['snr']
                no_snr_data = data['no_snr']
                
                model_improvements[model_type]['train'].append(
                    snr_data['r2_train'] - no_snr_data['r2_train'])
                model_improvements[model_type]['test'].append(
                    snr_data['r2_test'] - no_snr_data['r2_test'])
                model_improvements[model_type]['intv'].append(
                    snr_data['r2_intv'] - no_snr_data['r2_intv'])

    # 创建箱线图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    metrics = ['train', 'test', 'intv']
    metric_names = ['Train', 'Test', 'Intervention']
    
    for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[i]
        
        # 准备数据
        data_for_boxplot = []
        labels = []
        for model_type in model_types:
            if model_improvements[model_type][metric]:
                data_for_boxplot.append(model_improvements[model_type][metric])
                labels.append(model_type.upper())
        
        if data_for_boxplot:
            # 绘制箱线图
            box_plot = ax.boxplot(data_for_boxplot, labels=labels, patch_artist=True)
            
            # 设置颜色
            colors = ['#87CEEB', '#FFB6C1', '#98FB98', '#DDA0DD']
            for patch, color in zip(box_plot['boxes'], colors[:len(box_plot['boxes'])]):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            # 添加均值标注，紧贴100%分位数（最大值）上方
            for j, improvements in enumerate(data_for_boxplot):
                mean_val = np.mean(improvements)
                std_val = np.std(improvements)
                # 获取100%分位数（最大值）并紧贴其上方标注
                max_val = max(np.max(improvements), 0.2)
                y_pos = max_val + (abs(max_val) * 0.01)  # 紧贴最大值上方，只留1%的微小间距
                ax.scatter(j+1, mean_val, color='red', s=100, marker='D', zorder=5)
                ax.text(j+1, y_pos, f'{mean_val:+.3f}±{std_val:.3f}',
                       ha='center', va='bottom', fontsize=9, color='red', weight='bold')
        
        # 添加零线
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.7, linewidth=1)
        
        # 设置图表属性
        ax.set_ylabel('R² Improvement')
        ax.set_title(f'{metric_name} R² Improvement Distribution')
        ax.grid(True, alpha=0.3)

    plt.suptitle(f'SNR Method Multi-Model R² Improvement Distribution Summary\\n(Based on {len(complete_datasets)} Datasets)',
                fontsize=16, weight='bold')
    plt.tight_layout()
    plt.savefig(os.path.join(image_dir, 'multi_model_r2_summary_boxplot.png'), 
               dpi=300, bbox_inches='tight')
    plt.close()

def plot_model_comparison(complete_datasets, image_dir):
    """绘制不同模型之间的性能对比"""
    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 获取所有可用的模型类型
    all_models = set()
    for dataset_data in complete_datasets.values():
        all_models.update(dataset_data.keys())

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]
    
    if len(model_types) < 2:
        return
    
    # 计算每个模型的平均性能
    model_performance = {}
    for model_type in model_types:
        model_performance[model_type] = {
            'snr': {'train': [], 'test': [], 'intv': []},
            'no_snr': {'train': [], 'test': [], 'intv': []}
        }
        
        for dataset_name, models_data in complete_datasets.items():
            if model_type in models_data:
                data = models_data[model_type]
                
                model_performance[model_type]['snr']['train'].append(data['snr']['r2_train'])
                model_performance[model_type]['snr']['test'].append(data['snr']['r2_test'])
                model_performance[model_type]['snr']['intv'].append(data['snr']['r2_intv'])
                
                model_performance[model_type]['no_snr']['train'].append(data['no_snr']['r2_train'])
                model_performance[model_type]['no_snr']['test'].append(data['no_snr']['r2_test'])
                model_performance[model_type]['no_snr']['intv'].append(data['no_snr']['r2_intv'])
    
    # 绘制对比图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    metrics = ['train', 'test', 'intv']
    metric_names = ['Train', 'Test', 'Intervention']
    snr_methods = ['snr', 'no_snr']
    snr_names = ['SNR', 'Random']
    
    for i, (snr_method, snr_name) in enumerate(zip(snr_methods, snr_names)):
        for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            ax = axes[i, j]
            
            # 准备数据
            model_means = []
            model_stds = []
            labels = []
            
            for model_type in model_types:
                if model_performance[model_type][snr_method][metric]:
                    values = model_performance[model_type][snr_method][metric]
                    model_means.append(np.mean(values))
                    model_stds.append(np.std(values))
                    labels.append(model_type.upper())
            
            if model_means:
                x_pos = np.arange(len(labels))
                bars = ax.bar(x_pos, model_means, yerr=model_stds, capsize=5, 
                             alpha=0.7, color=['skyblue', 'lightcoral', 'lightgreen', 'plum'][:len(labels)])
                
                # 添加数值标签
                for k, (bar, mean, std) in enumerate(zip(bars, model_means, model_stds)):
                    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01,
                           f'{mean:.3f}±{std:.3f}', ha='center', va='bottom', fontsize=9)
                
                ax.set_xticks(x_pos)
                ax.set_xticklabels(labels)
            
            ax.set_ylabel('R² Score')
            ax.set_title(f'{snr_name} - {metric_name}')
            ax.grid(True, alpha=0.3)
            ax.set_ylim(0, 1.1)
    
    plt.suptitle('Model Performance Comparison', fontsize=16, weight='bold')
    plt.tight_layout()
    plt.savefig(os.path.join(image_dir, 'model_performance_comparison.png'),
               dpi=300, bbox_inches='tight')
    plt.close()

def plot_merged_r2_comparison(datasets, config_types, image_dir, results=None, r2_threshold=0.5,
                             annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    生成合并的R²对比图（当所有数据集图结构相同时）

    Args:
        datasets: 数据集字典
        config_types: 配置类型列表
        image_dir: 图片保存目录
        results: 完整的结果列表，用于计算有效数据集
        r2_threshold: R2阈值
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    # 计算有效数据集（如果提供了results）
    effective_datasets = {}
    if results is not None:
        _, effective_datasets = calculate_effective_datasets(results, r2_threshold)

    # 生成按预测模型分组的图
    plot_custom_functions_summary_by_model(datasets, config_types, image_dir, suffix='_merged',
                                          effective_datasets=effective_datasets,
                                          annotate_all_datasets=annotate_all_datasets,
                                          annotate_effective_datasets=annotate_effective_datasets)

    # 生成按SNR配置分组的图
    plot_custom_functions_summary_by_config(datasets, config_types, image_dir, suffix='_merged',
                                           effective_datasets=effective_datasets,
                                           annotate_all_datasets=annotate_all_datasets,
                                           annotate_effective_datasets=annotate_effective_datasets)

def plot_grouped_r2_comparison(datasets, config_types, image_dir, structure_analysis):
    """
    生成分组的R²对比图（当数据集图结构不同时）
    """
    if structure_analysis and structure_analysis['structure_groups']:
        # 按结构组生成分别的可视化
        for group_idx, group in enumerate(structure_analysis['structure_groups']):
            # 筛选属于当前结构组的数据集
            group_datasets = {}
            group_dataset_names = set()

            # 从group中提取数据集名称模式
            for dataset_idx, config_key in group:
                # 查找匹配的数据集
                for dataset_name in datasets.keys():
                    if dataset_name.endswith(f'_{dataset_idx}'):
                        group_dataset_names.add(dataset_name)

            # 筛选数据
            for dataset_name in group_dataset_names:
                if dataset_name in datasets:
                    group_datasets[dataset_name] = datasets[dataset_name]

            if group_datasets:
                # 为每个结构组生成可视化
                plot_custom_functions_detailed_comparison(
                    group_datasets, config_types, image_dir,
                    suffix=f'_group_{group_idx + 1}'
                )
                plot_custom_functions_summary_boxplot(
                    group_datasets, config_types, image_dir,
                    suffix=f'_group_{group_idx + 1}'
                )
                # 生成按预测模型分组的图
                plot_custom_functions_summary_by_model(
                    group_datasets, config_types, image_dir,
                    suffix=f'_group_{group_idx + 1}'
                )
                # 生成按SNR配置分组的图
                plot_custom_functions_summary_by_config(
                    group_datasets, config_types, image_dir,
                    suffix=f'_group_{group_idx + 1}'
                )

        # 生成按预测模型分组的图
        plot_custom_functions_summary_by_model(datasets, config_types, image_dir)
        # 生成按SNR配置分组的图
        plot_custom_functions_summary_by_config(datasets, config_types, image_dir)

def plot_custom_functions_detailed_comparison(datasets, config_types, image_dir, suffix=''):
    """绘制custom_functions配置的详细R²对比图"""
    # 生成按数据集分组的详细对比图
    plot_custom_functions_detailed_by_dataset(datasets, config_types, image_dir, suffix)

    # 生成汇总箱线图
    # plot_custom_functions_summary_boxplot(datasets, config_types, image_dir, suffix)

def plot_custom_functions_detailed_by_dataset(datasets, config_types, image_dir, suffix=''):
    """生成按数据集分组的自定义函数配置详细对比图"""
    dataset_names = list(datasets.keys())
    n_datasets = len(dataset_names)

    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 获取所有可用的模型类型
    all_models = set()
    for dataset_data in datasets.values():
        all_models.update(dataset_data.keys())

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]

    # 计算子图布局
    cols = min(3, n_datasets)
    rows = (n_datasets + cols - 1) // cols

    plt.figure(figsize=(8*cols, 6*rows))

    if n_datasets == 1:
        axes_flat = [plt.gca()]
    else:
        # 创建子图
        axes = []
        for i in range(n_datasets):
            row = i // cols
            col = i % cols
            ax = plt.subplot(rows, cols, i + 1)
            axes.append(ax)
        axes_flat = axes

    for i, (dataset_name, models_data) in enumerate(datasets.items()):
        ax = axes_flat[i]

        # 准备数据
        metrics = ['Train', 'Test', 'Intervention']
        n_metrics = len(metrics)
        n_models = len(model_types)
        n_configs = len(config_types)

        x = np.arange(n_metrics)
        total_width = 0.8
        width = total_width / (n_models * n_configs) if (n_models * n_configs) > 0 else 0.8

        colors = ['skyblue', 'lightcoral', 'lightgreen', 'plum', 'orange', 'pink']

        for j, model_type in enumerate(model_types):
            if model_type in models_data:
                for k, config_type in enumerate(config_types):
                    if config_type in models_data[model_type]:
                        data = models_data[model_type][config_type]
                        values = [data['r2_train'], data['r2_test'], data['r2_intv']]

                        # 计算柱子位置
                        offset = (j * n_configs + k - (n_models * n_configs - 1) / 2) * width

                        # 绘制柱状图
                        ax.bar(x + offset, values, width,
                              label=f'{model_type.upper()} {config_type}' if i == 0 else "",
                              color=colors[(j * n_configs + k) % len(colors)], alpha=0.8)

        # 设置图表属性
        # ax.set_xlabel('Evaluation Metrics')
        ax.set_ylabel('R² Score')
        ax.set_title(f'{dataset_name}')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics)
        if i == 0 and n_datasets > 1:
            ax.legend(fontsize=8, loc='upper left', bbox_to_anchor=(1, 1))
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1.1)

    # 设置总标题
    title_suffix = suffix.replace('_', ' ').title() if suffix else ''
    plt.suptitle(f'Custom Functions Multi-Model R² Detailed Comparison{title_suffix}',
                fontsize=16, weight='bold')
    plt.tight_layout()

    # 保存文件
    filename = f'custom_functions_r2_detailed_comparison{suffix}.png'
    plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight')
    plt.close()

def plot_custom_functions_summary_boxplot(datasets, config_types, image_dir, suffix=''):
    """生成自定义函数配置的汇总箱线图"""
    metrics = ['r2_train', 'r2_test', 'r2_intv']
    metric_names = ['Train R²', 'Test R²', 'Intervention R²']

    plt.figure(figsize=(18, 6))

    for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = plt.subplot(1, 3, i + 1)

        # 收集每个配置的数据
        config_data = {config: [] for config in config_types}

        for models in datasets.values():
            for model_data in models.values():
                for config_type in config_types:
                    if config_type in model_data:
                        config_data[config_type].append(model_data[config_type][metric])

        # 绘制箱线图
        data_to_plot = [config_data[config] for config in config_types if config_data[config]]
        valid_config_types = [config for config in config_types if config_data[config]]

        if data_to_plot:
            bp = ax.boxplot(data_to_plot, labels=valid_config_types, patch_artist=True)

            # 设置颜色
            colors = plt.cm.Set3(np.linspace(0, 1, len(valid_config_types)))
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)

        ax.set_title(f'{metric_name} Comparison')
        ax.set_ylabel(metric_name)
        ax.tick_params(axis='x', rotation=45)

    # 设置总标题
    title_suffix = suffix.replace('_', ' ').title() if suffix else ''
    plt.suptitle(f'Custom Functions R² Summary Boxplot{title_suffix}', fontsize=16, weight='bold')
    plt.tight_layout()

    # 保存文件
    filename = f'custom_functions_r2_summary_boxplot{suffix}.png'
    plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight')
    plt.close()

def plot_custom_functions_summary_by_model(datasets, config_types, image_dir, suffix='',
                                          effective_datasets=None, annotate_all_datasets=True,
                                          annotate_effective_datasets=True):
    """
    按预测模型分组的R²汇总箱线图
    为每个预测模型画一个图，横轴是SNR配置，用来对比同个预测模型在不同SNR配置下的结果

    Args:
        datasets: 数据集字典
        config_types: 配置类型列表
        image_dir: 图片保存目录
        suffix: 文件名后缀
        effective_datasets: 有效数据集字典 {config_key: set(effective_dataset_names)}
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 获取所有可用的模型类型
    all_models = set()
    for dataset_data in datasets.values():
        all_models.update(dataset_data.keys())

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]

    if not model_types:
        return

    metrics = ['r2_train', 'r2_test', 'r2_intv']
    metric_names = ['Train R²', 'Test R²', 'Intervention R²']

    for model_type in model_types:
        plt.figure(figsize=(18, 6))

        for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            ax = plt.subplot(1, 3, i + 1)

            # 收集当前模型在每个配置下的数据
            config_data = {config: [] for config in config_types}

            for models in datasets.values():
                if model_type in models:
                    model_data = models[model_type]
                    for config_type in config_types:
                        if config_type in model_data:
                            config_data[config_type].append(model_data[config_type][metric])

            # 准备箱线图数据
            box_data = []
            box_labels = []

            for config_type in config_types:
                if config_data[config_type]:
                    box_data.append(config_data[config_type])
                    box_labels.append(config_type)

            if box_data:
                # 绘制箱线图
                bp = ax.boxplot(box_data, labels=box_labels, patch_artist=True, showmeans=True)

                # 设置颜色
                colors = ['lightblue', 'lightcoral', 'lightgreen', 'plum', 'lightyellow', 'lightpink']
                for j, patch in enumerate(bp['boxes']):
                    patch.set_facecolor(colors[j % len(colors)])
                    patch.set_alpha(0.7)

                # 计算全局最大值用于确定注释位置
                all_max_values = [np.max(values) for values in box_data if values]
                if all_max_values:
                    global_max = max(all_max_values)
                    # 使用与特征重要性图相同的注释偏移量计算方式
                    annotation_offset = global_max * 0.12

                # 添加双重标注：所有数据集 + 有效数据集
                for j, values in enumerate(box_data):
                    if values:
                        config_type = box_labels[j]
                        max_val = np.max(values)

                        # 标注所有数据集的统计信息
                        if annotate_all_datasets:
                            mean_val = np.mean(values)
                            std_val = np.std(values)
                            y_pos = max_val + annotation_offset * 0.4
                            ax.text(j+1, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                                   ha='center', va='bottom', fontsize=7, color='blue',
                                   bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.95, edgecolor='lightgray'))

                        # 标注有效数据集的统计信息
                        if (annotate_effective_datasets and effective_datasets and
                            config_type in effective_datasets and effective_datasets[config_type]):

                            # 收集有效数据集的数据
                            effective_values = []
                            effective_dataset_names = effective_datasets[config_type]

                            for dataset_name in datasets:
                                if (dataset_name in effective_dataset_names and
                                    model_type in datasets[dataset_name] and
                                    config_type in datasets[dataset_name][model_type]):
                                    effective_values.append(datasets[dataset_name][model_type][config_type][metric])

                            if effective_values:
                                eff_mean_val = np.mean(effective_values)
                                eff_std_val = np.std(effective_values)
                                # 有效数据集标注位置稍微上移，避免重叠
                                eff_y_pos = max_val + annotation_offset * (0.8 if annotate_all_datasets else 0.4)
                                ax.text(j+1, eff_y_pos, f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                                       ha='center', va='bottom', fontsize=6, color='red',
                                       bbox=dict(boxstyle='round,pad=0.1', facecolor='lightyellow', alpha=0.95, edgecolor='orange'))

                # 调整y轴上限以容纳注释
                if all_max_values:
                    current_ylim = ax.get_ylim()
                    multiplier = 1.4 if (annotate_all_datasets and annotate_effective_datasets) else 1.0
                    new_ymax = global_max + annotation_offset * multiplier
                    ax.set_ylim(current_ylim[0], max(current_ylim[1], new_ymax))

            ax.set_ylabel('R² Score')
            # ax.set_xlabel('SNR Configuration')
            # 子图标题不再包含有效数据集个数
            ax.set_title(f'{metric_name} Comparison')
            ax.grid(True, alpha=0.3)
            ax.set_ylim(0, 1.1)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), ha='center') # rotation=45

        # 设置总标题，在这里添加有效数据集个数信息
        title_suffix = suffix.replace('_', ' ').title() if suffix else ''
        main_title = f'{model_type.upper()} Performance Across SNR Configurations{title_suffix}'
        if annotate_effective_datasets and effective_datasets:
            total_effective = sum(len(datasets) for datasets in effective_datasets.values())
            main_title += f' (Eff: {total_effective})'
        plt.suptitle(main_title, fontsize=16, weight='bold')
        plt.tight_layout()

        # 保存文件
        filename = f'custom_functions_r2_by_model_{model_type}{suffix}.png'
        plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight')
        plt.close()

def plot_custom_functions_summary_by_config(datasets, config_types, image_dir, suffix='',
                                           effective_datasets=None, annotate_all_datasets=True,
                                           annotate_effective_datasets=True):
    """
    按SNR配置分组的R²汇总箱线图
    为每个SNR配置画一个图，横轴是预测模型，用来对比同个SNR配置下不同预测模型的结果

    Args:
        datasets: 数据集字典
        config_types: 配置类型列表
        image_dir: 图片保存目录
        suffix: 文件名后缀
        effective_datasets: 有效数据集字典 {config_key: set(effective_dataset_names)}
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 获取所有可用的模型类型
    all_models = set()
    for dataset_data in datasets.values():
        all_models.update(dataset_data.keys())

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]

    if not model_types:
        return

    metrics = ['r2_train', 'r2_test', 'r2_intv']
    metric_names = ['Train R²', 'Test R²', 'Intervention R²']

    for config_type in config_types:
        plt.figure(figsize=(18, 6))

        for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            ax = plt.subplot(1, 3, i + 1)

            # 收集当前配置下每个模型的数据
            model_data = {model: [] for model in model_types}

            for models in datasets.values():
                for model_type in model_types:
                    if model_type in models and config_type in models[model_type]:
                        model_data[model_type].append(models[model_type][config_type][metric])

            # 准备箱线图数据
            box_data = []
            box_labels = []

            for model_type in model_types:
                if model_data[model_type]:
                    box_data.append(model_data[model_type])
                    box_labels.append(model_type.upper())

            if box_data:
                # 绘制箱线图
                bp = ax.boxplot(box_data, labels=box_labels, patch_artist=True, showmeans=True)

                # 设置颜色
                colors = ['lightblue', 'lightcoral', 'lightgreen', 'plum', 'lightyellow', 'lightpink']
                for j, patch in enumerate(bp['boxes']):
                    patch.set_facecolor(colors[j % len(colors)])
                    patch.set_alpha(0.7)

                # 计算全局最大值用于确定注释位置
                all_max_values = [np.max(values) for values in box_data if values]
                if all_max_values:
                    global_max = max(all_max_values)
                    # 使用与特征重要性图相同的注释偏移量计算方式
                    annotation_offset = global_max * 0.12

                # 添加双重标注：所有数据集 + 有效数据集
                for j, values in enumerate(box_data):
                    if values:
                        model_type = model_types[j]
                        max_val = np.max(values)

                        # 标注所有数据集的统计信息
                        if annotate_all_datasets:
                            mean_val = np.mean(values)
                            std_val = np.std(values)
                            y_pos = max_val + annotation_offset * 0.4
                            ax.text(j+1, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                                   ha='center', va='bottom', fontsize=7, color='blue',
                                   bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.95, edgecolor='lightgray'))

                        # 标注有效数据集的统计信息
                        if (annotate_effective_datasets and effective_datasets and
                            config_type in effective_datasets and effective_datasets[config_type]):

                            # 收集有效数据集的数据
                            effective_values = []
                            effective_dataset_names = effective_datasets[config_type]

                            for dataset_name in datasets:
                                if (dataset_name in effective_dataset_names and
                                    model_type in datasets[dataset_name] and
                                    config_type in datasets[dataset_name][model_type]):
                                    effective_values.append(datasets[dataset_name][model_type][config_type][metric])

                            if effective_values:
                                eff_mean_val = np.mean(effective_values)
                                eff_std_val = np.std(effective_values)
                                # 有效数据集标注位置稍微上移，避免重叠
                                eff_y_pos = max_val + annotation_offset * (0.8 if annotate_all_datasets else 0.4)
                                ax.text(j+1, eff_y_pos, f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                                       ha='center', va='bottom', fontsize=6, color='red',
                                       bbox=dict(boxstyle='round,pad=0.1', facecolor='lightyellow', alpha=0.95, edgecolor='orange'))

                # 调整y轴上限以容纳注释
                if all_max_values:
                    current_ylim = ax.get_ylim()
                    multiplier = 1.4 if (annotate_all_datasets and annotate_effective_datasets) else 1.0
                    new_ymax = global_max + annotation_offset * multiplier
                    ax.set_ylim(current_ylim[0], max(current_ylim[1], new_ymax))

            ax.set_ylabel('R² Score')
            # ax.set_xlabel('Model Type')
            # 子图标题不再包含有效数据集个数
            ax.set_title(f'{metric_name} Comparison')
            ax.grid(True, alpha=0.3)
            ax.set_ylim(0, 1.1)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), ha='center') # rotation=45,

        # 设置总标题，在这里添加有效数据集个数信息
        title_suffix = suffix.replace('_', ' ').title() if suffix else ''
        config_display = config_type.replace('_', ' ')
        main_title = f'Model Performance Comparison - {config_display}{title_suffix}'
        if annotate_effective_datasets and effective_datasets and config_type in effective_datasets:
            eff_count = len(effective_datasets[config_type])
            main_title += f' (Eff: {eff_count})'
        plt.suptitle(main_title, fontsize=16, weight='bold')
        plt.tight_layout()

        # 保存文件
        safe_config_name = config_type.replace('.', '_').replace(' ', '_')
        filename = f'custom_functions_r2_by_config_{safe_config_name}{suffix}.png'
        plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight')
        plt.close()




def plot_custom_functions_model_comparison(datasets, config_types, image_dir, results=None, r2_threshold=0.5,
                                         annotate_all_datasets=True, annotate_effective_datasets=True):
    """
    绘制模型综合表现的汇总图
    每个SNR配置的每个数据集类型（Train/Test/Intervention）作为一个子图
    子图中用不同浅色显示不同模型的R²箱线图，并在箱线图上方标出均值±标准差

    Args:
        datasets: 数据集字典
        config_types: 配置类型列表
        image_dir: 图像保存目录
        results: 模型结果列表，用于计算有效数据集
        r2_threshold: R2阈值，用于筛选有效数据集
        annotate_all_datasets: 是否标注所有数据集的统计信息
        annotate_effective_datasets: 是否标注有效数据集的统计信息
    """
    # 定义固定的模型顺序
    preferred_model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

    # 获取所有可用的模型类型
    all_models = set()
    for dataset_data in datasets.values():
        all_models.update(dataset_data.keys())

    # 按照固定顺序排列模型，只包含可用的模型
    model_types = [model for model in preferred_model_order if model in all_models]

    if len(model_types) < 1:
        return

    # 计算有效数据集（如果提供了results）
    effective_datasets = {}
    if results is not None:
        print(f"DEBUG: plot_custom_functions_model_comparison 收到 {len(results)} 个结果")
        _, effective_datasets = calculate_effective_datasets(results, r2_threshold)
        print(f"DEBUG: 计算得到有效数据集: {effective_datasets}")
    else:
        print("DEBUG: plot_custom_functions_model_comparison 没有收到results参数")

    # 收集每个模型在每个配置下的所有性能数据和有效数据集性能数据
    model_performance = {}
    model_performance_effective = {}

    for model_type in model_types:
        model_performance[model_type] = {}
        model_performance_effective[model_type] = {}

        for config_type in config_types:
            model_performance[model_type][config_type] = {
                'train': [], 'test': [], 'intv': []
            }
            model_performance_effective[model_type][config_type] = {
                'train': [], 'test': [], 'intv': []
            }

            for dataset_name, models_data in datasets.items():
                if model_type in models_data and config_type in models_data[model_type]:
                    data = models_data[model_type][config_type]

                    # 添加到所有数据集
                    model_performance[model_type][config_type]['train'].append(data['r2_train'])
                    model_performance[model_type][config_type]['test'].append(data['r2_test'])
                    model_performance[model_type][config_type]['intv'].append(data['r2_intv'])

                    # 如果是有效数据集，也添加到有效数据集
                    if config_type in effective_datasets and dataset_name in effective_datasets[config_type]:
                        model_performance_effective[model_type][config_type]['train'].append(data['r2_train'])
                        model_performance_effective[model_type][config_type]['test'].append(data['r2_test'])
                        model_performance_effective[model_type][config_type]['intv'].append(data['r2_intv'])

    # 检查配置数量，如果太多则分批绘制
    max_configs_per_plot = 4  # 限制每个图的最大配置数量（因为现在有3个子图类型）

    if len(config_types) > max_configs_per_plot:
        # 如果配置太多，分批绘制
        plot_custom_functions_model_comparison_batched(
            model_performance, model_performance_effective, config_types, model_types, image_dir,
            max_configs_per_plot, effective_datasets, annotate_all_datasets, annotate_effective_datasets
        )
        return

    # 创建子图布局：每个配置的每个数据集类型一个子图
    metrics = ['train', 'test', 'intv']
    metric_names = ['Train', 'Test', 'Intervention']

    n_configs = len(config_types)
    n_metrics = len(metrics)

    # 计算子图布局
    fig, axes = plt.subplots(n_configs, n_metrics, figsize=(6*n_metrics, 5*n_configs))

    # 确保axes是二维数组
    if n_configs == 1 and n_metrics == 1:
        axes = [[axes]]
    elif n_configs == 1:
        axes = [axes]
    elif n_metrics == 1:
        axes = [[ax] for ax in axes]

    # 模型颜色映射（使用浅色）
    model_colors = ['lightblue', 'lightcoral', 'lightgreen', 'plum', 'lightyellow', 'lightpink']

    for i, config_type in enumerate(config_types):
        for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            ax = axes[i][j]

            # 准备每个模型的数据
            box_data = []
            box_labels = []
            box_colors = []

            for k, model_type in enumerate(model_types):
                if model_performance[model_type][config_type][metric]:
                    values = model_performance[model_type][config_type][metric]
                    box_data.append(values)
                    box_labels.append(model_type.upper())
                    box_colors.append(model_colors[k % len(model_colors)])

            if box_data:
                # 绘制箱线图
                bp = ax.boxplot(box_data, labels=box_labels, patch_artist=True,
                               showmeans=True, meanline=True)

                # 设置颜色
                for patch, color in zip(bp['boxes'], box_colors):
                    patch.set_facecolor(color)
                    patch.set_alpha(0.7)

                # 添加均值±标准差标注
                for k, values in enumerate(box_data):
                    if values:
                        model_type = model_types[k]

                        # 获取100%分位数（最大值）作为基准位置
                        max_val = max(np.max(values), 0.2)
                        y_offset = max_val * 0.01  # 基础间距

                        # 标注所有数据集的统计信息
                        if annotate_all_datasets:
                            mean_val = np.mean(values)
                            std_val = np.std(values)
                            y_pos = max_val + y_offset
                            ax.text(k+1, y_pos,
                                   f'{mean_val:.3f}±{std_val:.3f}',
                                   ha='center', va='bottom', fontsize=7, color='blue')

                        # 标注有效数据集的统计信息
                        if (annotate_effective_datasets and
                            model_type in model_performance_effective and
                            config_type in model_performance_effective[model_type] and
                            metric in model_performance_effective[model_type][config_type] and
                            model_performance_effective[model_type][config_type][metric]):

                            eff_values = model_performance_effective[model_type][config_type][metric]
                            if eff_values:
                                eff_mean_val = np.mean(eff_values)
                                eff_std_val = np.std(eff_values)
                                # 如果同时标注两种，有效数据集标注位置稍微上移
                                eff_y_pos = max_val + y_offset * (3 if annotate_all_datasets else 1)
                                ax.text(k+1, eff_y_pos,
                                       f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                                       ha='center', va='bottom', fontsize=6, color='red')

            ax.set_ylabel('R² Score')
            # 子图标题不再包含有效数据集个数
            ax.set_title(f'{config_type} - {metric_name}')
            ax.grid(True, alpha=0.3)
            ax.set_ylim(0, 1.1)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    # 设置主标题，添加有效数据集个数信息
    main_title = 'Model Performance Comparison Across Custom Function Configurations\n(Each Config-Metric Combination as Subplot)'
    if annotate_effective_datasets and effective_datasets:
        total_effective = sum(len(datasets) for datasets in effective_datasets.values())
        main_title += f'\n(Eff: {total_effective})'
    plt.suptitle(main_title, fontsize=16, weight='bold', y=0.98)
    # 调整布局，避免标题和子图重叠
    plt.subplots_adjust(top=0.92, bottom=0.08, left=0.08, right=0.95, hspace=0.3, wspace=0.3)
    plt.savefig(os.path.join(image_dir, 'custom_functions_model_performance_comparison.png'),
               dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()

def plot_custom_functions_model_comparison_batched(model_performance, model_performance_effective, config_types, model_types, image_dir,
                                                  max_configs_per_plot, effective_datasets=None, annotate_all_datasets=True, annotate_effective_datasets=True):
    """分批绘制自定义函数配置模型性能对比图，避免图像过大"""

    # 将配置类型分批
    config_batches = [config_types[i:i+max_configs_per_plot]
                     for i in range(0, len(config_types), max_configs_per_plot)]

    for batch_idx, config_batch in enumerate(config_batches):
        # 为每批创建单独的图
        metrics = ['train', 'test', 'intv']
        metric_names = ['Train R²', 'Test R²', 'Intervention R²']

        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        colors = ['lightblue', 'lightcoral', 'lightgreen', 'plum', 'lightyellow', 'lightpink']

        for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            ax = axes[j]

            # 准备箱线图数据
            box_data = []
            box_labels = []
            box_colors = []

            for i, config_type in enumerate(config_batch):
                for model_type in model_types:
                    if model_performance[model_type][config_type][metric]:
                        values = model_performance[model_type][config_type][metric]
                        box_data.append(values)
                        box_labels.append(f'{config_type}\n{model_type.upper()}')
                        box_colors.append(colors[i % len(colors)])

            if box_data:
                # 绘制箱线图
                bp = ax.boxplot(box_data, labels=box_labels, patch_artist=True,
                               showmeans=True, meanline=True)

                # 设置颜色
                for patch, color in zip(bp['boxes'], box_colors):
                    patch.set_facecolor(color)
                    patch.set_alpha(0.7)

                # 添加均值和标准差标注
                for k, values in enumerate(box_data):
                    if values:
                        # 获取对应的配置和模型信息
                        config_idx = k // len(model_types)
                        model_idx = k % len(model_types)
                        if config_idx < len(config_batch):
                            config_type = config_batch[config_idx]
                            model_type = model_types[model_idx]

                            # 获取100%分位数（最大值）作为基准位置
                            max_val = max(np.max(values), 0.2)
                            y_offset = max_val * 0.01

                            # 标注所有数据集的统计信息
                            if annotate_all_datasets:
                                mean_val = np.mean(values)
                                std_val = np.std(values)
                                y_pos = max_val + y_offset
                                ax.text(k+1, y_pos,
                                       f'{mean_val:.3f}±{std_val:.3f}',
                                       ha='center', va='bottom', fontsize=6, color='blue')

                            # 标注有效数据集的统计信息
                            if (annotate_effective_datasets and
                                model_type in model_performance_effective and
                                config_type in model_performance_effective[model_type] and
                                metric in model_performance_effective[model_type][config_type] and
                                model_performance_effective[model_type][config_type][metric]):
                                eff_values = model_performance_effective[model_type][config_type][metric]
                                if eff_values:
                                    eff_mean_val = np.mean(eff_values)
                                    eff_std_val = np.std(eff_values)
                                    # 如果同时标注两种，有效数据集标注位置稍微上移
                                    eff_y_pos = max_val + y_offset * (3 if annotate_all_datasets else 1)
                                    ax.text(k+1, eff_y_pos,
                                           f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                                           ha='center', va='bottom', fontsize=5, color='red')

            ax.set_ylabel('R² Score')
            # 子图标题不再包含有效数据集个数
            ax.set_title(metric_name)
            ax.grid(True, alpha=0.3)
            ax.set_ylim(0, 1.1)

            # 旋转x轴标签以避免重叠
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        # 设置主标题，添加有效数据集个数信息
        main_title = f'Model Performance Comparison - Batch {batch_idx + 1}/{len(config_batches)}\n(Boxplots with Mean and Std)'
        if annotate_effective_datasets and effective_datasets:
            # 计算当前批次的有效数据集总数
            batch_effective = sum(len(effective_datasets[config_type]) for config_type in config_batch if config_type in effective_datasets)
            if batch_effective > 0:
                main_title += f'\n(Eff: {batch_effective})'
        plt.suptitle(main_title, fontsize=16, weight='bold', y=0.98)
        # 调整布局
        plt.subplots_adjust(top=0.92, bottom=0.12, left=0.08, right=0.95, wspace=0.3)

        # 保存每批的图
        if batch_idx == 0:
            # 第一批使用原始文件名
            filename = 'custom_functions_model_performance_comparison.png'
        else:
            # 后续批次使用带编号的文件名
            filename = f'custom_functions_model_performance_comparison_batch_{batch_idx + 1}.png'

        plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight', pad_inches=0.2)
        plt.close()

    print(f"由于配置数量过多({len(config_types)})，模型性能对比图已分成{len(config_batches)}个批次保存")


#-------------------------------------------------------------each dataset comparison---------------------------------------------------
def compare_mlp_parameters(scm_snr, scm_no_snr, dataset_idx):
    """
    比较两个SCM对象的MLP参数是否相同
    """
    print(f"\n数据集{dataset_idx} MLP参数对比:")
    print("=" * 60)

    if not hasattr(scm_snr, 'assignment') or not hasattr(scm_no_snr, 'assignment'):
        print("❌ 缺少assignment属性")
        return

    # 获取所有非根节点
    nodes_snr = set(scm_snr.assignment.keys())
    nodes_no_snr = set(scm_no_snr.assignment.keys())

    if nodes_snr != nodes_no_snr:
        print(f"❌ 节点集合不同: SNR={nodes_snr}, 非SNR={nodes_no_snr}")
        return

    all_same = True
    for node in sorted(nodes_snr):
        assignment_snr = scm_snr.assignment[node]['assignment']
        assignment_no_snr = scm_no_snr.assignment[node]['assignment']

        # 比较f_function的参数
        if hasattr(assignment_snr, 'f_function') and hasattr(assignment_no_snr, 'f_function'):
            f_func_snr = assignment_snr.f_function
            f_func_no_snr = assignment_no_snr.f_function

            # 比较参数
            params_same = True
            if isinstance(f_func_snr, torch.nn.Sequential) and isinstance(f_func_no_snr, torch.nn.Sequential):
                for i, (layer_snr, layer_no_snr) in enumerate(zip(f_func_snr, f_func_no_snr)):
                    if isinstance(layer_snr, torch.nn.Linear) and isinstance(layer_no_snr, torch.nn.Linear):
                        weight_diff = torch.max(torch.abs(layer_snr.weight - layer_no_snr.weight)).item()
                        bias_diff = torch.max(torch.abs(layer_snr.bias - layer_no_snr.bias)).item()

                        if weight_diff > 1e-6 or bias_diff > 1e-6:
                            params_same = False
                            print(f"节点{node} 层{i}: weight_diff={weight_diff:.8f}, bias_diff={bias_diff:.8f}")

            if params_same:
                print(f"✅ 节点{node}: MLP参数相同")
            else:
                print(f"❌ 节点{node}: MLP参数不同")
                all_same = False
        else:
            print(f"❌ 节点{node}: 缺少f_function")
            all_same = False

    if all_same:
        print("✅ 所有节点的MLP参数都相同")
    else:
        print("❌ 存在MLP参数不同的节点")
    print("=" * 60)

def compare_root_data(scm_snr, scm_no_snr, dataset_idx):
    """
    比较两个SCM对象的根节点数据是否相同
    """
    print(f"\n数据集{dataset_idx} 根节点数据对比:")
    print("=" * 60)

    if not hasattr(scm_snr, 'data') or not hasattr(scm_no_snr, 'data'):
        print("❌ 缺少data属性")
        return

    # 获取根节点
    root_nodes_snr = set(scm_snr.root_nodes) if hasattr(scm_snr, 'root_nodes') else set()
    root_nodes_no_snr = set(scm_no_snr.root_nodes) if hasattr(scm_no_snr, 'root_nodes') else set()

    if root_nodes_snr != root_nodes_no_snr:
        print(f"❌ 根节点集合不同: SNR={root_nodes_snr}, 非SNR={root_nodes_no_snr}")
        return

    all_same = True
    for node in sorted(root_nodes_snr):
        if node in scm_snr.data and node in scm_no_snr.data:
            data_snr = scm_snr.data[node]
            data_no_snr = scm_no_snr.data[node]

            if data_snr.shape != data_no_snr.shape:
                print(f"❌ 节点{node}: 形状不同 SNR={data_snr.shape}, 非SNR={data_no_snr.shape}")
                all_same = False
            else:
                max_diff = torch.max(torch.abs(data_snr - data_no_snr)).item()
                if max_diff > 1e-6:
                    print(f"❌ 节点{node}: 数据不同，最大差异={max_diff:.8f}")
                    all_same = False
                else:
                    print(f"✅ 节点{node}: 数据相同")
        else:
            print(f"❌ 节点{node}: 缺少数据")
            all_same = False

    if all_same:
        print("✅ 所有根节点数据都相同")
    else:
        print("❌ 存在根节点数据不同")
    print("=" * 60)

def plot_snr_per_node_comparison(scm_snr, scm_no_snr, dataset_idx, image_dir):
    """
    绘制同一个数据集使用SNR方法和不使用SNR方法的每节点实际SNR对比图，并标注signal_var/actual_noise_var=实际SNR，目标SNR点标注target=值。
    超过y轴上限的极值点加"↑"标识。
    """
    # 获取SNR方法的结果
    snr_results = None
    if hasattr(scm_snr, 'snr_validation_results') and scm_snr.snr_validation_results is not None:
        snr_results = scm_snr.snr_validation_results

    # 获取非SNR方法的结果
    no_snr_results = None
    if hasattr(scm_no_snr, 'snr_validation_results') and scm_no_snr.snr_validation_results is not None:
        no_snr_results = scm_no_snr.snr_validation_results

    if snr_results is None and no_snr_results is None:
        return

    # 获取所有节点（优先使用SNR方法的节点，因为它有目标SNR）
    all_nodes = set()
    if snr_results:
        all_nodes.update(snr_results.get('actual_snr', {}).keys())
        all_nodes.update(snr_results.get('target_snr', {}).keys())
    if no_snr_results:
        all_nodes.update(no_snr_results.get('actual_snr', {}).keys())

    if not all_nodes:
        return

    nodes = sorted(list(all_nodes))

    plt.figure(figsize=(12, 6))

    # 先收集所有SNR数值用于分位数截断
    all_snr_values = []
    target = None
    if snr_results and 'target_snr' in snr_results:
        target_snr = snr_results['target_snr']
        target = [target_snr.get(node, float('nan')) for node in nodes]
        all_snr_values += [v for v in target if not np.isnan(v)]
    if snr_results and 'actual_snr' in snr_results:
        actual_snr_method = snr_results['actual_snr']
        actual_snr = [actual_snr_method.get(node, float('nan')) for node in nodes]
        all_snr_values += [v for v in actual_snr if not np.isnan(v)]
    if no_snr_results and 'actual_snr' in no_snr_results:
        actual_no_snr_method = no_snr_results['actual_snr']
        actual_no_snr = [actual_no_snr_method.get(node, float('nan')) for node in nodes]
        all_snr_values += [v for v in actual_no_snr if not np.isnan(v)]

    # 计算y轴上限（95%分位数）
    if all_snr_values:
        y_max = np.percentile(all_snr_values, 80)
        y_max = max(y_max, 1.0)
        plt.ylim(None, y_max * 1.1)
    else:
        y_max = None

    # 绘制目标SNR（如果有）
    if target is not None:
        plt.plot(nodes, target, marker='o', label='Target SNR', linewidth=2, markersize=8, color='blue')
        # 标注目标SNR的具体数值
        for i, v in enumerate(target):
            if not np.isnan(v):
                plot_y = min(v, y_max) if y_max is not None else v
                expr = f'target={v:.2f}'
                if y_max is not None and v > y_max:
                    expr += '↑'
                plt.text(i, plot_y+0.1, expr, ha='center', va='bottom', fontsize=9, color='blue')

    # 绘制SNR方法的实际SNR
    if snr_results and 'actual_snr' in snr_results:
        actual_snr_method = snr_results['actual_snr']
        actual_snr = [actual_snr_method.get(node, float('nan')) for node in nodes]
        plt.plot(nodes, actual_snr, marker='s', label='Actual SNR (SNR Method)', linewidth=2, markersize=8, color='orange')
        # 标注 signal_var/actual_noise_var=实际SNR
        signal_var = snr_results.get('signal_var', {})
        actual_noise_var = snr_results.get('actual_noise_var', {})
        for i, node in enumerate(nodes):
            sig = signal_var.get(node, float('nan'))
            noi = actual_noise_var.get(node, float('nan'))
            snr = actual_snr_method.get(node, float('nan'))
            v = actual_snr[i]
            if not (np.isnan(sig) or np.isnan(noi) or np.isnan(snr)):
                plot_y = min(v, y_max) if y_max is not None else v
                expr = f"{sig:.4f}/{noi:.4f}={snr:.2f}"
                if y_max is not None and v > y_max:
                    expr += '↑'
                plt.text(i, plot_y+0.1, expr, ha='center', va='bottom', fontsize=9, color='black', rotation=0)

    # 绘制非SNR方法的实际SNR
    if no_snr_results and 'actual_snr' in no_snr_results:
        actual_no_snr_method = no_snr_results['actual_snr']
        actual_no_snr = [actual_no_snr_method.get(node, float('nan')) for node in nodes]
        plt.plot(nodes, actual_no_snr, marker='^', label='Random', linewidth=2, markersize=8, color='green')
        # 标注 signal_var/actual_noise_var=实际SNR
        signal_var_no = no_snr_results.get('signal_var', {})
        actual_noise_var_no = no_snr_results.get('actual_noise_var', {})
        for i, node in enumerate(nodes):
            sig = signal_var_no.get(node, float('nan'))
            noi = actual_noise_var_no.get(node, float('nan'))
            snr = actual_no_snr_method.get(node, float('nan'))
            v = actual_no_snr[i]
            if not (np.isnan(sig) or np.isnan(noi) or np.isnan(snr)):
                plot_y = min(v, y_max) if y_max is not None else v
                expr = f"{sig:.4f}/{noi:.4f}={snr:.2f}"
                if y_max is not None and v > y_max:
                    expr += '↑'
                plt.text(i, plot_y+0.1, expr, ha='center', va='bottom', fontsize=9, color='black', rotation=0)
    plt.xlabel('Node')
    plt.ylabel('SNR')
    plt.legend()
    plt.title(f'Dataset {dataset_idx} Per-Node SNR Comparison (signal_var/actual_noise_var=actual_SNR, target_SNR)')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(image_dir, f'dataset_{dataset_idx}_snr_per_node.png'))
    plt.close()

    # 打印详细的signal_var对比
    print(f"\\n数据集{dataset_idx} signal_var详细对比:")
    print("=" * 80)
    print(f"{'节点':<8} {'层级':<6} {'父节点':<20} {'SNR方法sig_var':<15} {'非SNR方法sig_var':<15} {'差异':<10}")
    print("-" * 80)

    if snr_results and no_snr_results:
        signal_var_snr = snr_results.get('signal_var', {})
        signal_var_no_snr = no_snr_results.get('signal_var', {})

        # 获取图结构信息
        dag = scm_snr.dag if hasattr(scm_snr, 'dag') else None
        layer_nodes = scm_snr.layer_nodes if hasattr(scm_snr, 'layer_nodes') else None

        # 创建节点到层级的映射
        node_to_layer = {}
        if layer_nodes:
            for layer_idx, layer in enumerate(layer_nodes):
                for node in layer:
                    node_to_layer[node] = layer_idx

        for node in nodes:
            sig_snr = signal_var_snr.get(node, float('nan'))
            sig_no_snr = signal_var_no_snr.get(node, float('nan'))
            diff = sig_snr - sig_no_snr if not (np.isnan(sig_snr) or np.isnan(sig_no_snr)) else float('nan')

            # 获取层级信息
            layer = node_to_layer.get(node, 'N/A')

            # 获取父节点信息
            parents = []
            if dag and node in dag.nodes():
                parents = list(dag.predecessors(node))
            parents_str = str(parents) if parents else "[]"
            if len(parents_str) > 18:
                parents_str = parents_str[:15] + "..."

            print(f"{str(node):<8} {str(layer):<6} {parents_str:<20} {sig_snr:<15.6f} {sig_no_snr:<15.6f} {diff:<10.6f}")
    print("=" * 80)
