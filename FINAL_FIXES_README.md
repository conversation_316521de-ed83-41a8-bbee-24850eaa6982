# 最终修复总结

## 修复内容

### 1. 删除DAG重试逻辑 ✅

**问题**：之前如果DAG生成有问题，会在单个数据集内进行多次重试，重新生成DAG。

**修复**：
- 完全移除了SCM类中的DAG重新生成重试逻辑
- 现在如果DAG有问题，直接跳过生成下一个数据集
- 不再在单个数据集内进行多次重试

**代码变化**：
```python
# 之前：复杂的重试逻辑，重新生成DAG
if _current_retry < max_retries:
    print(f"重新生成DAG... (重试 {_current_retry + 1}/{max_retries})")
    # ... 重新生成DAG的代码

# 现在：直接跳过
print("跳过当前数据集生成")
if self.allow_skip:
    raise SkipDatasetException("未能找到有效目标节点")
```

### 2. 修复custom_dag_size参数使用 ✅

**问题**：
- 使用`custom_dag_type='random_ER'`时，如果不指定`custom_dag_size`，默认使用`'small'`规模
- 导致期望medium规模（21-50节点）但实际得到small规模（11-20节点）

**修复**：
1. **添加`custom_dag_size`参数**到`generate_datasets`函数
2. **智能参数传递**：只有随机图才传递size参数
3. **明确文档说明**：预定义结构不需要size参数

**使用方法**：
```python
# ✅ 正确：随机图明确指定size
datasets = generate_datasets(
    num_dataset=100,
    h_config=h_config,
    custom_dag_type='random_ER',
    custom_dag_size='medium',  # 明确指定medium规模
    seed=42
)

# ✅ 正确：预定义结构不需要size
datasets = generate_datasets(
    num_dataset=100,
    h_config=h_config,
    custom_dag_type='common_cause',  # 不需要指定size
    seed=42
)
```

### 3. 孤立节点移除控制 ✅

**问题分析**：
```python
def _remove_isolating_node(B):
    """移除孤立节点"""
    non_iso_index = np.logical_or(B.any(axis=0), B.any(axis=1))
    if non_iso_index.any():
        return B[non_iso_index][:, non_iso_index]
    return B
```

- `B.any(axis=0)`：检查每列是否有非零元素（该节点是否有入边）
- `B.any(axis=1)`：检查每行是否有非零元素（该节点是否有出边）
- **移除所有孤立节点**：没有任何连接的节点都会被移除
- **导致特征数量减少**：移除节点后，特征数量不等于原始节点数-1

**修复**：
1. **添加`remove_isolated_nodes`参数**，默认为`False`
2. **只有明确指定时才移除孤立节点**
3. **保持向后兼容性**

**使用方法**：
```python
# 默认行为：不移除孤立节点（保持特征数量一致）
datasets = generate_datasets(
    num_dataset=100,
    h_config=h_config,
    custom_dag_type='random_ER',
    custom_dag_size='medium',
    remove_isolated_nodes=False,  # 默认值
    seed=42
)

# 如果需要移除孤立节点
datasets = generate_datasets(
    num_dataset=100,
    h_config=h_config,
    custom_dag_type='random_ER',
    custom_dag_size='medium',
    remove_isolated_nodes=True,  # 明确指定移除
    seed=42
)
```

## 参数说明

### generate_datasets函数新增参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `custom_dag_size` | str | 'small' | 随机图规模：'small'(11-20), 'medium'(21-50), 'large'(51-100) |
| `remove_isolated_nodes` | bool | False | 是否移除孤立节点 |

### 规模对应关系

| 规模 | 节点数范围 | 适用场景 |
|------|------------|----------|
| small | 11-20 | 快速测试、小规模实验 |
| medium | 21-50 | 中等规模研究 |
| large | 51-100 | 大规模实验 |

## 最佳实践

### 1. 明确指定参数
```python
datasets = generate_datasets(
    num_dataset=100,
    h_config=h_config,
    custom_dag_type='random_ER',
    custom_dag_size='medium',        # 明确指定规模
    remove_isolated_nodes=False,     # 明确指定是否移除孤立节点
    seed=42,                         # 确保可重现
    max_retries=0,                   # 不重试，直接跳过失败
    allow_skip=True                  # 允许跳过
)
```

### 2. 确保特征数量正确
```python
h_config = {
    'drop_node_ratio': 0.0,  # 重要：设置为0确保特征数量正确
    # ... 其他配置
}
```

### 3. 验证生成结果
```python
for i, dataset in enumerate(datasets):
    scm = dataset[-1]
    node_count = len(scm.dag.nodes())
    feature_count = len(scm.selected_features)
    
    print(f"数据集{i+1}: 节点数={node_count}, 特征数={feature_count}")
    
    # 验证特征数量
    expected_features = node_count - 1
    assert feature_count == expected_features, f"特征数量不正确"
```

## 跳过机制工作流程

1. **尝试生成数据集**
2. **如果DAG有问题**：
   - 不重试DAG生成
   - 直接跳过当前数据集
   - 继续尝试下一个数据集
3. **循环直到成功生成指定数量的数据集**

## 向后兼容性

- 所有现有代码无需修改即可继续使用
- 新参数都有合理的默认值
- 保持原有的功能和行为

## 测试文件

- `test_isolated_nodes_fix.py`：测试孤立节点和size参数修复
- `test_medium_fix.py`：测试medium规模修复
- `usage_example_fixed.py`：正确使用示例

## 问题解决

✅ **"尝试生成第11个数据集"**：这是正常的，前10次失败被跳过，第11次成功
✅ **medium规模特征数19**：现在明确指定`custom_dag_size='medium'`会得到21-50个节点
✅ **特征数量不一致**：设置`drop_node_ratio=0.0`和`remove_isolated_nodes=False`确保一致性
✅ **删除重试逻辑**：DAG生成失败直接跳过，不再重试
