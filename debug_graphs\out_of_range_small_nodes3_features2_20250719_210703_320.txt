parents: ['U'] -> child: X
  f函数:
Linear
w=[[0.5364794135093689]]
b=[2.1350362300872803]
  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.7623924992559626
  === SNR验证结果 ===
  实际信号方差: 0.290621
  实际信号标准差: 0.539093
  实际噪声方差: 0.539083
  实际噪声标准差: 0.734223
  实际SNR: 0.5391029276970825
  目标SNR: 0.5

parents: ['U'] -> child: Y
  f函数:
Linear
w=[[-5.328620433807373]]
b=[-0.8128214478492737]
  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 2.394640612944066
  === SNR验证结果 ===
  实际信号方差: 28.671518
  实际信号标准差: 5.354579
  实际噪声方差: 5.353384
  实际噪声标准差: 2.313738
  实际SNR: 5.355774156284646
  目标SNR: 5.0