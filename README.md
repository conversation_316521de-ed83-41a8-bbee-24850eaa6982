# SCM模拟数据生成和表格模型因果能力评估框架

## 项目概述

基于结构化因果模型(SCM)的模拟数据生成和表格模型因果能力评估框架，支持多种DAG结构、自定义函数配置、SNR控制和机器学习模型评估。

### 核心特性

- 灵活的SCM建模：支持多种DAG结构和自定义函数配置
- 多样化数据生成：原始数据、干预数据、扰动数据生成
- 精确的SNR控制：基于蒙特卡洛方法的信噪比控制
- 多模型支持：集成多种机器学习模型进行性能比较
- 丰富的可视化：自动生成各种分析图表和统计报告
- 高性能计算：支持GPU加速和并行处理

## 文件结构

### 核心模块

#### `scm_model.py` - SCM核心实现
结构化因果模型的主要实现，包含：
- SCM模型定义和数据生成
- 干预和扰动实验支持
- SNR控制和蒙特卡洛计算
- 节点选择机制
- 种子管理系统

#### `scm_data_generator.py` - 数据集生成器
批量数据集生成工具，提供：
- 多种DAG结构生成（预定义结构、随机图）
- 种子管理和配置
- 数据保存和管理
- 并行处理支持

#### `function_generator.py` - 自定义函数生成器
函数配置系统，支持：
- 多种函数类型（线性、MLP、神经网络、高斯过程等）
- 手动参数指定或随机初始化
- SNR控制和噪声生成
- PyTorch实现

### 测试和评估

#### `scm_test.py` - 模型测试框架
模型评估系统，包含：
- 多种机器学习模型（OLS、Lasso、CatBoost、XGBoost、TabPFN）
- 并行处理和GPU加速
- 特征重要性分析
- 性能指标计算和比较

#### `model_weight_fusion.py` - 模型权重融合
TabPFN模型权重融合工具：
- 支持不同TabPFN变体的权重融合
- 路径管理和错误处理
- 模型定制和优化

### 工具库

#### `utils_scm.py` - SCM工具函数
图操作和分析工具：
- 节点关系分析（父子、兄弟、配偶关系）
- 图遍历和拓扑排序
- 节点和边分类系统
- 图可视化功能

#### `utils_plot.py` - 可视化工具
实验结果可视化：
- 性能比较图表（R²、特征重要性等）
- SNR分析图表
- 样式和颜色管理
- 多种图表类型支持

#### `utils_test.py` - 测试工具
数据处理和分析工具：
- 实验数据提取和处理
- 结果聚合和统计分析
- DAG结构管理和去重
- 数据验证和完整性检查

## 快速开始

### 环境要求

```bash
# 基础依赖
pip install torch numpy pandas networkx matplotlib seaborn
pip install scikit-learn xgboost catboost

# 可选依赖
pip install python-igraph  # 用于随机图生成
pip install GPy           # 用于高斯过程函数
pip install tabpfn        # 用于TabPFN模型
```

### 完整实验流程

参见scm_test.py


### 实验包含的基本步骤

#### 1. 生成简单数据集

```python
from scm_data_generator import generate_datasets

# 生成数据集
datasets = generate_datasets(
    num_dataset=10,
    h_config={
        'num_samples': 1000,
        'task': 'regression',
        'device': 'cpu'
    },
    custom_dag_type='common_cause',
    seed=42
)
```

#### 2. 自定义函数配置

```python
# 定义自定义函数
custom_functions = {
    'Y': {
        'type': 'linear',
        'coefficients': [2.0, 3.0],
        'bias': 1.0,
        'target_snr': 10
    },
    'X1': {
        'type': 'mlp',
        'hidden_dim': 64,
        'target_snr': 15
    }
}

datasets = generate_datasets(
    num_dataset=5,
    h_config=config,
    custom_functions=custom_functions,
    seed=42
)
```

#### 3. 运行模型评估

```python
from scm_test import process_single_config

# 运行模型评估
results = process_single_config(
    custom_function_config=custom_functions,
    config_name="test_config",
    base_config=base_config,
    num_datasets=10
)
```

## 高级功能

### SNR控制

框架支持精确的信噪比控制：

```python
# 基于目标SNR的噪声控制
function_config = {
    'type': 'linear',
    'target_snr': 20,  # 目标信噪比
    'use_monte_carlo_precompute': True,  # 启用蒙特卡洛预计算
    'monte_carlo_samples': 10000
}
```

### 干预和扰动实验

```python
# 生成干预数据
intervention_data = generate_datasets(
    num_dataset=5,
    h_config=config,
    intervention_type='counterfactual',
    intervention_node_type='all_parents',
    node_unobserved='intervention_nodes'
)

# 生成扰动数据
perturbation_data = generate_datasets(
    num_dataset=5,
    h_config=config,
    perturbation_type='statistical',
    perturbation_node_type='single_parent'
)
```

### 并行处理

```python
# 启用GPU并行处理
results = process_multiple_configs(
    custom_functions_configs=configs,
    base_config=config,
    num_datasets=20,
    use_multiprocessing=True,
    gpu_ids=[0, 1, 2, 3]  # 使用多个GPU
)
```

## 支持的模型

- **线性模型**：OLS、Lasso回归
- **树模型**：CatBoost、XGBoost、LightGBM
- **神经网络**：TabPFN（多个变体）

## 可视化功能

支持生成多种分析图表：
- R²性能比较图
- 特征重要性分析图
- SNR对比图表
- 模型性能分布图
- 节点关系图

## 实验配置

### DAG结构类型

- **预定义结构**：common_cause、chain、fork、collider等
- **随机图**：ER图、SF图（无标度网络）
- **自定义结构**：用户指定的边列表

### 函数类型

- `linear`：线性函数
- `mlp`：多层感知机
- `random_neural_network`：随机神经网络
- `gaussian_process`：高斯过程
- `fourier_transform`：傅里叶变换
- `piecewise_linear`：分段线性函数



## 详细功能说明

### 不可观测节点配置

框架支持灵活的不可观测节点配置：

```python
# 基本配置
node_unobserved = False  # 所有节点可观测
node_unobserved = True   # 干预节点不可观测

# 高级配置
node_unobserved = 'intervention_n_2'      # 随机选择2个干预节点不可观测
node_unobserved = 'intervention_ratio_0.5' # 50%的干预节点不可观测
node_unobserved = 'random_n_3'            # 随机选择3个节点不可观测
node_unobserved = 'parents'               # 目标节点的父节点不可观测
node_unobserved = 'markov_blanket'        # 马尔可夫毯节点不可观测
```

### 种子管理系统

随机种子管理确保实验可重现：

```python
# 全局种子配置
seed = 42

# 详细种子配置
seeds_config = {
    'dag_generation': [100, 200, 300],    # DAG生成种子
    'scm_model': [400, 500, 600],         # SCM模型种子
    'config_generation': [700, 800, 900], # 配置生成种子
    'data_sampling': [1000, 1100, 1200]   # 数据采样种子
}
```

### 蒙特卡洛配置

精确的SNR控制通过蒙特卡洛方法实现：

```python
monte_carlo_config = {
    'use_monte_carlo_precompute': True,
    'monte_carlo_samples': 10000,
    'monte_carlo_root_seed': 42,
    'monte_carlo_noise_seed': 123,
    'monte_carlo_resample': True,
    'monte_carlo_resample_size': 1000
}
```


## 结果分析

### 分析报告

生成以下分析内容：

1. **性能比较报告**
   - R²分数比较
   - MSE和MAPE指标
   - 模型排名分析

2. **特征重要性分析**
   - 排列重要性计算
   - 节点重要性排序
   - 重要性分布图

3. **SNR验证报告**
   - 目标vs实际SNR对比
   - 噪声标准差验证
   - 信号质量分析

4. **统计分析**
   - 相关性分析
   - 互信息计算
   - 分布统计



## 故障排除

### 常见问题

1. **GPU内存不足**
   ```python
   # 减少批次大小或使用CPU
   config['device'] = 'cpu'
   config['num_samples'] = 1000  # 减少样本数
   ```

2. **TabPFN模型加载失败**
   ```bash
   # 确保正确安装TabPFN
   pip install tabpfn
   # 或禁用TabPFN
   TABPFN_AVAILABLE = False
   ```

3. **随机图生成失败**
   # 安装igraph库 pygraphviz库

4. **高斯过程函数不可用**
   ```bash
   # 安装GPy库
   pip install GPy
   ```

### 性能优化建议

1. **使用GPU加速**
   ```python
   config['device'] = 'cuda'
   ```

2. **启用并行处理**
   ```python
   use_multiprocessing = True
   gpu_ids = [0, 1, 2, 3]  # 多GPU并行
   ```

3. **优化蒙特卡洛采样**
   ```python
   config['monte_carlo_samples'] = 5000  # 根据需要调整
   config['monte_carlo_resample_size'] = 500
   ```

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交Issue和Pull Request。

## 联系

如有问题或建议，请通过Issue联系。
