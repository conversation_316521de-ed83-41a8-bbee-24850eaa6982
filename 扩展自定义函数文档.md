# 自定义函数类型完整文档

## 概述

本文档描述了因果推断数据生成系统中所有可用的自定义函数类型。系统支持多种函数类型，从简单的线性函数到复杂的非线性函数，分为可逆函数和不可逆函数两大类，为不同的研究需求提供了丰富的选择。

## 函数分类

### 可逆函数 (Invertible Functions)
这些函数在其定义域内是单调的，因此是可逆的，适用于需要保持因果关系明确性的场景。

### 不可逆函数 (Non-Invertible Functions)
这些函数可能存在多对一的映射关系，更适合模拟复杂的现实世界关系。

## 所有可用函数类型

### 1. 线性函数 (Linear) - 可逆

**函数类型**: `linear`

**描述**: 基础线性函数，提供简单的线性映射关系。

**配置参数**:
- `coefficients` (list): 线性系数列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    1: {
        'type': 'linear',
        'coefficients': [2.0, -1.5],
        'bias': 0.5,
        'target_snr': 10.0
    }
}
```

**数学表示**:
```
y = a₁x₁ + a₂x₂ + ... + b
```

### 2. 多项式函数 (Polynomial) - 可逆

**函数类型**: `polynomial`

**描述**: 奇次幂多项式函数，使用奇次幂保证可逆性。

**配置参数**:
- `coefficients` (list): 系数列表
- `powers` (list): 幂次列表（自动确保为奇数）
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    2: {
        'type': 'polynomial',
        'coefficients': [1.5, 1.0],
        'powers': [3, 5],
        'bias': 0.1,
        'noise_std': 0.05
    }
}
```

**数学表示**:
```
y = a₁x₁^p₁ + a₂x₂^p₂ + ... + b (p₁, p₂, ... 为奇数)
```

### 3. 指数函数 (Exponential) - 可逆

**函数类型**: `exponential`

**描述**: 指数函数，提供快速增长的非线性关系。

**配置参数**:
- `coefficients` (list): 系数列表
- `exp_coefficients` (list): 指数系数列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    3: {
        'type': 'exponential',
        'coefficients': [1.0, 0.5],
        'exp_coefficients': [0.5, 0.3],
        'bias': 0.0,
        'target_snr': 8.0
    }
}
```

**数学表示**:
```
y = a₁exp(b₁x₁) + a₂exp(b₂x₂) + ... + c
```

### 4. 对数函数 (Logarithmic) - 可逆

**函数类型**: `logarithmic`

**描述**: 对数函数，提供缓慢增长的非线性关系。

**配置参数**:
- `coefficients` (list): 系数列表
- `log_coefficients` (list): 对数系数列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    4: {
        'type': 'logarithmic',
        'coefficients': [2.0, 1.0],
        'log_coefficients': [1.0, 0.5],
        'bias': 0.0,
        'noise_std': 0.1
    }
}
```

**数学表示**:
```
y = a₁log(b₁|x₁|+1) + a₂log(b₂|x₂|+1) + ... + c
```

### 5. 缩放Sigmoid函数 (Sigmoid Scaled) - 可逆

**函数类型**: `sigmoid_scaled`

**描述**: 缩放的sigmoid函数，提供S型曲线关系。

**配置参数**:
- `coefficients` (list): 系数列表
- `scale_coefficients` (list): 缩放系数列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    5: {
        'type': 'sigmoid_scaled',
        'coefficients': [2.0, 1.5],
        'scale_coefficients': [1.0, 0.8],
        'bias': 0.0,
        'target_snr': 12.0
    }
}
```

**数学表示**:
```
y = a₁sigmoid(b₁x₁) + a₂sigmoid(b₂x₂) + ... + c
```

### 6. 缩放Tanh函数 (Tanh Scaled) - 可逆

**函数类型**: `tanh_scaled`

**描述**: 缩放的tanh函数，提供双曲正切关系。

**配置参数**:
- `coefficients` (list): 系数列表
- `scale_coefficients` (list): 缩放系数列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    6: {
        'type': 'tanh_scaled',
        'coefficients': [1.0, 1.2],
        'scale_coefficients': [0.5, 0.7],
        'bias': 0.0,
        'noise_std': 0.08
    }
}
```

**数学表示**:
```
y = a₁tanh(b₁x₁) + a₂tanh(b₂x₂) + ... + c
```

## 不可逆函数类型

### 7. 偶次多项式函数 (Polynomial Even) - 不可逆

**函数类型**: `polynomial_even`

**描述**: 偶次幂多项式函数，使用偶次幂创建不可逆关系。

**配置参数**:
- `coefficients` (list): 系数列表
- `powers` (list): 幂次列表（自动确保为偶数）
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    7: {
        'type': 'polynomial_even',
        'coefficients': [1.0, 0.5],
        'powers': [2, 4],
        'bias': 0.0,
        'target_snr': 8.0
    }
}
```

**数学表示**:
```
y = a₁x₁^p₁ + a₂x₂^p₂ + ... + b (p₁, p₂, ... 为偶数)
```

### 8. 正弦函数 (Sine) - 不可逆

**函数类型**: `sine`

**描述**: 正弦函数，提供周期性振荡关系。

**配置参数**:
- `coefficients` (list): 系数列表
- `frequency_coefficients` (list): 频率系数列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    8: {
        'type': 'sine',
        'coefficients': [1.0, 0.8],
        'frequency_coefficients': [1.0, 2.0],
        'bias': 0.0,
        'noise_std': 0.1
    }
}
```

**数学表示**:
```
y = a₁sin(b₁x₁) + a₂sin(b₂x₂) + ... + c
```

### 9. 余弦函数 (Cosine) - 不可逆

**函数类型**: `cosine`

**描述**: 余弦函数，提供周期性振荡关系。

**配置参数**:
- `coefficients` (list): 系数列表
- `frequency_coefficients` (list): 频率系数列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    9: {
        'type': 'cosine',
        'coefficients': [1.2, 0.6],
        'frequency_coefficients': [0.5, 1.5],
        'bias': 0.0,
        'target_snr': 9.0
    }
}
```

**数学表示**:
```
y = a₁cos(b₁x₁) + a₂cos(b₂x₂) + ... + c
```

### 10. ReLU二次组合函数 (ReLU Quadratic) - 不可逆

**函数类型**: `relu_quadratic`

**描述**: ReLU函数与二次项的组合，提供分段非线性关系。

**配置参数**:
- `relu_coefficients` (list): ReLU系数列表
- `quad_coefficients` (list): 二次项系数列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    10: {
        'type': 'relu_quadratic',
        'relu_coefficients': [1.0, 0.8],
        'quad_coefficients': [0.5, 0.3],
        'bias': 0.0,
        'noise_std': 0.12
    }
}
```

**数学表示**:
```
y = a₁ReLU(x₁) + b₁x₁² + a₂ReLU(x₂) + b₂x₂² + ... + c
```

### 11. 高斯径向基函数 (Gaussian RBF) - 不可逆

**函数类型**: `gaussian_rbf`

**描述**: 高斯径向基函数，提供局部化的非线性关系。

**配置参数**:
- `coefficients` (list): 系数列表
- `width_coefficients` (list): 宽度系数列表
- `centers` (list): 中心点列表
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    11: {
        'type': 'gaussian_rbf',
        'coefficients': [1.0, 0.8],
        'width_coefficients': [1.0, 1.5],
        'centers': [0.0, 0.5],
        'bias': 0.0,
        'target_snr': 7.0
    }
}
```

**数学表示**:
```
y = a₁exp(-b₁(x₁-c₁)²) + a₂exp(-b₂(x₂-c₂)²) + ... + d
```

### 12. 高斯过程函数 (Gaussian Process) - 不可逆

**函数类型**: `gaussian_process`

**描述**: 使用GPy库生成高斯过程样本，提供非线性、平滑的随机函数。

**依赖**: 需要安装GPy库 (`pip install GPy`)

**配置参数**:
- `lengthscale` (float, 默认: 1.0): RBF核的长度尺度参数
- `f_magn` (float, 默认: 3.0): 方差参数，控制函数的幅度
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    12: {
        'type': 'gaussian_process',
        'lengthscale': 1.5,
        'f_magn': 2.0,
        'target_snr': 8.0
    }
}
```

**数学表示**:
```
y ~ GP(0, k(x,x'))
其中 k(x,x') = σ² * exp(-||x-x'||²/(2l²))
```

### 13. 傅里叶级数函数 (Fourier Series) - 不可逆

**函数类型**: `fourier_series`

**描述**: 实现随机傅里叶级数，提供周期性和振荡特性的函数。

**配置参数**:
- `n_terms` (int, 默认: 5): 傅里叶级数的项数
- `a_coeffs` (list, 可选): 余弦项系数，如未指定则随机生成
- `b_coeffs` (list, 可选): 正弦项系数，如未指定则随机生成
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    13: {
        'type': 'fourier_series',
        'n_terms': 3,
        'a_coeffs': [1.0, 0.5, 0.2],
        'b_coeffs': [0.8, 0.3, 0.1],
        'bias': 0.0,
        'target_snr': 10.0
    }
}
```

**数学表示**:
```
y = Σ(i=1 to n) [a_i * cos(i*x) + b_i * sin(i*x)] + bias
```

### 14. 分段线性函数 (Piecewise Linear) - 不可逆

**函数类型**: `piecewise_linear`

**描述**: 基于线性插值的分段函数，提供非平滑的分段线性关系。

**配置参数**:
- `x_points` (int, 默认: 10): 分段点的数量
- `x_range` (list, 默认: [-1, 1]): 输入范围 [min, max]
- `y_values` (list, 可选): 各分段点的y值，如未指定则随机生成
- `bias` (float, 默认: 0.0): 偏置项
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    14: {
        'type': 'piecewise_linear',
        'x_points': 8,
        'x_range': [-2, 2],
        'y_values': [0.1, 0.5, 1.2, 0.8, -0.3, 0.7, 1.1, 0.4],
        'bias': 0.0,
        'noise_std': 0.15
    }
}
```

**数学表示**:
```
y = interp(x, x_points, y_values) + bias
其中 interp 为线性插值函数
```

### 15. 随机神经网络函数 (Random Neural Network) - 不可逆

**函数类型**: `random_neural_network`

**描述**: 使用随机初始化的小型神经网络，提供复杂的非线性映射关系。

**配置参数**:
- `hidden_dim` (int, 默认: 10): 隐藏层维度
- `depth` (int, 默认: 2): 网络深度（隐藏层数量）
- `activation` (str, 默认: 'tanh'): 激活函数类型
  - 可选值: 'tanh', 'relu', 'sigmoid'
- `target_snr` (float, 可选): 目标信噪比
- `noise_std` (float, 可选): 固定噪声标准差

**使用示例**:
```python
custom_functions = {
    15: {
        'type': 'random_neural_network',
        'hidden_dim': 16,
        'depth': 3,
        'activation': 'tanh',
        'target_snr': 12.0
    }
}
```

**网络结构**:
```
Input -> Linear(input_dim, hidden_dim) -> Activation
      -> [Linear(hidden_dim, hidden_dim) -> Activation] × (depth-1)
      -> Linear(hidden_dim, 1) -> Output
```

## 技术特性

### 1. 完全集成
- 所有新函数类型都完全集成到现有的 `_setup_custom_function` 方法中
- 与现有的线性、多项式、指数等函数类型无缝兼容
- 支持现有的所有配置选项和功能

### 2. 参数化配置
- 每种函数类型都提供丰富的参数配置选项
- 支持自定义参数或随机生成
- 灵活的配置方式适应不同的实验需求

### 3. 随机种子支持
- 所有函数类型都支持随机种子设置
- 确保实验的可重现性
- 与现有的种子管理系统兼容

### 4. SNR支持
- 支持目标信噪比 (`target_snr`) 配置
- 支持固定噪声标准差 (`noise_std`) 配置
- 与Monte Carlo预计算功能完全兼容

### 5. 向后兼容
- 不影响现有的函数类型和配置
- 现有代码无需修改即可继续使用
- 新旧功能可以混合使用

## 使用指南

### 基本使用

1. **在配置中指定函数类型**:
```python
custom_functions = {
    node_id: {
        'type': 'function_type_name',
        # 其他参数...
    }
}
```

2. **与数据生成集成**:
```python
datasets = generate_datasets(
    num_dataset=1,
    h_config=h_config,
    custom_functions=custom_functions,
    seed=42
)
```

### 高级配置示例

```python
# 混合使用多种函数类型的完整示例
custom_functions = {
    # 可逆函数
    1: {
        'type': 'linear',
        'coefficients': [2.0, -1.5],
        'bias': 0.5,
        'target_snr': 10.0
    },
    2: {
        'type': 'polynomial',
        'coefficients': [1.5, 1.0],
        'powers': [3, 5],
        'bias': 0.1,
        'noise_std': 0.05
    },
    3: {
        'type': 'exponential',
        'coefficients': [1.0, 0.5],
        'exp_coefficients': [0.5, 0.3],
        'bias': 0.0,
        'target_snr': 8.0
    },
    4: {
        'type': 'sigmoid_scaled',
        'coefficients': [2.0, 1.5],
        'scale_coefficients': [1.0, 0.8],
        'bias': 0.0,
        'target_snr': 12.0
    },

    # 不可逆函数
    5: {
        'type': 'sine',
        'coefficients': [1.0, 0.8],
        'frequency_coefficients': [1.0, 2.0],
        'bias': 0.0,
        'noise_std': 0.1
    },
    6: {
        'type': 'fourier_series',
        'n_terms': 5,
        'target_snr': 8.0
    },
    7: {
        'type': 'gaussian_rbf',
        'coefficients': [1.0, 0.8],
        'width_coefficients': [1.0, 1.5],
        'centers': [0.0, 0.5],
        'bias': 0.0,
        'target_snr': 7.0
    },
    8: {
        'type': 'random_neural_network',
        'hidden_dim': 20,
        'depth': 2,
        'activation': 'relu',
        'target_snr': 15.0
    }
}
```

### 按应用场景分类的配置示例

#### 1. 因果发现研究配置
```python
# 强调可逆性和明确因果关系
causal_discovery_functions = {
    1: {'type': 'linear', 'coefficients': [2.0], 'target_snr': 10.0},
    2: {'type': 'polynomial', 'coefficients': [1.5], 'powers': [3], 'target_snr': 8.0},
    3: {'type': 'exponential', 'coefficients': [1.0], 'exp_coefficients': [0.5], 'target_snr': 9.0}
}
```

#### 2. 复杂非线性关系建模
```python
# 使用不可逆函数模拟复杂现实关系
complex_modeling_functions = {
    1: {'type': 'gaussian_process', 'lengthscale': 1.5, 'f_magn': 2.0, 'target_snr': 8.0},
    2: {'type': 'fourier_series', 'n_terms': 7, 'target_snr': 10.0},
    3: {'type': 'random_neural_network', 'hidden_dim': 32, 'depth': 3, 'target_snr': 12.0}
}
```

#### 3. 周期性数据模拟
```python
# 专注于周期性和振荡关系
periodic_functions = {
    1: {'type': 'sine', 'coefficients': [1.0], 'frequency_coefficients': [2.0], 'target_snr': 8.0},
    2: {'type': 'cosine', 'coefficients': [0.8], 'frequency_coefficients': [1.5], 'target_snr': 9.0},
    3: {'type': 'fourier_series', 'n_terms': 5, 'target_snr': 10.0}
}
```

## 测试验证

系统包含完整的测试脚本 `test_extended_functions.py`，验证所有新函数类型的功能：

```bash
python test_extended_functions.py
```

## 函数类型总结表

| 编号 | 函数类型 | 英文名称 | 可逆性 | 复杂度 | 主要用途 |
|-----|---------|---------|--------|--------|---------|
| 1 | 线性函数 | linear | 可逆 | 低 | 基础线性关系 |
| 2 | 多项式函数 | polynomial | 可逆 | 低 | 单调非线性关系 |
| 3 | 指数函数 | exponential | 可逆 | 低 | 快速增长关系 |
| 4 | 对数函数 | logarithmic | 可逆 | 低 | 缓慢增长关系 |
| 5 | 缩放Sigmoid | sigmoid_scaled | 可逆 | 低 | S型曲线关系 |
| 6 | 缩放Tanh | tanh_scaled | 可逆 | 低 | 双曲正切关系 |
| 7 | 偶次多项式 | polynomial_even | 不可逆 | 低 | 对称非线性关系 |
| 8 | 正弦函数 | sine | 不可逆 | 低 | 周期性振荡 |
| 9 | 余弦函数 | cosine | 不可逆 | 低 | 周期性振荡 |
| 10 | ReLU二次组合 | relu_quadratic | 不可逆 | 低 | 分段非线性 |
| 11 | 高斯径向基 | gaussian_rbf | 不可逆 | 低 | 局部化关系 |
| 12 | 高斯过程 | gaussian_process | 不可逆 | 高 | 平滑随机函数 |
| 13 | 傅里叶级数 | fourier_series | 不可逆 | 低 | 复杂周期性 |
| 14 | 分段线性 | piecewise_linear | 不可逆 | 低 | 分段关系建模 |
| 15 | 随机神经网络 | random_neural_network | 不可逆 | 中等 | 复杂非线性关系 |

**测试结果**:
- ✅ 所有基础函数类型: 通过
- ✅ fourier_series: 通过
- ✅ piecewise_linear: 通过
- ✅ random_neural_network: 通过
- ⚠️ gaussian_process: 需要安装GPy库

## 安装要求

### 基本要求
- PyTorch
- NumPy
- NetworkX

### 可选要求
- GPy (用于高斯过程函数): `pip install GPy`

## 注意事项

1. **高斯过程函数**: 需要额外安装GPy库，如果未安装会显示警告并跳过
2. **计算复杂度**: 神经网络函数和高斯过程函数计算复杂度较高，建议合理设置参数
3. **内存使用**: 大型网络或大量采样点可能消耗较多内存
4. **数值稳定性**: 某些参数组合可能导致数值不稳定，建议进行测试验证

## 扩展指南

如需添加新的函数类型，请参考现有实现：

1. 在 `_setup_custom_function` 方法中添加新的 `elif` 分支
2. 实现对应的 PyTorch Module 类
3. 添加参数验证和配置处理
4. 更新测试脚本进行验证

## 实现细节

### 代码结构

扩展函数的实现位于 `multi_scm_model.py` 文件中的 `_setup_custom_function` 方法内：

```python
def _setup_custom_function(self, device, init_std):
    """设置自定义函数"""
    function_type = self.custom_function_config.get('type', 'linear')

    if function_type == 'gaussian_process':
        # 高斯过程实现
        ...
    elif function_type == 'fourier_series':
        # 傅里叶级数实现
        ...
    elif function_type == 'piecewise_linear':
        # 分段线性实现
        ...
    elif function_type == 'random_neural_network':
        # 随机神经网络实现
        ...
```

### 与Monte Carlo预计算的兼容性

所有扩展函数类型都完全支持Monte Carlo预计算功能：

1. **信号方差估计**: 通过大样本Monte Carlo采样估计信号方差
2. **噪声标准差计算**: 基于目标SNR和估计的信号方差计算噪声标准差
3. **缓存机制**: 相同配置下的多个数据集共享预计算结果
4. **一致性保证**: 确保同一配置下的数据集具有相同的噪声参数

### 性能考虑

| 函数类型 | 计算复杂度 | 内存使用 | 推荐场景 | 可逆性 |
|---------|-----------|---------|---------|--------|
| linear | O(n) | 低 | 基础线性关系 | 可逆 |
| polynomial | O(n) | 低 | 单调非线性关系 | 可逆 |
| exponential | O(n) | 低 | 快速增长关系 | 可逆 |
| logarithmic | O(n) | 低 | 缓慢增长关系 | 可逆 |
| sigmoid_scaled | O(n) | 低 | S型曲线关系 | 可逆 |
| tanh_scaled | O(n) | 低 | 双曲正切关系 | 可逆 |
| polynomial_even | O(n) | 低 | 对称非线性关系 | 不可逆 |
| sine | O(n) | 低 | 周期性振荡 | 不可逆 |
| cosine | O(n) | 低 | 周期性振荡 | 不可逆 |
| relu_quadratic | O(n) | 低 | 分段非线性 | 不可逆 |
| gaussian_rbf | O(n) | 低 | 局部化关系 | 不可逆 |
| fourier_series | O(n_terms) | 低 | 复杂周期性 | 不可逆 |
| piecewise_linear | O(x_points) | 低 | 分段关系建模 | 不可逆 |
| random_neural_network | O(hidden_dim × depth) | 中等 | 复杂非线性关系 | 不可逆 |
| gaussian_process | O(n_samples²) | 高 | 平滑随机函数 | 不可逆 |

## 应用场景

### 1. 因果发现研究
- 使用不同函数类型模拟真实世界的复杂因果关系
- 测试因果发现算法在不同函数形式下的性能
- 评估算法对非线性关系的敏感性

### 2. 机器学习基准测试
- 生成具有已知因果结构的合成数据集
- 评估预测模型在不同函数复杂度下的表现
- 测试特征重要性分析方法

### 3. 统计方法验证
- 验证统计推断方法的有效性
- 测试假设检验在不同数据分布下的功效
- 评估置信区间的覆盖率

### 4. 教学和演示
- 展示不同类型的函数关系
- 教学中的数据生成和可视化
- 概念验证和原型开发

## 故障排除

### 常见问题

1. **GPy导入错误**
   ```
   错误: 高斯过程函数需要安装GPy库
   解决: pip install GPy
   ```

2. **内存不足**
   ```
   问题: 高斯过程或大型神经网络消耗过多内存
   解决: 减少n_samples或hidden_dim参数
   ```

3. **数值不稳定**
   ```
   问题: 某些参数组合导致NaN或Inf
   解决: 调整函数参数范围，检查输入数据范围
   ```

4. **性能问题**
   ```
   问题: 数据生成速度过慢
   解决: 减少复杂度参数，使用更简单的函数类型
   ```

### 调试技巧

1. **启用详细日志**: 在配置中设置详细输出
2. **分步测试**: 先测试单个函数类型
3. **参数验证**: 检查所有配置参数的合理性
4. **小规模测试**: 使用较小的参数进行初始测试

## 最佳实践

### 1. 参数选择指南

#### 可逆函数参数建议
- **linear**: coefficients根据期望的线性关系强度设置
- **polynomial**: powers使用奇数(3, 5, 7)，coefficients避免过大值
- **exponential**: exp_coefficients建议在0.1-1.0之间避免数值溢出
- **logarithmic**: log_coefficients建议在0.5-2.0之间
- **sigmoid_scaled/tanh_scaled**: scale_coefficients建议在0.5-2.0之间

#### 不可逆函数参数建议
- **polynomial_even**: powers使用偶数(2, 4, 6)
- **sine/cosine**: frequency_coefficients建议在0.5-3.0之间
- **fourier_series**: n_terms建议在3-10之间
- **piecewise_linear**: x_points建议在5-20之间
- **random_neural_network**: hidden_dim建议在8-64之间
- **gaussian_process**: n_samples建议在50-200之间
- **gaussian_rbf**: width_coefficients建议在0.5-2.0之间

### 2. 性能优化
- 对于大规模实验，优先使用计算复杂度较低的函数类型
- 合理设置Monte Carlo采样数量平衡精度和速度
- 使用GPU加速神经网络函数计算

### 3. 实验设计
- 混合使用不同函数类型增加数据多样性
- 根据研究目标选择合适的函数复杂度
- 保持配置的可重现性和文档化

## 更新日志

- **v1.0** (2024): 初始实现四种扩展函数类型
- 完成与Monte Carlo预计算的集成
- 通过全面测试验证
- 添加完整的文档和使用指南
