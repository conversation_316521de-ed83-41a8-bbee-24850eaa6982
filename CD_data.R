# 创建R脚本来获取bnlearn中的所有因果数据集
# 安装和加载必要的包
if (!require(bnlearn)) {
    install.packages("bnlearn")
    library(bnlearn)
}

# 获取bnlearn中所有可用的数据集
available_datasets <- data(package = "bnlearn")$results[, "Item"]
print("Available datasets in bnlearn:")
print(available_datasets)

# 创建输出目录
dir.create("bnlearn_datasets", showWarnings = FALSE)
dir.create("bnlearn_datasets/csv", showWarnings = FALSE)
dir.create("bnlearn_datasets/adjacency_matrices", showWarnings = FALSE)

# 定义已知的数据集和对应的网络结构
known_datasets <- list(
    "asia" = list(data = "asia", network = "asia"),
    "learning.test" = list(data = "learning.test", network = "learning.test"),
    "gaussian.test" = list(data = "gaussian.test", network = "gaussian.test"),
    "insurance" = list(data = "insurance", network = "insurance"),
    "hailfinder" = list(data = "hailfinder", network = "hailfinder"),
    "marks" = list(data = "marks", network = "marks"),
    "alarm" = list(data = "alarm", network = "alarm")
)

# 处理每个数据集
results_summary <- data.frame(
    dataset = character(),
    nodes = integer(),
    edges = integer(),
    data_rows = integer(),
    data_cols = integer(),
    stringsAsFactors = FALSE
)

for (dataset_name in names(known_datasets)) {
    cat("\\nProcessing dataset:", dataset_name, "\\n")
    
    tryCatch({
        # 加载数据
        data(list = known_datasets[[dataset_name]]$data, package = "bnlearn")
        dataset <- get(known_datasets[[dataset_name]]$data)
        
        # 加载网络结构
        data(list = known_datasets[[dataset_name]]$network, package = "bnlearn")
        network <- get(known_datasets[[dataset_name]]$network)
        
        # 保存CSV文件
        csv_filename <- paste0("bnlearn_datasets/csv/", dataset_name, ".csv")
        write.csv(dataset, csv_filename, row.names = FALSE)
        cat("Saved CSV:", csv_filename, "\\n")
        
        # 获取邻接矩阵
        adj_matrix <- amat(network)
        
        # 保存邻接矩阵
        adj_filename <- paste0("bnlearn_datasets/adjacency_matrices/", dataset_name, "_adjacency.csv")
        write.csv(adj_matrix, adj_filename, row.names = TRUE)
        cat("Saved adjacency matrix:", adj_filename, "\\n")
        
        # 记录统计信息
        results_summary <- rbind(results_summary, data.frame(
            dataset = dataset_name,
            nodes = nnodes(network),
            edges = narcs(network),
            data_rows = nrow(dataset),
            data_cols = ncol(dataset)
        ))
        
        cat("Dataset info - Nodes:", nnodes(network), "Edges:", narcs(network), 
            "Data shape:", nrow(dataset), "x", ncol(dataset), "\\n")
            
    }, error = function(e) {
        cat("Error processing", dataset_name, ":", e$message, "\\n")
    })
}

# 保存汇总信息
write.csv(results_summary, "bnlearn_datasets/datasets_summary.csv", row.names = FALSE)

cat("\\n=== SUMMARY ===\\n")
print(results_summary)

cat("\\nAll files have been saved to the 'bnlearn_datasets' directory\\n")
cat("- CSV data files: bnlearn_datasets/csv/\\n")
cat("- Adjacency matrices: bnlearn_datasets/adjacency_matrices/\\n")
cat("- Summary file: bnlearn_datasets/datasets_summary.csv\\n")

