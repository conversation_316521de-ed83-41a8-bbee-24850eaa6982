import networkx as nx
import random
import numpy as np
import scipy.stats as stats
import matplotlib.pyplot as plt
import torch
import copy
import pandas as pd
import torch.nn as nn
# from utils import normalize_data, nan_handling_missing_for_unknown_reason_value, nan_handling_missing_for_no_reason_value, nan_handling_missing_for_a_reason_value, to_ranking_low_mem, remove_outliers, normalize_by_used_features_f


# Util functions
# 定义截断正态分布采样器函数，参数 mu 为均值，sigma 为标准差
trunc_norm_sampler_f = lambda mu, sigma : lambda: stats.truncnorm((0 - mu) / sigma, (1000000 - mu) / sigma, loc=mu, scale=sigma).rvs(1)[0]
# 定义 Beta 分布采样器函数，参数 a 和 b 为 Beta 分布的形状参数
beta_sampler_f = lambda a, b : lambda : np.random.beta(a, b)
# 定义 Gamma 分布采样器函数，参数 a 和 b 为 Gamma 分布的形状参数和尺度参数
gamma_sampler_f = lambda a, b : lambda : np.random.gamma(a, b)
# 定义均匀分布采样器函数，参数 a 和 b 为均匀分布的下界和上界
uniform_sampler_f = lambda a, b : lambda : np.random.uniform(a, b)
# 定义均匀整数分布采样器函数，参数 a 和 b 为均匀分布的下界和上界，结果会四舍五入为整数
uniform_int_sampler_f = lambda a, b : lambda : round(np.random.uniform(a, b))

# 定义一个函数，用于采样原因节点的取值范围
def causes_sampler_f(num_causes, min_lb, max_lb, max_len):
    # 断言确保 max_lb 大于 min_lb
    assert(max_lb-min_lb>0)
    # 断言确保 max_lb 加上 max_len 不超过 1
    assert(max_lb+max_len<=1)
    # 从 [min_lb, max_lb) 均匀采样 num_causes 个下界值
    lb = np.random.uniform(low=min_lb, high=max_lb, size=num_causes)
    # 在下界的基础上，从 [0, max_len) 均匀采样长度，并加到下界上得到上界
    ub = lb + np.random.uniform(low=0.0, high=max_len, size=num_causes)
    # 返回下界和上界数组
    return lb, ub
            
# 定义绘制图形并保存的函数
def draw_graph(G, path):
    # 创建一个图形窗口，设置大小为 12x9 英寸（更大更清晰）
    plt.figure(figsize=(12, 9))
    # 使用 graphviz 的 "dot" 布局算法计算节点位置
    pos = nx.nx_agraph.graphviz_layout(G, prog="dot")

    # 根据节点数量自适应调整字体大小和节点大小
    num_nodes = len(G.nodes())
    if num_nodes <= 10:
        font_size = 12
        node_size = 500
    elif num_nodes <= 20:
        font_size = 10
        node_size = 400
    elif num_nodes <= 30:
        font_size = 8
        node_size = 300
    elif num_nodes <= 50:
        font_size = 6
        node_size = 200
    else:
        font_size = 4
        node_size = 150

    # 绘制图形 G，设置节点标签、大小、颜色，边的颜色和箭头样式
    nx.draw(G, pos, with_labels=True, node_size=node_size, node_color='lightblue',
            edge_color='gray', arrows=True, font_size=font_size)
    # 将绘制的图形保存到指定路径
    # plt.savefig(path)
    # 显示图形
    plt.show()

# 定义一个类别采样器函数，用于生成类别数量
def class_sampler_f(min_: int, max_: int):
    # 定义内部采样函数 s
    def s():
        # 以 0.5 的概率决定采样方式
        if random.random() > 0.5:
            # 如果随机数大于 0.5，则从 [min_, max_] 均匀采样一个整数作为类别数量
            return uniform_int_sampler_f(min_, max_)()
        # 否则，返回固定的类别数量 2
        return 2
    # 返回内部采样函数 s
    return s

# 定义一个函数，用于随机化类别标签
def randomize_classes(x, num_classes):
    # 创建一个从 0 到 num_classes-1 的类别张量，设备与 x 相同
    classes = torch.arange(0, num_classes, device=x.device)
    # 创建一个 num_classes 的随机排列，作为新的类别标签，类型与 x 相同
    random_classes = torch.randperm(num_classes, device=x.device).type(x.type())
    # 将 x 的最后一维扩展，并与原始类别比较，然后乘以新的随机类别，最后求和得到新的类别标签
    x = ((x.unsqueeze(-1) == classes) * random_classes).sum(-1)
    # 返回随机化后的类别标签
    return x

## Find specific nodes in a graph
# 定义一个函数，获取图中一个节点的所有后代节点
def get_all_descendants(graph, node):
    # 初始化已访问节点集合
    visited = set()
    # 初始化栈，并将起始节点加入栈中
    stack = [node]

    # 初始化后代节点集合
    descendants = set()

    # 当栈不为空时循环
    while stack:
        # 弹出栈顶节点作为当前节点
        current = stack.pop()
        # 遍历当前节点的直接后继（子节点）
        for child in graph.successors(current):
            # 如果子节点未被访问过
            if child not in visited:
                # 将子节点标记为已访问
                visited.add(child)
                # 将子节点添加到后代节点集合中
                descendants.add(child)
                # 将子节点压入栈中，继续查找其后代
                stack.append(child)
    # 返回所有后代节点集合
    return descendants

# 定义一个函数，获取图中一个节点的所有祖先节点
def get_all_antecedents(graph, node):
    # 初始化已访问节点集合
    visited = set()
    # 初始化栈，并将起始节点加入栈中
    stack = [node]

    # 初始化祖先节点集合
    antecedents = set()

    # 当栈不为空时循环
    while stack:
        # 弹出栈顶节点作为当前节点
        current = stack.pop()
        # 遍历当前节点的直接前驱（父节点）
        for parent in graph.predecessors(current):
            # 如果父节点未被访问过
            if parent not in visited:
                # 将父节点标记为已访问
                visited.add(parent)
                # 将父节点添加到祖先节点集合中
                antecedents.add(parent)
                # 将父节点压入栈中，继续查找其祖先
                stack.append(parent)
    # 返回所有祖先节点集合
    return antecedents

# 定义一个函数，获取图中一个节点的父节点集合
def get_parents(graph, node):
    # 返回节点的所有直接前驱（父节点）的集合
    return set(graph.predecessors(node))

# 定义一个函数，获取图中一个节点的祖父节点集合
def get_grandparents(graph, node):
    # 初始化祖父节点集合
    grandparents = set()
    # 遍历节点的每个父节点
    for parent in graph.predecessors(node):
        # 遍历父节点的每个父节点（即祖父节点）
        for grandparent in graph.predecessors(parent):
            # 将祖父节点添加到集合中
            grandparents.add(grandparent)
    # 返回祖父节点集合
    return grandparents

# 定义一个函数，获取图中一个节点的兄弟节点集合
def get_siblings(graph, node):
    # 初始化兄弟节点集合
    siblings = set()
    # 遍历节点的每个父节点
    for parent in graph.predecessors(node):
        # 遍历父节点的每个子节点
        for child in graph.successors(parent):
            # 如果子节点不是节点本身，则为兄弟节点
            if child != node:
                # 将兄弟节点添加到集合中
                siblings.add(child)
    # 返回兄弟节点集合
    return siblings

# 定义一个函数，用于生成结构化配置
def config_generation(h_config):
    # 初始化一个空列表，用于存储每层的节点数
    n_nodes_list = []
    # 创建一个节点数量采样器，从 h_config 中指定的最小和最大节点数之间均匀采样整数
    num_node_sampler = uniform_int_sampler_f(h_config['min_num_node'], h_config['max_num_node'])
    # 循环生成除最后一层外的每层节点数
    for i in range(h_config['num_layers']-1):
        n_nodes_list.append(num_node_sampler())
    # 根据 h_config 中的配置决定最后一层的节点数
    if h_config['single_effect_last_layer']:
        # 如果最后一层是单一效应节点，则节点数为 1
        n_nodes_list.append(1)
    elif h_config['last_layer_fix_num_node']:
        # 如果最后一层节点数固定，则使用 h_config 中指定的数量
        n_nodes_list.append(h_config['num_node_last_layer'])
    else:
        # 否则，使用节点数量采样器生成最后一层的节点数
        n_nodes_list.append(num_node_sampler())

    # 为每层（除最后一层外）创建一个子节点数量采样器列表
    # 每个采样器从 1 到 h_config['max_num_children'] 和下一层节点数中的较小值之间均匀采样整数
    num_children_samplers = [uniform_int_sampler_f(1, min(h_config['max_num_children'], n_nodes_list[i+1])) for i in range(len(n_nodes_list)-1)]

    # 从 h_config 中指定的最小和最大噪声标准差之间均匀采样一个噪声标准差
    noise_std = uniform_sampler_f(h_config['min_noise_std'], h_config['max_noise_std'])()
    
    # 从 h_config 中指定的最小和最大 MLP 初始化标准差之间均匀采样一个初始化标准差
    init_std = uniform_sampler_f(h_config['min_init_std'], h_config['max_init_std'])()

    # 从 h_config 中指定的最小和最大输出多类有序概率之间均匀采样一个概率值
    output_multiclass_ordered_p = uniform_sampler_f(h_config['min_output_multiclass_ordered_p'], h_config['max_output_multiclass_ordered_p'])()
    
    # 从 h_config 中指定的最小和最大样本数量之间均匀采样一个整数作为样本数量
    num_samples = uniform_int_sampler_f(h_config['min_num_samples'], h_config['max_num_samples'])()

    # 从 h_config 中指定的最小和最大节点丢弃比例之间均匀采样一个丢弃比例
    drop_node_ratio = uniform_sampler_f(h_config['min_drop_node_ratio'], h_config['max_drop_node_ratio'])()

    # 从 2 到 h_config 中指定的最大类别数之间均匀采样一个整数作为分类任务的目标类别数
    num_classes = uniform_int_sampler_f(2, h_config['max_num_classes'])()

    # 创建配置字典，存储所有采样得到的参数和 h_config 中的一些固定参数
    config={
        'device':h_config['device'], # 设备（如 'cpu' 或 'cuda'）
        'n_nodes_list':n_nodes_list, # 每层的节点数列表
        'noise_std':noise_std, # 噪声标准差
        'init_std':init_std, # MLP 初始化标准差
        'num_children_samplers': num_children_samplers, # 子节点数量采样器列表
        'min_root':h_config['min_root'], # 根节点初始化范围的最小值
        'max_root':h_config['max_root'], # 根节点初始化范围的最大值
        'max_range':h_config['max_range'], # 根节点初始化范围的最大长度
        'num_classes':num_classes, # 分类任务的目标类别数
        'output_multiclass_ordered_p':output_multiclass_ordered_p, # 输出多类有序的概率
        'num_samples':num_samples, # 样本数量
        'task':h_config['task'], # 任务类型（如 'classification' 或 'regression'）
        'sample_std':h_config['sample_std'], # 是否对标准差进行采样
        'categorical_feature_p':h_config['categorical_feature_p'], # 生成类别特征的概率
        'drop_node_ratio':drop_node_ratio, # 节点丢弃比例
        'intervention_on_no_root':h_config['intervention_on_no_root'], # 是否在非根节点上进行干预
        'intervention_on_observed':h_config['intervention_on_observed'], # 是否在观测到的节点上进行干预
        'sample_cause_ranges':h_config['sample_cause_ranges'] # 是否采样原因节点的范围
    }

    # 返回生成的配置字典
    return config

# Generate tree-based DAG
# 定义一个函数，用于生成基于树的有向无环图 (DAG)
def generate_dag(n_nodes_list, num_children_samplers):
    # 创建一个空的有向图对象
    G = nx.DiGraph()
    # 获取层数
    num_layer = len(n_nodes_list)
    # 初始化一个空列表，用于存储每层的节点
    layer_nodes = []
    # 初始化节点索引
    node_idx = 0

    # 为每一层生成节点
    for i in range(num_layer):
        # 生成当前层的节点列表，节点编号从 node_idx 开始
        node_list = np.arange(node_idx, node_idx+n_nodes_list[i])
        # 将当前层的节点添加到图中
        G.add_nodes_from(node_list)
        # 将当前层的节点列表添加到 layer_nodes 中
        layer_nodes.append(node_list)
        # 更新节点索引，为下一层做准备
        node_idx += n_nodes_list[i]

    # 为每层（除最后一层外）的节点添加边
    for i in range(num_layer-1):
        # 使用对应的子节点数量采样器获取当前层节点应有的子节点数量
        num_children = num_children_samplers[i]()
        # 遍历当前层的每个节点
        for node in layer_nodes[i]:
            # 从下一层节点中随机选择 num_children 个不重复的节点作为子节点
            children = np.random.choice(layer_nodes[i+1], size=num_children, replace=False)
            # 添加从当前节点到其子节点的边
            G.add_edges_from([(node, child) for child in children])

    # 识别图中的根节点（入度为 0 的节点）
    root_nodes = [node for node in G.nodes if G.in_degree(node) == 0]
                                  
    # 返回生成的 DAG、根节点列表和每层节点列表
    return G, root_nodes, layer_nodes

# Generate assignment functions
# 定义一个函数，用于为 DAG 中的非根节点生成赋值函数
def generate_assignment(G, noise_std, init_std, device, sample_std=False):
    # 定义一个内部类 AssingmentGenerator，继承自 nn.Module，用于生成赋值函数
    class AssingmentGenerator(nn.Module):
        def __init__(self, in_dim, out_dim, noise_std, init_std, device):
            '''
            参数:
                in_dim: 输入维度 (父节点数量)
                out_dim: 输出维度 (通常为 1)
                noise_std: 噪声的标准差
                init_std: MLP 参数初始化的标准差
                device: 计算设备
            '''
            super(AssingmentGenerator, self).__init__()
            # 定义可选的激活函数
            activation_choices = {
            "identity": nn.Identity(), # 恒等函数
            "sigmoid": nn.Hardsigmoid(), # Sigmoid 函数 (使用 Hardsigmoid)
            "relu": nn.ReLU(), # ReLU 函数
            "tanh": nn.Tanh(), # Tanh 函数
            "softplus":nn.Softplus() # Softplus 函数
            }
            # 设置输入和输出维度
            self.in_dim = in_dim
            self.out_dim = out_dim
            # 从可选激活函数中随机选择一个
            self.selected_name, self.activation = random.choice(list(activation_choices.items()))
            
            # 定义一个简单的多层感知机 (MLP)
            self.mlp = nn.Sequential(*[nn.Linear(self.in_dim, self.in_dim, device=device), # 线性层
                                       self.activation, # 激活函数
                                       nn.Linear(self.in_dim, self.out_dim, device=device), # 线性层
                                       self.activation # 激活函数
                                       ])
            # 初始化 MLP 的参数
            for i, (n, p) in enumerate(self.mlp.named_parameters()):
                # 如果参数是权重矩阵 (二维)
                if len(p.shape) == 2:
                    # 使用正态分布初始化权重，标准差为 init_std
                    nn.init.normal_(p, std=init_std)
            # 设置噪声标准差
            self.noise_std = noise_std
    
        def forward(self, x):
            # 在不计算梯度的情况下执行前向传播
            with torch.no_grad():
                # 生成形状为 (batch_size, out_dim) 的正态分布噪声
                noise = torch.normal(torch.zeros((x.shape[0], self.out_dim), device=device), self.noise_std)
                # MLP 的非线性输出
                non_linear_output = self.mlp(x)
                # 加性噪声模型：输出 = MLP输出 + 噪声
                output = non_linear_output + noise 
            # 返回浮点型的输出
            return output.float() 
        
    # 初始化一个空字典，用于存储每个非根节点的赋值信息
    assignment_list = {}
    # 获取图中所有非根节点（入度不为 0 的节点）
    non_root_nodes = [node for node in G.nodes if G.in_degree(node) != 0]
    # 遍历每个非根节点
    for node in non_root_nodes:
        # 获取当前节点的父节点列表
        parents = list(G.predecessors(node))
        # 如果 sample_std 为 True，则从以 noise_std 为标准差的正态分布中采样一个值作为噪声标准差，并取绝对值
        # 否则，直接使用传入的 noise_std
        noise_std_sample = torch.abs(torch.normal(torch.zeros(1), float(noise_std))) if sample_std else noise_std
        # 如果 sample_std 为 True，则从以 init_std 为标准差的正态分布中采样一个值作为 MLP 初始化标准差，并取绝对值
        # 否则，直接使用传入的 init_std
        init_std_sample = torch.abs(torch.normal(torch.zeros(1), float(init_std))).item() if sample_std else init_std
        # 将节点的赋值信息存入字典，包括父节点列表和生成的 AssingmentGenerator 实例
        assignment_list[node] = {'parents': parents,
            'assignment':AssingmentGenerator(len(parents), 1, noise_std_sample, init_std_sample, device)}
        
    # 返回包含所有非根节点赋值信息的字典
    return assignment_list
        

# Get perturbation node
# 定义一个函数，根据扰动类型获取单个扰动节点
def get_single_perturbation_node(graph, target_node, perturbation_type):
    # 如果扰动类型是 'on_grandparents' (在祖父节点上)
    if perturbation_type == 'on_grandparents':
        # 获取目标节点的祖父节点
        grandparents = get_grandparents(graph, target_node)
        # 从祖父节点中随机选择一个作为扰动节点
        perturbation_node = np.random.choice(list(grandparents))
    # 如果扰动类型是 'on_siblings' (在兄弟节点上)
    elif perturbation_type == 'on_siblings':
        # 获取目标节点的兄弟节点
        siblings = get_siblings(graph, target_node)
        # 从兄弟节点中随机选择一个作为扰动节点
        perturbation_node = np.random.choice(list(siblings))
    # 如果扰动类型是 'on_parents' (在父节点上)
    elif perturbation_type == 'on_parents':
        # 获取目标节点的父节点
        parents = get_parents(graph, target_node)
        # 从父节点中随机选择一个作为扰动节点
        perturbation_node = np.random.choice(list(parents))
    # 如果扰动类型是 'on_other_nodes' (在其他节点上)
    elif perturbation_type == 'on_other_nodes':
        # 获取目标节点的父节点、兄弟节点和祖父节点
        parents = get_parents(graph, target_node)
        siblings = get_siblings(graph, target_node)
        grandparents = get_grandparents(graph, target_node)
        # 将目标节点及其父节点、祖父节点、兄弟节点从所有节点中排除
        exclude = {target_node} | parents | grandparents | siblings
        all_nodes = set(graph.nodes)
        other_nodes = all_nodes - exclude
        # 从其他节点中随机选择一个作为扰动节点
        perturbation_node = np.random.choice(list(other_nodes))
    
    # 返回选择的扰动节点
    return perturbation_node

# 定义一个函数，根据扰动类型获取所有符合条件的扰动节点
def get_all_perturbation_nodes(graph, target_node, perturbation_type):
    # 如果扰动类型是 'on_grandparents'
    if perturbation_type == 'on_grandparents':
        # 获取目标节点的所有祖父节点
        perturbation_node = get_grandparents(graph, target_node)
    # 如果扰动类型是 'on_siblings'
    elif perturbation_type == 'on_siblings':
        # 获取目标节点的所有兄弟节点
        perturbation_node = get_siblings(graph, target_node)
    # 如果扰动类型是 'on_parents'
    elif perturbation_type == 'on_parents':
        # 获取目标节点的所有父节点
        perturbation_node = get_parents(graph, target_node)
    # 如果扰动类型是 'on_other_nodes'
    elif perturbation_type == 'on_other_nodes':
        # 获取目标节点的父节点、兄弟节点和祖父节点
        parents = get_parents(graph, target_node)
        siblings = get_siblings(graph, target_node)
        grandparents = get_grandparents(graph, target_node)
        # 将目标节点及其父节点、祖父节点、兄弟节点从所有节点中排除
        exclude = {target_node} | parents | grandparents | siblings
        all_nodes = set(graph.nodes)
        # 得到所有其他节点
        perturbation_node = all_nodes - exclude
    
    # 返回所有符合条件的扰动节点集合
    return perturbation_node


# Transfer continuous data into discrete data
# 定义一个多类排序任务的 PyTorch 模块
class MulticlassRank(nn.Module):
    '''多类排序任务'''
    def __init__(self, num_classes, ordered_p=0.5):
        super().__init__()
        # 使用 class_sampler_f 从 2 到 num_classes 之间采样实际使用的类别数量
        self.num_classes = class_sampler_f(2, num_classes)()
        # 设置有序概率，用于决定是否随机化类别顺序
        self.ordered_p = ordered_p

    def forward(self, x, class_boundaries=None):
        # x 的形状是 (T, B, H)，其中 T 通常是样本数量，B 是特征数量 (这里常为1)，H 是... (这里可能指单个值)
        # 注意：这个实现在批处理中为每个类边界采样相同的序列索引

        # 如果未提供类别边界
        if class_boundaries is None:
            # 从 x 的第一个维度（样本）中随机选择 num_classes - 1 个索引作为类别边界的原始值
            class_boundaries = torch.randint(0, x.shape[0], (self.num_classes - 1,))
            # 从 x 中获取这些边界值，并调整形状以进行广播比较
            class_boundaries = x[class_boundaries].unsqueeze(1) # 形状变为 (num_classes-1, 1, H) or (num_classes-1, B, H)
        
        # 将输入 x 与类别边界进行比较，计算每个样本属于哪个类别
        # (x > class_boundaries) 的结果是布尔张量，sum(axis=0) 沿着类别边界维度求和
        # 得到每个样本的类别标签 (0 到 num_classes-1)
        d = (x > class_boundaries).sum(axis=0) # 形状变为 (B, H)

        # 以下被注释掉的代码块用于随机化或反转类别顺序，但当前版本未使用
        # randomized_classes = torch.rand((d.shape[1], )) > self.ordered_p
        # d[:, randomized_classes] = randomize_classes(d[:, randomized_classes], self.num_classes)
        # reverse_classes = torch.rand((d.shape[1],)) > 0.5
        # d[:, reverse_classes] = self.num_classes - 1 - d[:, reverse_classes]
        # 返回离散化后的类别标签和使用的类别边界
        return d, class_boundaries

# 定义一个多类值任务的 PyTorch 模块
class MulticlassValue(nn.Module):
    '''多类任务，列表随机化与反转'''
    def __init__(self, num_classes, ordered_p=0.5):
        super().__init__()
        # 使用 class_sampler_f 从 2 到 num_classes 之间采样实际使用的类别数量
        self.num_classes = class_sampler_f(2, num_classes)()
        # 初始化 num_classes - 1 个类别边界值，作为模型的参数（但不需要梯度）
        self.classes = nn.Parameter(torch.randn(self.num_classes-1), requires_grad=False)
        # 设置有序概率
        self.ordered_p = ordered_p

    def forward(self, x):
        # x 的形状是 (T, B, H)
        # 将输入 x 与类别边界 self.classes 进行比较，计算类别标签
        # self.classes.unsqueeze(-1).unsqueeze(-1) 将边界调整形状以进行广播
        d = (x > (self.classes.unsqueeze(-1).unsqueeze(-1))).sum(axis=0) # 形状变为 (B, H)

        # 随机决定哪些批次/特征的类别顺序需要随机化
        randomized_classes = torch.rand((d.shape[1],)) > self.ordered_p
        # 对选定的批次/特征进行类别随机化
        d[:, randomized_classes] = randomize_classes(d[:, randomized_classes], self.num_classes)
        # 随机决定哪些批次/特征的类别顺序需要反转
        reverse_classes = torch.rand((d.shape[1],)) > 0.5
        # 对选定的批次/特征进行类别反转
        d[:, reverse_classes] = self.num_classes - 1 - d[:, reverse_classes]
        # 返回处理后的类别标签
        return d

# 定义结构化因果模型 (SCM) 的 PyTorch 模块
class SCM(torch.nn.Module):
    def __init__(self, config):
        super(SCM, self).__init__()
        # 保存配置字典
        self.config = config
        # 将配置字典中的所有键值对设置为类的属性
        for key in config:
            setattr(self, key, config[key])

        # 根据配置生成 DAG、根节点和分层节点
        self.dag, self.root_nodes, self.layer_nodes = generate_dag(self.n_nodes_list, self.num_children_samplers)
        # 以下被注释掉的代码用于绘制和保存 DAG 图
        # path = 'dag.png'
        # draw_graph(self.dag, path)
        
        # 为 DAG 中的非根节点生成赋值函数
        self.assignment = generate_assignment(self.dag, self.noise_std, self.init_std, self.device, self.sample_std)

        # 如果配置要求采样原因节点的范围
        if self.sample_cause_ranges:
            # 为根节点采样初始化范围的下界 (lb) 和上界 (ub)
            self.lb, self.ub = causes_sampler_f(len(self.root_nodes), self.min_root, self.max_root, self.max_range)
            # 将下界和上界转换为张量，并调整形状以匹配样本数量
            self.lb = torch.tensor(self.lb, device=self.device).unsqueeze(0).tile(
                (self.num_samples, 1))                
            self.ub = torch.tensor(self.ub, device=self.device).unsqueeze(0).tile(
                (self.num_samples, 1))
            
        # 选择特征和目标变量
        # 逐层随机丢弃特征节点
        self.selected_features = []
        # 遍历除最后一层外的所有层
        for layer in range(len(self.layer_nodes)-1):
            # 从当前层节点中随机选择一部分作为特征，选择比例为 (1 - drop_node_ratio)
            self.selected_features += list(np.random.choice(self.layer_nodes[layer], size=round(len(self.layer_nodes[layer])*(1-self.drop_node_ratio)), replace=False))
        
        # 在最后一层中随机选择一个目标节点
        # 对最后一层的节点进行随机排列
        perm_final_layer = np.random.permutation(self.layer_nodes[-1])
        # 遍历排列后的最后一层节点
        for node in perm_final_layer:
            # 选择第一个入度不为0的节点作为目标节点 (确保目标节点不是根节点)
            if self.dag.in_degree(node) != 0 :
                self.selected_target = node
                break # 找到后即停止

        # 如果最后一层节点数大于1，则从最后一层中（排除已选目标节点）添加更多特征
        if len(self.layer_nodes[-1]) > 1:
            # 计算要选择的特征数量，确保至少选择 (1-drop_node_ratio) 比例的节点，减1是因为目标节点已被选
            num_features_to_select = round(len(self.layer_nodes[-1])*(1-self.drop_node_ratio)) -1
            # 确保选择数量不为负
            num_features_to_select = max(0, num_features_to_select)
            # 从最后一层中排除目标节点后，随机选择特征
            potential_features = np.delete(self.layer_nodes[-1], np.where(self.layer_nodes[-1]==self.selected_target))
            if len(potential_features) > 0 and num_features_to_select > 0:
                 self.selected_features += list(np.random.choice(potential_features, size=min(num_features_to_select, len(potential_features)), replace=False))


        # 如果任务是分类任务
        if self.task == 'classification':
            # 初始化多类排序分配器
            self.class_assigner = MulticlassRank(self.num_classes, 
                       ordered_p=self.output_multiclass_ordered_p)
        
        
    # 定义 SCM 类的前向传播方法
    def forward(self, exist_boudries=None): # exist_boudries 参数用于在后续调用时复用类别边界
        # 采样根节点的数据
        if self.sample_cause_ranges:
            # 如果配置要求采样原因节点的范围，则在 [lb, ub) 范围内均匀采样根节点数据
            root_data = (self.ub - self.lb) * torch.rand((self.num_samples, len(self.root_nodes)), device=self.device) + self.lb
        else:
            # 否则，在 [min_root, max_root) 范围内均匀采样根节点数据
            root_data = (self.max_root - self.min_root) * torch.rand((self.num_samples, len(self.root_nodes)), device=self.device) + self.min_root
        # 将根节点数据转换为浮点型
        root_data = root_data.float()

        # 通过图传播数据    
        self.data = {} # 初始化一个字典来存储每个节点的数据
        # 按照 DAG 的拓扑排序遍历所有节点
        for i, node in enumerate(nx.topological_sort(self.dag)):
            # 如果当前节点是根节点
            if node in self.root_nodes:
                # 从 root_data 中获取对应的数据 (需要找到根节点在 root_nodes 中的索引，这里假设拓扑排序的前 len(self.root_nodes) 个是根节点且顺序一致)
                # 注意：这里的索引 i 对应拓扑排序的索引，如果根节点不都在最前面，或者顺序不一致，这里会出错。
                # 一个更稳健的方法是：root_node_idx = self.root_nodes.index(node) (如果 self.root_nodes 是列表)
                # 或者在初始化时就将根节点数据直接映射到 self.data[node]
                # 当前代码假设拓扑排序中根节点的顺序与 root_data 的列顺序一致。
                # 为了安全，我们应该找到 node 在 self.root_nodes 中的实际索引
                try:
                    # 尝试找到当前根节点在 self.root_nodes 列表中的索引
                    root_node_index_in_list = list(self.root_nodes).index(node)
                    self.data[node] = root_data[:, root_node_index_in_list].unsqueeze(1)
                except ValueError:
                    # 如果节点不在 self.root_nodes 中（理论上不应该发生，因为上面有 if node in self.root_nodes 判断）
                    # 或者如果 self.root_nodes 不是列表（它是 numpy array，需要转换）
                    # 这是一个潜在的bug点，如果拓扑排序和 self.root_nodes 的顺序不匹配
                    print(f"Warning: Root node {node} not found in self.root_nodes or indexing issue.")
                    # Fallback, though likely incorrect if orders don't match
                    self.data[node] = root_data[:, i].unsqueeze(1) # 使用拓扑排序的索引 i，这可能不正确
            else:
                # 如果当前节点不是根节点，则其值由其父节点的值和赋值函数决定
                # 获取父节点的数据，并拼接成一个张量
                parents_data = torch.cat([self.data[parent] for parent in self.assignment[node]['parents']], dim=1)              
                # 使用赋值函数计算当前节点的值
                self.data[node] = self.assignment[node]['assignment'](parents_data)
        
        # Select corresponding data
        values = torch.cat([self.data[node] for node in self.dag.nodes], dim=1)
        # x = values[:, self.selected_features]
        # y = values[:, [self.selected_target]]
        # x = torch.cat([self.data[node] for node in self.selected_features], dim=1)
        # y = self.data[self.target_node]
        node_to_idx = {node: i for i, node in enumerate(self.dag.nodes())} # node to index mapping
        selected_target_idx = node_to_idx[self.selected_target]
        selected_feature_indices = [node_to_idx[feat_node] for feat_node in self.selected_features if feat_node in node_to_idx]

        y = values[:, [selected_target_idx]]
        x = values[:, selected_feature_indices]

        # 数据后处理
        # 被注释掉的部分：移除 x 中的异常值并进行归一化
        # x = remove_outliers(x.unsqueeze(1))
        # x, y = normalize_data(x).squeeze(), normalize_data(y)

        # 生成类别特征 (当前被注释掉)
        ### NEED TO CHECK THE CONSISTENCY WITH INTERVENTION  TURN OFF FOR NOW ###
        # if self.categorical_feature_p and random.random() < self.categorical_feature_p:
        #     # 遍历每个特征列，随机生成不同数量的唯一类别，并使用MulticlassRank进行转换，模拟分类特征。这里使用了Gamma分布来生成类别数量，确保至少有2个类别，避免单一类别的情况
        #     # TODO 增加分类特征比例的分布控制的参数
        #     p = random.random()
        #     self.categorical_features = []
        #     for col in range(x.shape[1]):
        #         num_unique_features = max(round(random.gammavariate(1,10)),2)
        #         m = MulticlassRank(num_unique_features, ordered_p=0.3)
        #         if random.random() < p:
        #             self.categorical_features.append(col)
        #             x[:, col] = m(x[:, col].unsqueeze(1)).squeeze()        
        
        # 生成类别目标 (如果任务是分类)
        ### NEED TO CHECK IF STOCHACITY IN THE CLASS_ASSIGNER WILL INFLENCE THE SCM DEPENDENCY STRUCTURE #####
        class_boundaries = None # 初始化类别边界
        if self.task == 'classification':
            # 如果任务是分类
            if exist_boudries is not None:
                # 如果提供了已存在的类别边界，则使用它们
                y, class_boundaries = self.class_assigner(y, exist_boudries)
            else:
                # 否则，新生成类别边界
                y, class_boundaries = self.class_assigner(y)
            # 将目标变量 y 转换为整数类型并移除多余的维度
            y = y.int().squeeze()

        elif self.task == 'regression':
            # 如果任务是回归，将 y 转换为浮点型并移除多余维度
            y = y.float().squeeze()

        # 返回特征 x，目标 y，以及类别边界 (如果适用)
        return x, y, class_boundaries

    def perturbation(self, perturbation_type, perturbation_node_type):
            
        # Generate training data
        x_train, y_train, class_boundaries_train = self.forward()
        
        # Generate original test data
        x_test_original, y_test_original, class_boundaries = self.forward(exist_boudries=class_boundaries_train)
        
        if perturbation_type == 'single_node':
            # Get pertubation node
            perturbation_node = get_single_perturbation_node(self.dag, self.selected_target, perturbation_node_type)

            # Generate perturbated test data
            idx_perturbation_node = self.selected_features.index(perturbation_node)
            x_test_perturbation = copy.deepcopy(x_test_original)
            # perturbation_value = uniform_sampler_f(2, 5)() * torch.ones((self.num_samples, 1), device=self.device)
            perturbation_value = (5 - 2) * torch.rand((self.num_samples, len(perturbation_node)), device=self.device) + 5

            x_test_perturbation[:, [idx_perturbation_node]] = perturbation_value
            y_test_perturbation = copy.deepcopy(y_test_original)
        
        elif perturbation_type == 'complete_node_type':
            perturbation_node = get_all_perturbation_nodes(self.dag, self.selected_target, perturbation_node_type)

            # Generate perturbated test data
            x_test_perturbation = copy.deepcopy(x_test_original)
            # perturbation_value = torch.cat([uniform_sampler_f(2, 5)() * torch.ones((self.num_samples, 1), device=self.device) for i in range(len(x_test_perturbation))], dim=1)
            perturbation_value = (5 - 2) * torch.rand((self.num_samples, len(perturbation_node)), device=self.device) + 5
            
            idx_perturbation_node = [self.selected_features.index(val) for val in perturbation_node]
            x_test_perturbation[:, idx_perturbation_node] = perturbation_value
            y_test_perturbation = copy.deepcopy(y_test_original)            

        elif perturbation_type == 'complete_noncausal_node':
            noncausal_nodes_type = ['on_grandparents', 'on_siblings', 'on_other_nodes']
            perturbation_node = []
            for node_type in noncausal_nodes_type:
                perturbation_node += list(get_all_perturbation_nodes(self.dag, self.selected_target, node_type))

            # Generate perturbated test data
            x_test_perturbation = copy.deepcopy(x_test_original)
            # perturbation_value = torch.cat([uniform_sampler_f(2, 5)() * torch.ones((self.num_samples, 1), device=self.device) for i in range(len(x_test_perturbation))], dim=1)
            perturbation_value = (5 - 2) * torch.rand((self.num_samples, len(perturbation_node)), device=self.device) + 5
            
            idx_perturbation_node = [self.selected_features.index(val) for val in perturbation_node]
            x_test_perturbation[:, idx_perturbation_node] = perturbation_value
            y_test_perturbation = copy.deepcopy(y_test_original)   

        elif perturbation_type == 'direct_cause_block':
            # 如果是扰动直接原因 (父节点)
            # 获取目标节点的父节点
            parents = get_parents(self.dag, self.selected_target)
          
            # # Generate training data
            # x_train, y_train, class_boundaries_train = self.forward()            

            # # Generate original test data
            # x_test_original, y_test_original, class_boundaries = self.forward(exist_boudries=class_boundaries_train)

            # Generate perturbated test data
            idx_parents = [self.selected_features.index(val) for val in parents]
            x_test_perturbation = copy.deepcopy(x_test_original)

            # 创建一个随机排列的索引，用于从训练数据中采样父节点的值
            perm = list(range(x_test_original.shape[0]))
            random.shuffle(perm)

            perturbation_parents_values = x_train[perm, :][:, idx_parents]
            y_test_perturbation = y_train[perm]

            x_test_perturbation[:, idx_parents] = perturbation_parents_values

        elif perturbation_type == 'all_nodes':
            # # Generate training data
            # x_train, y_train, class_boundaries_train = self.forward()  

            # # Generate original test data
            # x_test_original, y_test_original, class_boundaries = self.forward(exist_boudries=class_boundaries_train)

            # Generate perturbated test data
            x_test_perturbation = (5 - 2) * torch.rand((self.num_samples, len(self.selected_features)), device=self.device) + 5

            y_test_perturbation = copy.deepcopy(y_test_original)                      
        
        output_original = (torch.cat([x_train, x_test_original], dim=0), torch.cat([y_train, y_test_original], dim=0))
        output_perturbation = (torch.cat([x_train, x_test_perturbation], dim=0), torch.cat([y_train, y_test_perturbation], dim=0))

        return output_original, output_perturbation

    def intervention(self, intervention_type, intervention_node_type):
        # Generate the original and intervened dataset at the same time
        if intervention_node_type == 'direct_cause':
            # Intervention on DAG
            direct_causes = np.asarray(list(self.dag.predecessors(self.selected_target)))
            if self.intervention_on_observed:
                direct_causes = [node for node in direct_causes if node in self.selected_features]
            if self.intervention_on_no_root:
                direct_causes = [node for node in direct_causes if node not in self.root_nodes]

            intervention_node = np.random.choice(direct_causes)
            
            parent_intervention = np.asarray(list(self.dag.predecessors(intervention_node)))
            intervened_dag = copy.deepcopy(self.dag)
            intervened_dag.remove_edges_from([(parent, intervention_node) for parent in parent_intervention])

             # 以下被注释掉的代码用于绘制干预前后的 DAG
            # path = 'dag_intervened.png'
            # draw_graph(intervened_dag, path)
            # path = 'self_dag_intervened.png'
            # draw_graph(self.dag, path)

            # Get all the descendants of intervened node in original graph
            descendants_intervened = get_all_descendants(intervened_dag, intervention_node)
            
            # Propagate the graph and set intervened node to intervention value
            # Sample root nodes
            # if intervention_node in self.root_nodes:
            #     root_nodes_intervened = copy.copy(self.root_nodes)
            #     root_nodes_intervened = np.delete(root_nodes_intervened, np.where(self.root_nodes==intervention_node))
            # else:
            #     root_nodes_intervened = self.root_nodes

            # if self.sample_cause_ranges:
            #     root_data = (self.ub - self.lb) * torch.rand((self.num_samples, len(self.root_nodes)), device=self.device) + self.lb
            # else:
            #     root_data = (self.max_root - self.min_root) * torch.rand((self.num_samples, len(self.root_nodes)), device=self.device) + self.min_root
            # root_data = root_data.float()
            if self.sample_cause_ranges:
                root_data_base = (self.ub - self.lb) * torch.rand((self.num_samples, len(self.root_nodes)), device=self.device) + self.lb
            else:
                root_data_base = (self.max_root - self.min_root) * torch.rand((self.num_samples, len(self.root_nodes)), device=self.device) + self.min_root
            root_data_base = root_data_base.float()
            # Propagate data through the graph    
            intervened_data = {}
            original_data = {}

            # 将 root_data_base 映射到每个根节点
            # 创建一个从根节点到其在 root_data_base 中列索引的映射
            root_node_to_idx_map = {node: i for i, node in enumerate(self.root_nodes)}

            # 遍历拓扑排序的节点
            for node in nx.topological_sort(self.dag):
                if node in self.root_nodes:
                    # 当前节点是根节点
                    original_value_for_node = root_data_base[:, root_node_to_idx_map[node]].unsqueeze(1)
                    original_data[node] = original_value_for_node
                    
                    if node == intervention_node:
                        # 如果当前根节点是干预节点
                        # 干预值：在 [0, 1) 范围内随机采样一个常数值，并广播到所有样本
                        intervened_data[node] = uniform_sampler_f(0, 1)() * torch.ones((self.num_samples, 1), device=self.device)
                    else:
                        # 如果当前根节点不是干预节点，其值在干预图中保持不变
                        intervened_data[node] = original_value_for_node
                else:
                    parents_data_intervention = torch.cat([intervened_data[parent] for parent in self.assignment[node]['parents']], dim=1)                
                    parents_data_original = torch.cat([original_data[parent] for parent in self.assignment[node]['parents']], dim=1)                
                    if node == intervention_node:                       
                        intervened_data[node] = uniform_sampler_f(0, 1)() * torch.ones((self.num_samples, 1), device=self.device)
                        original_data[node] = self.assignment[node]['assignment'](parents_data_original)
                    else:
                        if node not in descendants_intervened and intervention_node not in self.assignment[node]['parents']:
                            intervention_value = self.assignment[node]['assignment'](parents_data_intervention)
                            original_value = copy.deepcopy(intervention_value)
                            
                            intervened_data[node] = intervention_value
                            original_data[node] = original_value
                        else:
                            intervention_value = self.assignment[node]['assignment'](parents_data_intervention)
                            original_value = self.assignment[node]['assignment'](parents_data_original)

                            intervened_data[node] = intervention_value
                            original_data[node] = original_value

            # Select corresponding data
            # 收集干预后的数据
            values_intervened = torch.cat([intervened_data[node] for node in self.dag.nodes()], dim=1)
            node_to_idx = {node: i for i, node in enumerate(self.dag.nodes())} # node to index mapping
            selected_target_idx = node_to_idx[self.selected_target]
            selected_feature_indices = [node_to_idx[feat_node] for feat_node in self.selected_features if feat_node in node_to_idx]

            y_intervened = values_intervened[:, [selected_target_idx]]
            x_intervened = values_intervened[:, selected_feature_indices]

            # 收集原始数据
            values_original = torch.cat([original_data[node] for node in self.dag.nodes()], dim=1)
            y_original = values_original[:, [selected_target_idx]]
            x_original = values_original[:, selected_feature_indices]
            
            # Remove outliers in x and normalization      
            # x_intervened = remove_outliers(x_intervened.unsqueeze(1))
            # x_intervened, y_intervened = normalize_data(x_intervened).squeeze(), normalize_data(y_intervened)

            # x_original = remove_outliers(x_original.unsqueeze(1))
            # x_original, y_original = normalize_data(x_original).squeeze(), normalize_data(y_original)

            if self.task == 'classification':
                y_original, class_boundaries_original = self.class_assigner(y_original)
                y_original = y_original.int().squeeze()

                y_intervened, class_boundaries = self.class_assigner(y_intervened, class_boundaries_original)
                y_intervened = y_intervened.int().squeeze()

            elif self.task == 'regression':
                y_intervened = y_intervened.float().squeeze()
                y_original = y_original.float().squeeze()

            if intervention_type == 'counterfactual':
                output_original = (x_original, y_original)
                eval_position = int(self.num_samples *0.5)
                output_interventional = (torch.cat([x_original[:eval_position], x_intervened[eval_position:]], dim=0), torch.cat([y_original[:eval_position], y_intervened[eval_position:]], dim=0))
            elif intervention_type == 'statistical':
                output_original = self.forward()
                output_interventional = (torch.cat([x_original[:eval_position], x_intervened[eval_position:]], dim=0), torch.cat([y_original[:eval_position], y_intervened[eval_position:]], dim=0))              

        return output_original, output_interventional

def generate_datasets(num_dataset, h_config, perturbation_type=None, perturbation_node_type=None, intervention_type=None, intervention_node_type=None):
    datasets = []
    for i in range(num_dataset):
        dataset_name = 'dataset_{}'.format(i)
        config = config_generation(h_config)
        scm = SCM(config)
        
        try:
            if perturbation_type is not None:
                # assert(intervention_type is not None)
                # assert(intervention_node_type is not None)
                # (x_original, y_original), (x_intervened, y_intervened) = scm.intervention(intervention_type, intervention_node_type)

                # dataset = [dataset_name, x_original, y_original, x_intervened, y_intervened, config]
                # datasets.append(dataset) 
                (x_original, y_original), (x_perturbation, y_perturbation) = scm.perturbation(perturbation_type=perturbation_type, perturbation_node_type=perturbation_node_type)

                dataset = [dataset_name, x_original, y_original, x_perturbation, y_perturbation, config]
                datasets.append(dataset) 

            else:
                if intervention_type is not None:
                    x_intervened, y_intervened = scm.intervention(intervention_type, intervention_node_type)
                    data_x = x_intervened
                    data_y = y_intervened
                else:
                    data_x, data_y = scm()
                    # x_test_origine, y_test_origine = scm()
                    # data_x = torch.concat([x_train_origine, x_test_origine], dim=0)
                    # data_y = torch.concat([y_train_origine, y_test_origine], dim=0)
                    # path_x = './test_data/data_x_{}.csv'.format(dataset_name)
                    # path_y = './test_data/data_y_{}.csv'.format(dataset_name)
                    # path_data = './test_data/data_{}.csv'.format(dataset_name)
                    # data = {}
                    # for k, v in scm.data.items():
                    #     data[k] = v.reshape(-1)
                    # pd.DataFrame(data).to_csv(path_data)
                    # pd.DataFrame(data_x).to_csv(path_x)
                    # pd.DataFrame(data_y).to_csv(path_y)
                dataset = [dataset_name, data_x, data_y, config]
                datasets.append(dataset)
        except Exception as e:
            print('error {} caught during dataset generation on dataset{} '.format(e, dataset_name))
            continue 

    return datasets
        


if __name__ == '__main__':

    n_nodes_list = [10,10,5]
    max_num_children = 3
    num_children_samplers = [uniform_int_sampler_f(1, min(max_num_children, n_nodes_list[i+1])) for i in range(len(n_nodes_list)-1)]
    G, root_nodes, layer_nodes = generate_dag(n_nodes_list, num_children_samplers)
    path = './dag.png'
    draw_graph(G, path)
    # Test SCM (intervened) data generation
    config={
        'device':'cpu',
        'n_nodes_list':[10,10,5],
        'noise_std':0.01,
        'min_root':0.0,
        'max_root':0.5,
        'output_multiclass_ordered_p':0.5,
        'max_range': 0.5,
        'init_std':1,
        'num_children_samplers':[uniform_int_sampler_f(1, min(3, n_nodes_list[i+1])) for i in range(len(n_nodes_list)-1)],
        'num_samples':10,
        'num_classes':5,
        'sample_std':True,
        'task':'regression',
        'categorical_feature_p':0,
        'drop_node_ratio':0,
        'intervention_on_no_root': True,
        'intervention_on_observed':True,
        'sample_cause_ranges':True
    }
    scm = SCM(config)
    x_origine, y_origine, class_boundaries = scm()
    print()
    (x_ori,y_ori), (x_putb,y_putb) = scm.perturbation(perturbation_type='complete_noncausal_node',perturbation_node_type='on_grandparents')
    print()
    (x_ori,y_ori), (x_intv,y_intv) = scm.intervention(intervention_type='counterfactual', intervention_node_type = 'direct_cause')
    print()
    # Test generate_datasets function
    # num_dataset = 1
    # h_config={
    #     'device':'cpu',
    #     'min_num_node':5,
    #     'max_num_node':5,
    #     'num_layers':3,
    #     'max_num_children':3,
    #     'max_num_classes':10,
    #     'min_noise_std':0.01,
    #     'max_noise_std':0.1,
    #     'min_init_std':1,
    #     'max_init_std':5,
    #     'min_root':0.0,
    #     'max_root':0.5,
    #     'max_range': 0.5,
    #     'min_output_multiclass_ordered_p':0.0,
    #     'max_output_multiclass_ordered_p':0.5,
    #     'min_num_samples':100,
    #     'max_num_samples':1000,
    #     'test_sample_ratio':0.5,
    #     'categorical_feature_p':0,
    #     'min_drop_node_ratio':0,
    #     'max_drop_node_ratio':0,
    #     'task':'classification',
    #     'intervention_on_no_root':True,
    #     'sample_std':True,
    #     'single_effect_last_layer':False,
    #     'last_layer_min_num_node':True,
    #     'intervention_on_observed':True,
    #     'sample_cause_ranges':True        
    # }
    # benchmark = True
    # intervention_type = 'statistical'
    # intervention_node_type = 'direct_cause'
    # test_datasets = generate_datasets(num_dataset, h_config, benchmark=benchmark, intervention_type=intervention_type, intervention_node_type=intervention_node_type)

    import pdb; pdb.set_trace()


