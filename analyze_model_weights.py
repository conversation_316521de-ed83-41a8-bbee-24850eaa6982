import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import os
import json
from collections import defaultdict

def load_checkpoint(checkpoint_path):
    """加载检查点文件"""
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        return checkpoint
    except Exception as e:
        print(f"Error loading {checkpoint_path}: {e}")
        return None

def extract_weights(checkpoint):
    """从检查点中提取权重"""
    weights = {}
    
    # 尝试不同的可能键名
    possible_keys = ['model_state_dict', 'state_dict', 'model', 'net']
    state_dict = None
    
    for key in possible_keys:
        if key in checkpoint:
            state_dict = checkpoint[key]
            break
    
    # 如果没有找到标准键，检查是否直接是state_dict
    if state_dict is None:
        if isinstance(checkpoint, dict) and any(k.endswith('.weight') or k.endswith('.bias') for k in checkpoint.keys()):
            state_dict = checkpoint
        else:
            print("Available keys in checkpoint:", list(checkpoint.keys()) if isinstance(checkpoint, dict) else "Not a dict")
            return weights
    
    # 提取权重参数
    for name, param in state_dict.items():
        if isinstance(param, torch.Tensor):
            # 只保留权重和偏置参数
            if '.weight' in name or '.bias' in name:
                weights[name] = param.detach().cpu().numpy().flatten()
    
    return weights

def plot_weight_distributions(weights1, weights2, model1_name, model2_name):
    """绘制权重分布的直方图和核密度图"""

    # 获取所有层的名称
    all_layers = set(weights1.keys()) | set(weights2.keys())
    common_layers = set(weights1.keys()) & set(weights2.keys())

    print(f"Model 1 ({model1_name}) has {len(weights1)} layers")
    print(f"Model 2 ({model2_name}) has {len(weights2)} layers")
    print(f"Common layers: {len(common_layers)}")

    if not common_layers:
        print("No common layers found!")
        return

    # 选择重要的层进行可视化
    layers_to_plot = sorted(list(common_layers))

    # 创建多个图表
    # 1. 总体权重分布比较
    plot_overall_distribution(weights1, weights2, model1_name, model2_name)

    # 2. 按层类型分组的分布
    plot_layer_type_distributions(weights1, weights2, model1_name, model2_name, layers_to_plot)

    # 3. 详细的层级比较（绘制所有层）
    plot_detailed_layer_comparison(weights1, weights2, model1_name, model2_name, layers_to_plot)

def plot_overall_distribution(weights1, weights2, model1_name, model2_name):
    """绘制总体权重分布"""
    # 合并所有权重
    all_weights1 = np.concatenate([w for w in weights1.values()])
    all_weights2 = np.concatenate([w for w in weights2.values()])

    # 创建更大的图形，增加间距
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 7))
    fig.suptitle(f'Overall Weight Distribution Comparison\n{model1_name} vs {model2_name}',
                 fontsize=16, fontweight='bold', y=0.95)

    # 直方图
    ax1.hist(all_weights1, bins=80, alpha=0.6, label=model1_name, color='steelblue',
             density=True, edgecolor='black', linewidth=0.5)
    ax1.hist(all_weights2, bins=80, alpha=0.6, label=model2_name, color='crimson',
             density=True, edgecolor='black', linewidth=0.5)
    ax1.set_title('Histogram', fontsize=14, fontweight='bold', pad=20)
    ax1.set_xlabel('Weight Value', fontsize=12)
    ax1.set_ylabel('Density', fontsize=12)
    ax1.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3, linestyle='--')

    # 核密度图
    try:
        sns.kdeplot(all_weights1, ax=ax2, label=model1_name, color='steelblue', linewidth=2.5)
        sns.kdeplot(all_weights2, ax=ax2, label=model2_name, color='crimson', linewidth=2.5)
    except:
        kde1 = stats.gaussian_kde(all_weights1)
        kde2 = stats.gaussian_kde(all_weights2)
        x_range = np.linspace(min(np.min(all_weights1), np.min(all_weights2)),
                            max(np.max(all_weights1), np.max(all_weights2)), 200)
        ax2.plot(x_range, kde1(x_range), label=model1_name, color='steelblue', linewidth=2.5)
        ax2.plot(x_range, kde2(x_range), label=model2_name, color='crimson', linewidth=2.5)

    ax2.set_title('Kernel Density Estimation', fontsize=14, fontweight='bold', pad=20)
    ax2.set_xlabel('Weight Value', fontsize=12)
    ax2.set_ylabel('Density', fontsize=12)
    ax2.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3, linestyle='--')

    # 调整布局，增加间距
    plt.tight_layout(rect=[0, 0, 1, 0.92])
    plt.subplots_adjust(wspace=0.3)

    # 保存到new_model_checkpoints目录
    output_dir = "new_model_checkpoints"
    os.makedirs(output_dir, exist_ok=True)
    save_path = os.path.join(output_dir, 'overall_weight_distributions.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.show()
    plt.close()  # 关闭图形以释放内存

def plot_layer_type_distributions(weights1, weights2, model1_name, model2_name, layers):
    """按层类型分组绘制分布"""
    # 按层类型分组
    layer_groups = {
        'Transformer MLP': [l for l in layers if 'transformer_encoder' in l and 'mlp' in l],
        'Decoder': [l for l in layers if 'decoder_dict' in l],
        'Encoder': [l for l in layers if 'encoder' in l and 'transformer' not in l],
        'Embedding': [l for l in layers if 'embedding' in l],
        'Other': [l for l in layers if not any(keyword in l for keyword in ['transformer_encoder', 'decoder_dict', 'encoder', 'embedding'])]
    }

    # 过滤空组
    layer_groups = {k: v for k, v in layer_groups.items() if v}

    n_groups = len(layer_groups)
    # 创建更大的图形，增加垂直间距
    fig, axes = plt.subplots(n_groups, 2, figsize=(16, 6*n_groups))
    if n_groups == 1:
        axes = axes.reshape(1, -1)

    fig.suptitle(f'Weight Distribution by Layer Type\n{model1_name} vs {model2_name}',
                 fontsize=16, fontweight='bold', y=0.98)

    for i, (group_name, group_layers) in enumerate(layer_groups.items()):
        # 合并该组的所有权重
        group_weights1 = np.concatenate([weights1[l] for l in group_layers if l in weights1])
        group_weights2 = np.concatenate([weights2[l] for l in group_layers if l in weights2])

        # 直方图
        ax_hist = axes[i, 0]
        ax_hist.hist(group_weights1, bins=60, alpha=0.6, label=model1_name,
                    color='steelblue', density=True, edgecolor='black', linewidth=0.5)
        ax_hist.hist(group_weights2, bins=60, alpha=0.6, label=model2_name,
                    color='crimson', density=True, edgecolor='black', linewidth=0.5)
        ax_hist.set_title(f'{group_name} - Histogram', fontsize=13, fontweight='bold', pad=15)
        ax_hist.set_xlabel('Weight Value', fontsize=11)
        ax_hist.set_ylabel('Density', fontsize=11)
        ax_hist.legend(fontsize=10, frameon=True, fancybox=True, shadow=True)
        ax_hist.grid(True, alpha=0.3, linestyle='--')

        # 核密度图
        ax_kde = axes[i, 1]
        try:
            sns.kdeplot(group_weights1, ax=ax_kde, label=model1_name,
                       color='steelblue', linewidth=2.5)
            sns.kdeplot(group_weights2, ax=ax_kde, label=model2_name,
                       color='crimson', linewidth=2.5)
        except:
            kde1 = stats.gaussian_kde(group_weights1)
            kde2 = stats.gaussian_kde(group_weights2)
            x_range = np.linspace(min(np.min(group_weights1), np.min(group_weights2)),
                                max(np.max(group_weights1), np.max(group_weights2)), 200)
            ax_kde.plot(x_range, kde1(x_range), label=model1_name,
                       color='steelblue', linewidth=2.5)
            ax_kde.plot(x_range, kde2(x_range), label=model2_name,
                       color='crimson', linewidth=2.5)

        ax_kde.set_title(f'{group_name} - KDE', fontsize=13, fontweight='bold', pad=15)
        ax_kde.set_xlabel('Weight Value', fontsize=11)
        ax_kde.set_ylabel('Density', fontsize=11)
        ax_kde.legend(fontsize=10, frameon=True, fancybox=True, shadow=True)
        ax_kde.grid(True, alpha=0.3, linestyle='--')

    # 调整布局，增加间距
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.subplots_adjust(hspace=0.4, wspace=0.3)

    # 保存到new_model_checkpoints目录
    output_dir = "new_model_checkpoints"
    os.makedirs(output_dir, exist_ok=True)
    save_path = os.path.join(output_dir, 'layer_type_weight_distributions.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.show()
    plt.close()  # 关闭图形以释放内存

def plot_detailed_layer_comparison(weights1, weights2, model1_name, model2_name, layers_to_plot):
    """详细的层级比较 - 绘制所有层，分页显示以避免布局混乱"""
    # 绘制所有层，不进行筛选
    all_layers = sorted(list(set(weights1.keys()) & set(weights2.keys())))
    n_layers = len(all_layers)
    layers_per_page = 6  # 每页显示6层

    # 分页处理
    for page in range(0, n_layers, layers_per_page):
        end_idx = min(page + layers_per_page, n_layers)
        current_layers = all_layers[page:end_idx]
        n_current = len(current_layers)

        # 创建当前页的图形
        fig, axes = plt.subplots(n_current, 2, figsize=(16, 4.5*n_current))
        if n_current == 1:
            axes = axes.reshape(1, -1)

        page_num = page // layers_per_page + 1
        total_pages = (n_layers + layers_per_page - 1) // layers_per_page
        fig.suptitle(f'Detailed Layer Weight Distributions (Page {page_num}/{total_pages})\n{model1_name} vs {model2_name}',
                     fontsize=16, fontweight='bold', y=0.96)

        for i, layer_name in enumerate(current_layers):
            w1 = weights1[layer_name]
            w2 = weights2[layer_name]

            # 简化层名显示
            display_name = layer_name.replace('transformer_encoder.layers.', 'TF_L').replace('.weight', '_W').replace('.bias', '_B')
            display_name = display_name.replace('decoder_dict.standard.', 'Dec_').replace('feature_positional_embedding_embeddings', 'PosEmb')
            display_name = display_name.replace('y_encoder.1.layer', 'YEnc')

            # 直方图
            ax_hist = axes[i, 0]
            ax_hist.hist(w1, bins=50, alpha=0.6, label=model1_name,
                        color='steelblue', density=True, edgecolor='black', linewidth=0.3)
            ax_hist.hist(w2, bins=50, alpha=0.6, label=model2_name,
                        color='crimson', density=True, edgecolor='black', linewidth=0.3)
            ax_hist.set_title(f'Histogram: {display_name}', fontsize=12, fontweight='bold', pad=12)
            ax_hist.set_xlabel('Weight Value', fontsize=10)
            ax_hist.set_ylabel('Density', fontsize=10)
            ax_hist.legend(fontsize=9, frameon=True, fancybox=True, shadow=True)
            ax_hist.grid(True, alpha=0.3, linestyle='--')

            # 核密度图
            ax_kde = axes[i, 1]
            try:
                # 检查是否有足够的变化来计算KDE
                if np.std(w1) > 1e-8 and np.std(w2) > 1e-8:
                    sns.kdeplot(w1, ax=ax_kde, label=model1_name,
                               color='steelblue', linewidth=2.5, warn_singular=False)
                    sns.kdeplot(w2, ax=ax_kde, label=model2_name,
                               color='crimson', linewidth=2.5, warn_singular=False)
                else:
                    # 如果方差太小，显示提示信息
                    ax_kde.text(0.5, 0.5, 'Insufficient variance\nfor KDE estimation',
                               transform=ax_kde.transAxes, ha='center', va='center',
                               fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
            except Exception as e:
                # 如果seaborn失败，尝试scipy
                try:
                    if np.std(w1) > 1e-8 and np.std(w2) > 1e-8:
                        kde1 = stats.gaussian_kde(w1)
                        kde2 = stats.gaussian_kde(w2)
                        x_range = np.linspace(min(np.min(w1), np.min(w2)),
                                            max(np.max(w1), np.max(w2)), 200)
                        ax_kde.plot(x_range, kde1(x_range), label=model1_name,
                                   color='steelblue', linewidth=2.5)
                        ax_kde.plot(x_range, kde2(x_range), label=model2_name,
                                   color='crimson', linewidth=2.5)
                    else:
                        ax_kde.text(0.5, 0.5, 'Insufficient variance\nfor KDE estimation',
                                   transform=ax_kde.transAxes, ha='center', va='center',
                                   fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
                except:
                    ax_kde.text(0.5, 0.5, f'KDE failed:\n{str(e)[:30]}...',
                               transform=ax_kde.transAxes, ha='center', va='center',
                               fontsize=9, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))

            ax_kde.set_title(f'KDE: {display_name}', fontsize=12, fontweight='bold', pad=12)
            ax_kde.set_xlabel('Weight Value', fontsize=10)
            ax_kde.set_ylabel('Density', fontsize=10)
            ax_kde.legend(fontsize=9, frameon=True, fancybox=True, shadow=True)
            ax_kde.grid(True, alpha=0.3, linestyle='--')

        # 调整布局
        plt.tight_layout(rect=[0, 0, 1, 0.93])
        plt.subplots_adjust(hspace=0.4, wspace=0.3)

        # 保存当前页到new_model_checkpoints目录
        output_dir = "new_model_checkpoints"
        os.makedirs(output_dir, exist_ok=True)
        filename = f'detailed_layer_weight_distributions_page_{page_num}.png'
        save_path = os.path.join(output_dir, filename)
        plt.savefig(save_path, dpi=300, bbox_inches='tight',
                    facecolor='white', edgecolor='none')
        plt.show()
        plt.close()  # 关闭图形以释放内存

def compute_statistics(weights1, weights2, model1_name, model2_name):
    """计算权重分布的统计信息并保存为JSON"""
    print(f"\n=== Weight Distribution Statistics ===")
    print(f"Model 1: {model1_name}")
    print(f"Model 2: {model2_name}")
    print("="*50)

    common_layers = set(weights1.keys()) & set(weights2.keys())

    # 创建统计数据字典
    statistics = {
        "model1_name": model1_name,
        "model2_name": model2_name,
        "layer_statistics": {}
    }

    for layer_name in sorted(common_layers):
        w1 = weights1[layer_name]
        w2 = weights2[layer_name]

        # 计算统计量
        stats1 = {
            "min": float(np.min(w1)),
            "median": float(np.median(w1)),
            "max": float(np.max(w1)),
            "mean": float(np.mean(w1)),
            "std": float(np.std(w1)),
            "shape": list(w1.shape) if hasattr(w1, 'shape') else [len(w1)]
        }

        stats2 = {
            "min": float(np.min(w2)),
            "median": float(np.median(w2)),
            "max": float(np.max(w2)),
            "mean": float(np.mean(w2)),
            "std": float(np.std(w2)),
            "shape": list(w2.shape) if hasattr(w2, 'shape') else [len(w2)]
        }

        # 计算差异
        differences = {
            "min_diff": abs(stats1["min"] - stats2["min"]),
            "median_diff": abs(stats1["median"] - stats2["median"]),
            "max_diff": abs(stats1["max"] - stats2["max"]),
            "mean_diff": abs(stats1["mean"] - stats2["mean"]),
            "std_diff": abs(stats1["std"] - stats2["std"])
        }

        # 保存到统计字典
        statistics["layer_statistics"][layer_name] = {
            model1_name: stats1,
            model2_name: stats2,
            "differences": differences
        }

        # 打印统计信息
        print(f"\nLayer: {layer_name}")
        print(f"  {model1_name}:")
        print(f"    Min: {stats1['min']:.6f}, Median: {stats1['median']:.6f}, Max: {stats1['max']:.6f}")
        print(f"    Mean: {stats1['mean']:.6f}, Std: {stats1['std']:.6f}")
        print(f"    Shape: {stats1['shape']}")

        print(f"  {model2_name}:")
        print(f"    Min: {stats2['min']:.6f}, Median: {stats2['median']:.6f}, Max: {stats2['max']:.6f}")
        print(f"    Mean: {stats2['mean']:.6f}, Std: {stats2['std']:.6f}")
        print(f"    Shape: {stats2['shape']}")

        print(f"  Differences:")
        print(f"    Min diff: {differences['min_diff']:.6f}, Median diff: {differences['median_diff']:.6f}")
        print(f"    Max diff: {differences['max_diff']:.6f}, Mean diff: {differences['mean_diff']:.6f}")
        print(f"    Std diff: {differences['std_diff']:.6f}")

    # 保存统计数据为JSON
    output_dir = "new_model_checkpoints"
    os.makedirs(output_dir, exist_ok=True)
    json_path = os.path.join(output_dir, "weight_statistics_comparison.json")

    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(statistics, f, indent=2, ensure_ascii=False)

    print(f"\n统计数据已保存到: {json_path}")
    return statistics

def main():
    # 模型检查点路径
    checkpoint1_path = "/root/LDM_test/tabpfn-v2-regressor.ckpt"
    checkpoint2_path = "new_model_checkpoints/mse_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt"
    
    model1_name = "DEFAULT Model"
    model2_name = "MSE Model"
    
    print("Loading checkpoints...")
    
    # 加载检查点
    checkpoint1 = load_checkpoint(checkpoint1_path)
    checkpoint2 = load_checkpoint(checkpoint2_path)
    
    if checkpoint1 is None or checkpoint2 is None:
        print("Failed to load one or both checkpoints")
        return
    
    print("Extracting weights...")
    
    # 提取权重
    weights1 = extract_weights(checkpoint1)
    weights2 = extract_weights(checkpoint2)
    
    if not weights1 or not weights2:
        print("Failed to extract weights from one or both models")
        return
    
    # 计算统计信息
    compute_statistics(weights1, weights2, model1_name, model2_name)
    
    # 绘制分布图
    print("\nGenerating plots...")
    plot_weight_distributions(weights1, weights2, model1_name, model2_name)
    
    print("Analysis complete! Check 'weight_distributions_comparison.png' for the visualization.")

if __name__ == "__main__":
    main()
