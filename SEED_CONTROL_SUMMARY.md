# 随机种子控制修复总结

## 问题描述

在原始代码中，`generate_datasets`函数的随机性没有被完全控制，导致多次运行相同配置时无法得到相同的结果。主要问题包括：

1. **`config_generation`函数中的随机性未被控制**：该函数使用多个采样器生成配置参数，但没有传递种子参数
2. **特征选择中的随机性未被控制**：`select_features`函数使用`np.random.choice`但没有种子控制
3. **目标节点选择中的随机性未被控制**：`select_valid_target_node`函数中的随机选择没有种子控制
4. **其他随机操作的种子控制不完整**：一些随机操作设置了种子但没有正确恢复随机状态

## 修复方案

### 1. 修改 `config_generation` 函数

**文件**: `multi_data_generator.py`

**修改内容**:
- 添加 `seed` 参数
- 为所有采样器函数传递种子参数，使用不同的偏移量避免冲突：
  - `num_node_sampler`: `seed`
  - `num_children_samplers`: `seed + 100 + i`
  - `noise_std`: `seed + 200`
  - `init_std`: `seed + 300`
  - `output_multiclass_ordered_p`: `seed + 400`
  - `num_samples`: `seed + 500`
  - `drop_node_ratio`: `seed + 600`
  - `num_classes`: `seed + 700`

### 2. 修改 `generate_datasets` 函数

**文件**: `multi_data_generator.py`

**修改内容**:
- 为每个数据集生成配置时传递种子：`config_seed = seed + i * 10000`
- 确保每个数据集使用不同但可重现的种子

### 3. 修改 `select_features` 函数

**文件**: `multi_scm_model.py`

**修改内容**:
- 添加随机种子控制：`seed + 8000`
- 在随机选择前保存随机状态，选择后恢复状态
- 确保特征选择的可重现性

### 4. 修改 `select_valid_target_node` 函数

**文件**: `multi_scm_model.py`

**修改内容**:
- 在目标节点选择时添加种子控制：`seed + 9000`
- 保存和恢复随机状态

### 5. 修改 `get_unobserved_nodes` 函数

**文件**: `multi_scm_model.py`

**修改内容**:
- 在所有使用`np.random.choice`的地方添加完整的种子控制
- 保存和恢复随机状态，确保不影响其他随机操作

### 6. 修改 `generate_sample_index` 函数

**文件**: `multi_scm_model.py`

**修改内容**:
- 添加完整的随机状态保存和恢复机制

## 种子分配策略

为了避免种子冲突，采用了以下分配策略：

- **数据集级别**: `seed + i` (i为数据集索引)
- **配置生成**: `seed + i * 10000`
- **配置内部采样器**: `seed + 100~700` (不同偏移)
- **特征选择**: `seed + 8000`
- **目标节点选择**: `seed + 9000`
- **不可观测节点选择**: `seed + 4000~4100`
- **样本索引生成**: `seed + 6000`
- **前向传播**: `seed + 1000~3000`

## 验证结果

### 测试1: 可重现性测试
- ✅ 相同种子的多次运行产生完全相同的结果
- ✅ 不同种子的运行产生不同的结果

### 测试2: 数据集多样性测试
- ✅ 一次运行中生成的多个数据集使用不同的随机种子
- ✅ 每个数据集都是唯一的

## 使用示例

```python
from multi_data_generator import generate_datasets

# 配置参数
h_config = {
    'device': 'cpu',
    'min_num_node': 5,
    'max_num_node': 10,
    # ... 其他配置
}

# 第一次运行
datasets_run1 = generate_datasets(
    num_dataset=3,
    h_config=h_config,
    intervention_type='counterfactual',
    intervention_node_type='all_parents',
    intervention_value_method='sample',
    custom_dag_type='common_cause',
    node_unobserved=False,
    seed=42  # 设置种子
)

# 第二次运行（相同种子）
datasets_run2 = generate_datasets(
    num_dataset=3,
    h_config=h_config,
    intervention_type='counterfactual',
    intervention_node_type='all_parents',
    intervention_value_method='sample',
    custom_dag_type='common_cause',
    node_unobserved=False,
    seed=42  # 相同种子
)

# datasets_run1 和 datasets_run2 将完全相同
```

## 关键特性

1. **完全可重现**: 相同种子的多次运行产生相同结果
2. **数据集多样性**: 一次运行中的不同数据集使用不同种子
3. **状态隔离**: 随机操作不会影响其他部分的随机性
4. **向后兼容**: 不传递种子时行为与原来相同

## 测试文件

- `test_seed_control.py`: 完整的种子控制测试
- `demo_seed_control.py`: 演示脚本

运行测试：
```bash
python test_seed_control.py
python demo_seed_control.py
```

## 总结

通过这些修改，现在可以确保：
- 多次运行相同配置的`generate_datasets`函数时，对应的第i个原始数据和干预数据分别相等
- 在一次运行生成的n个数据集中，n个数据的随机种子是不一样的
- 所有随机性都被正确控制，结果完全可重现
