"""
g函数配置示例 - 展示如何配置后非线性变换函数

展示如何在SCM模型中使用不同的函数作为g函数，包括：
1. 简单的单调激活函数（向后兼容）
2. 复杂的1维函数（使用function_generator.py中的所有函数类型）
"""

from scm_test import FunctionConfig, NodeFunctionConfigs, TestConfig, SNRConfig

def example_identity_g_function():
    """恒等g函数配置示例（默认）"""
    print("=" * 60)
    print("恒等g函数配置示例（默认）")
    print("=" * 60)
    
    # 使用默认的identity g函数
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='linear',
            coefficients=[1.5, -0.8],
            bias=0.2,
            # g_function_type='identity'  # 默认值，可以不指定
            target_snr=2.0
        ),
        target_child_config=FunctionConfig(
            function_type='polynomial',
            degree=2,
            g_function_type='identity',  # 显式指定
            noise_std=0.1
        ),
        other_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=3,
            depth=2,
            activation='tanh',
            # 不指定g_function_type，使用默认identity
            target_snr=1.8
        )
    )
    
    print("恒等g函数配置:")
    print(f"  Target: {node_configs.target_config.function_type} + identity(默认)")
    print(f"  Target_child: {node_configs.target_child_config.function_type} + {node_configs.target_child_config.g_function_type}")
    print(f"  Other: {node_configs.other_config.function_type} + identity(默认)")
    print("  说明: identity函数不改变输入，相当于传统的ANM模型")
    
    return node_configs

def example_sigmoid_g_function():
    """Sigmoid g函数配置示例"""
    print("\n" + "=" * 60)
    print("Sigmoid g函数配置示例")
    print("=" * 60)
    
    # 使用sigmoid g函数
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='linear',
            coefficients=[2.0, -1.5, 1.0],
            g_function_type='sigmoid',  # 输出范围(0,1)
            target_snr=2.5
        ),
        target_child_config=FunctionConfig(
            function_type='exponential',
            scale=0.8,
            g_function_type='sigmoid',
            noise_std=0.08
        ),
        other_config=FunctionConfig(
            function_type='sine',
            frequency=1.5,
            scale=2.0,
            g_function_type='sigmoid',
            target_snr=2.0
        )
    )
    
    print("Sigmoid g函数配置:")
    print(f"  Target: {node_configs.target_config.function_type} + {node_configs.target_config.g_function_type}")
    print(f"  Target_child: {node_configs.target_child_config.function_type} + {node_configs.target_child_config.g_function_type}")
    print(f"  Other: {node_configs.other_config.function_type} + {node_configs.other_config.g_function_type}")
    print("  说明: sigmoid将输出压缩到(0,1)范围，适合概率或比例数据")
    
    return node_configs

def example_tanh_g_function():
    """Tanh g函数配置示例"""
    print("\n" + "=" * 60)
    print("Tanh g函数配置示例")
    print("=" * 60)
    
    # 使用tanh g函数
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='polynomial',
            degree=3,
            scale=1.2,
            g_function_type='tanh',  # 输出范围(-1,1)
            target_snr=2.0
        ),
        target_child_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=4,
            depth=2,
            activation='relu',
            g_function_type='tanh',
            noise_std=0.12
        ),
        other_config=FunctionConfig(
            function_type='cosine',
            frequency=2.0,
            g_function_type='tanh',
            target_snr=1.5
        )
    )
    
    print("Tanh g函数配置:")
    print(f"  Target: {node_configs.target_config.function_type} + {node_configs.target_config.g_function_type}")
    print(f"  Target_child: {node_configs.target_child_config.function_type} + {node_configs.target_child_config.g_function_type}")
    print(f"  Other: {node_configs.other_config.function_type} + {node_configs.other_config.g_function_type}")
    print("  说明: tanh将输出压缩到(-1,1)范围，适合标准化数据")
    
    return node_configs

def example_softplus_g_function():
    """Softplus g函数配置示例"""
    print("\n" + "=" * 60)
    print("Softplus g函数配置示例")
    print("=" * 60)
    
    # 使用softplus g函数
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='logarithmic',
            scale=1.0,
            g_function_type='softplus',  # 输出范围(0,∞)
            target_snr=2.2
        ),
        target_child_config=FunctionConfig(
            function_type='linear',
            coefficients=[1.0, 2.0],
            bias=-0.5,
            g_function_type='softplus',
            noise_std=0.1
        ),
        other_config=FunctionConfig(
            function_type='gaussian_rbf',
            centers=[[0.0, 0.0], [1.0, 1.0]],
            g_function_type='softplus',
            target_snr=1.8
        )
    )
    
    print("Softplus g函数配置:")
    print(f"  Target: {node_configs.target_config.function_type} + {node_configs.target_config.g_function_type}")
    print(f"  Target_child: {node_configs.target_child_config.function_type} + {node_configs.target_child_config.g_function_type}")
    print(f"  Other: {node_configs.other_config.function_type} + {node_configs.other_config.g_function_type}")
    print("  说明: softplus确保输出为正值，适合计数或强度数据")
    
    return node_configs

def example_mixed_g_functions():
    """混合g函数配置示例"""
    print("\n" + "=" * 60)
    print("混合g函数配置示例")
    print("=" * 60)
    
    # 不同节点使用不同的g函数
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=3,
            depth=2,
            activation='tanh',
            g_function_type='elu+1',  # ELU+1确保正值
            target_snr=2.5
        ),
        target_child_config=FunctionConfig(
            function_type='fourier_series',
            n_components=4,
            frequency=1.0,
            g_function_type='scaled_tanh',  # 缩放的tanh
            noise_std=0.09
        ),
        other_config=FunctionConfig(
            function_type='piecewise_linear',
            n_components=3,
            g_function_type='sigmoid',  # 标准sigmoid
            target_snr=2.0
        )
    )
    
    print("混合g函数配置:")
    print(f"  Target: {node_configs.target_config.function_type} + {node_configs.target_config.g_function_type}")
    print(f"  Target_child: {node_configs.target_child_config.function_type} + {node_configs.target_child_config.g_function_type}")
    print(f"  Other: {node_configs.other_config.function_type} + {node_configs.other_config.g_function_type}")
    print("  说明: 不同节点使用不同的g函数，增加模型的表达能力")
    
    return node_configs

def example_complete_test_with_g_functions():
    """包含g函数的完整测试配置"""
    print("\n" + "=" * 60)
    print("包含g函数的完整测试配置")
    print("=" * 60)
    
    # 使用混合g函数的完整配置
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='polynomial',
            degree=2,
            scale=1.5,
            g_function_type='sigmoid',
            target_snr=2.5,
            noise_mean=0.02,
            noise_mean_mode='fixed'
        ),
        target_child_config=FunctionConfig(
            function_type='sine',
            frequency=2.0,
            scale=1.0,
            g_function_type='tanh',
            noise_std=0.1,
            noise_mean=-0.01,
            noise_mean_mode='fixed'
        ),
        other_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=4,
            depth=2,
            activation='relu',
            g_function_type='softplus',
            target_snr=2.0,
            noise_mean_mode='signal_mean'
        )
    )
    
    # SNR配置
    snr_config = SNRConfig(
        parent_snr_values=[2.0, 2.5],
        child_snr_multipliers=[0.8, 1.0],
        other_snr_multipliers=[1.0, 1.2]
    )
    
    # 完整测试配置
    test_config = TestConfig(
        n_datasets=3,
        snr_config=snr_config,
        node_function_configs=node_configs,
        device='cpu',
        num_workers=2
    )
    
    total_configs = (len(snr_config.parent_snr_values) * 
                    len(snr_config.child_snr_multipliers) * 
                    len(snr_config.other_snr_multipliers))
    
    print("完整配置详情:")
    print(f"  将生成 {total_configs} 种配置组合")
    print(f"  每种组合生成 {test_config.n_datasets} 个数据集")
    print(f"  总计 {total_configs * test_config.n_datasets} 个数据集")
    print(f"  所有数据集都将使用指定的g函数进行后非线性变换")
    
    return test_config

def example_complex_g_functions():
    """复杂g函数配置示例（使用function_generator中的函数类型）"""
    print("\n" + "=" * 60)
    print("复杂g函数配置示例")
    print("=" * 60)

    # 使用复杂函数作为g函数
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=3,
            depth=2,
            activation='relu',
            g_function_config={  # 使用多项式作为g函数
                'type': 'polynomial',
                'degree': 2,
                'scale': 1.0
            },
            target_snr=2.5
        ),
        target_child_config=FunctionConfig(
            function_type='linear',
            coefficients=[1.5, -0.8],
            g_function_config={  # 使用正弦函数作为g函数
                'type': 'sine',
                'frequency': 2.0,
                'scale': 1.5
            },
            noise_std=0.1
        ),
        other_config=FunctionConfig(
            function_type='exponential',
            scale=0.8,
            g_function_config={  # 使用神经网络作为g函数
                'type': 'random_neural_network',
                'hidden_dim': 2,
                'depth': 1,
                'activation': 'tanh'
            },
            target_snr=2.0
        )
    )

    print("复杂g函数配置:")
    print(f"  Target: {node_configs.target_config.function_type} + polynomial g函数")
    print(f"  Target_child: {node_configs.target_child_config.function_type} + sine g函数")
    print(f"  Other: {node_configs.other_config.function_type} + neural_network g函数")
    print("  说明: 使用复杂函数作为g函数，增加模型表达能力")

    return node_configs

def example_linear_g_functions():
    """线性g函数配置示例"""
    print("\n" + "=" * 60)
    print("线性g函数配置示例")
    print("=" * 60)

    # 使用线性函数作为g函数
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='polynomial',
            degree=2,
            g_function_config={  # 线性缩放和偏移
                'type': 'linear',
                'coefficients': [2.0],  # 缩放因子
                'bias': 0.5  # 偏移量
            },
            target_snr=2.0
        ),
        target_child_config=FunctionConfig(
            function_type='sine',
            frequency=1.5,
            g_function_config={  # 不同的线性变换
                'type': 'linear',
                'coefficients': [0.5],  # 压缩
                'bias': -0.2  # 负偏移
            },
            noise_std=0.08
        ),
        other_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=2,
            g_function_config={  # 恒等线性变换
                'type': 'linear',
                'coefficients': [1.0],
                'bias': 0.0
            },
            target_snr=1.8
        )
    )

    print("线性g函数配置:")
    print(f"  Target: 2x + 0.5 变换")
    print(f"  Target_child: 0.5x - 0.2 变换")
    print(f"  Other: x + 0 变换（等价于identity）")
    print("  说明: 线性g函数提供简单的缩放和偏移变换")

    return node_configs

def example_priority_demonstration():
    """配置优先级演示"""
    print("\n" + "=" * 60)
    print("配置优先级演示")
    print("=" * 60)

    # 同时指定简单和复杂g函数配置
    node_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='linear',
            g_function_type='sigmoid',  # 简单类型
            g_function_config={  # 复杂配置，优先级更高
                'type': 'exponential',
                'scale': 0.5
            },
            target_snr=2.0
        ),
        target_child_config=FunctionConfig(
            function_type='polynomial',
            degree=2,
            g_function_type='tanh',  # 只有简单类型
            noise_std=0.1
        ),
        other_config=FunctionConfig(
            function_type='sine',
            frequency=1.0,
            # 都不指定，使用默认identity
            target_snr=1.5
        )
    )

    print("优先级演示:")
    print("  Target: 同时指定sigmoid和exponential，将使用exponential（复杂配置优先）")
    print("  Target_child: 只指定tanh，将使用tanh")
    print("  Other: 都不指定，将使用默认identity")
    print("  说明: g_function_config优先于g_function_type")

    return node_configs

def main():
    """展示所有g函数配置示例"""
    print("g函数配置示例 - 后非线性变换函数配置")
    print("=" * 80)

    # 展示各种g函数配置
    example_identity_g_function()
    example_sigmoid_g_function()
    example_tanh_g_function()
    example_softplus_g_function()
    example_mixed_g_functions()
    example_complex_g_functions()  # 新增
    example_linear_g_functions()   # 新增
    example_priority_demonstration()  # 新增
    example_complete_test_with_g_functions()
    
    print("\n" + "=" * 80)
    print("g函数配置系统总结:")
    print("=" * 80)
    
    print("支持的g函数类型:")
    print("\n简单类型（g_function_type）:")
    simple_functions = [
        ('identity', '恒等函数', 'f(x) = x', '不改变输出，传统ANM模型'),
        ('sigmoid', 'Sigmoid函数', 'f(x) = 1/(1+exp(-x))', '输出范围(0,1)，适合概率数据'),
        ('tanh', 'Tanh函数', 'f(x) = tanh(x)', '输出范围(-1,1)，适合标准化数据'),
        ('softplus', 'Softplus函数', 'f(x) = log(1+exp(x))', '输出范围(0,∞)，适合正值数据'),
        ('elu+1', 'ELU+1函数', 'f(x) = ELU(x)+1', '确保正值输出，平滑过渡'),
        ('scaled_tanh', '缩放Tanh', 'f(x) = 2*tanh(x/2)', '缩放的tanh，保持单调性')
    ]

    for func_type, name, formula, desc in simple_functions:
        print(f"  {func_type:12} - {name:12} - {formula:20} - {desc}")

    print("\n复杂类型（g_function_config）:")
    complex_functions = [
        ('linear', '线性函数', 'f(x) = ax + b', '缩放和偏移变换'),
        ('polynomial', '多项式函数', 'f(x) = ax^n + ...', '非线性多项式变换'),
        ('sine/cosine', '三角函数', 'f(x) = a*sin(bx)', '周期性变换'),
        ('exponential', '指数函数', 'f(x) = a*exp(bx)', '指数增长/衰减'),
        ('logarithmic', '对数函数', 'f(x) = a*log(bx)', '对数变换'),
        ('neural_network', '神经网络', '多层感知机', '复杂非线性变换'),
        ('gaussian_rbf', '高斯RBF', '径向基函数', '局部化变换'),
        ('fourier_series', '傅里叶级数', '频域变换', '频率分解'),
        ('piecewise_linear', '分段线性', '分段函数', '分段变换')
    ]

    for func_type, name, formula, desc in complex_functions:
        print(f"  {func_type:15} - {name:12} - {formula:15} - {desc}")
    
    print("\n配置优势:")
    print("✅ 极大的灵活性：支持所有function_generator.py中的函数类型")
    print("✅ 向后兼容：保持对简单激活函数的支持")
    print("✅ 配置优先级：复杂配置优先于简单类型")
    print("✅ 1维约束：所有g函数都是1维输入输出，保持一致性")
    print("✅ 参数化：支持函数特定参数的完整配置")
    print("✅ 混合使用：可以在同一配置中混合使用不同类型的g函数")
    print("✅ 数据适应性：根据数据特性选择最合适的变换函数")

    print("\n使用建议:")
    print("简单类型适用场景:")
    print("• identity: 传统线性/非线性关系，不需要输出变换")
    print("• sigmoid: 概率、比例、二分类相关的变量")
    print("• tanh: 标准化数据、对称分布的变量")
    print("• softplus: 计数数据、强度数据、必须为正的变量")

    print("\n复杂类型适用场景:")
    print("• linear: 需要精确控制缩放和偏移的场景")
    print("• polynomial: 需要非线性但可控的变换")
    print("• sine/cosine: 周期性数据或需要振荡变换")
    print("• exponential: 指数增长/衰减的数据")
    print("• neural_network: 需要复杂非线性变换的场景")
    print("• gaussian_rbf: 需要局部化变换的场景")

    print("\n配置方式:")
    print("1. 简单配置: g_function_type='sigmoid'")
    print("2. 复杂配置: g_function_config={'type': 'polynomial', 'degree': 2}")
    print("3. 优先级: 如果同时指定，g_function_config优先")

    print("\n注意事项:")
    print("⚠️  g函数的选择应该基于目标变量的实际含义和分布")
    print("⚠️  复杂g函数可能影响模型的可识别性，需要谨慎选择")
    print("⚠️  不同的g函数会影响噪声的相对大小和SNR的解释")
    print("⚠️  建议先使用简单类型，确有需要时再使用复杂类型")

if __name__ == "__main__":
    main()
