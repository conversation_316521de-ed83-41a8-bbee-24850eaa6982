import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
# 生成示例数据（替换为实际数据）
np.random.seed(42)
A = np.random.normal(50, 15, 1000)
B = A + np.random.normal(0, 10, 1000)  # B与A有相关性但存在差异

plt.figure(figsize=(10,6))
plt.scatter(A, B, c=np.where(A>B, 'r', 'g'), alpha=0.6, s=20)
plt.plot([0,100], [0,100], 'k--', lw=1)  # 添加参考对角线
plt.colorbar(label='A vs B (Red:A>B, Green:B>A)')
plt.xlabel('Group A Values')
plt.ylabel('Group B Values')
plt.title('Scatter Plot with Reference Line [[1]](#__1) [[3]](#__3)')
plt.grid(alpha=0.3)
plt.show()

