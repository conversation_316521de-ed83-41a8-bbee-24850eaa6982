import os
import json
import pandas as pd
import numpy as np

import matplotlib.pyplot as plt

import traceback
import networkx as nx
from datetime import datetime
from collections import OrderedDict
from sklearn.feature_selection import mutual_info_regression
from scm_utils import  get_exclusive_node_relationships
from plot_utils import calculate_effective_datasets
#------------------------------------------------extract scm info-----------------------------------------------
def extract_serializable_scm_info(scm_objects, extract_function_weights=True):
    """
    从SCM对象中提取可序列化的信息，避免pickle错误

    Args:
        scm_objects: {dataset_idx: scm_object} 格式的字典
        extract_function_weights: 是否提取函数的具体参数权重

    Returns:
        dict: 包含可序列化信息的字典
    """
    serializable_info = {}

    for dataset_idx, scm in scm_objects.items():
        try:
            # 提取基本信息
            info = {
                'dataset_idx': dataset_idx,
                'selected_features': getattr(scm, 'selected_features', None),
                'selected_target': getattr(scm, 'selected_target', None),
                'num_samples': getattr(scm, 'num_samples', None),
            }

            # 提取配置信息（从scm.config中获取）
            if hasattr(scm, 'config') and scm.config:
                config = scm.config
                info['config'] = {
                    'train_test_split_ratio': config.get('train_test_split_ratio', 0.5),
                    'noise_config': {
                        'min_noise_std': config.get('min_noise_std', 0.1),
                        'max_noise_std': config.get('max_noise_std', 0.5),
                        'noise_distribution': 'gaussian',  # 当前代码使用高斯噪声分布
                        'noise_mean': 0.0,  # 高斯噪声的均值
                        'use_snr_method': config.get('use_snr_method', False),
                        'snr_config': config.get('snr_config', {}),
                        'use_monte_carlo_precompute': config.get('use_monte_carlo_precompute', False)
                    },
                    'root_distribution_config': {
                        'root_distribution': config.get('root_distribution', 'gaussian'),
                        'root_mean': config.get('root_mean', 0.0),
                        'root_std': config.get('root_std', 1.0),
                        'sample_root_std': config.get('sample_root_std', False),
                        'min_root': config.get('min_root', 0.0),
                        'max_root': config.get('max_root', 1.0),
                        'sample_cause_ranges': config.get('sample_cause_ranges', False)
                    }
                }

            # 提取DAG信息（如果存在）
            if hasattr(scm, 'dag'):
                info['dag_nodes'] = list(scm.dag.nodes())
                info['dag_edges'] = list(scm.dag.edges())

            # 提取DAG类型信息（如果存在）
            if hasattr(scm, 'dag_info') and scm.dag_info:
                info['dag_info'] = scm.dag_info
                # 特别提取structure_type用于判断是否为随机图
                info['dag_structure_type'] = scm.dag_info.get('structure_type', None)

            # 提取SNR验证结果
            if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results:
                info['snr_validation_results'] = scm.snr_validation_results

            # 提取根节点分布信息
            if hasattr(scm, 'get_root_distribution_info'):
                try:
                    info['root_distribution_info'] = scm.get_root_distribution_info()
                except Exception as e:
                    info['root_distribution_info'] = f"Error: {str(e)}"

            # 提取节点统计信息
            if hasattr(scm, 'get_node_statistics'):
                try:
                    info['node_statistics'] = scm.get_node_statistics()
                except Exception as e:
                    info['node_statistics'] = f"Error: {str(e)}"

            # 提取assignment信息（包含函数配置）
            if hasattr(scm, 'assignment') and scm.assignment:
                assignment_info = {}
                for node, assignment_data in scm.assignment.items():
                    if 'assignment' in assignment_data:
                        assignment_obj = assignment_data['assignment']
                        node_info = {
                            'parents': assignment_data.get('parents', [])
                        }

                        # 提取自定义函数配置和详细的生成机制
                        if hasattr(assignment_obj, 'custom_function_config') and assignment_obj.custom_function_config:
                            func_config = assignment_obj.custom_function_config
                            node_info['function_config'] = {
                                "type": func_config.get('type', 'mlp'),
                                "parents": node_info['parents']  # 添加父节点信息
                            }

                            # 保存函数参数，但排除噪声和SNR相关配置
                            for param_name, param_value in func_config.items():
                                if param_name not in ['type', 'target_snr', 'noise_std']:
                                    # 确保参数值是可序列化的
                                    if isinstance(param_value, (list, tuple)):
                                        node_info['function_config'][param_name] = list(param_value)
                                    elif isinstance(param_value, (str, int, float, bool, type(None))):
                                        node_info['function_config'][param_name] = param_value
                                    elif hasattr(param_value, 'tolist'):  # torch.Tensor
                                        node_info['function_config'][param_name] = param_value.tolist()
                        else:
                            node_info['function_config'] = {
                                "type": "mlp",
                                "parents": node_info['parents']
                            }

                        # 提取具体的函数生成机制（如权重、偏置等）
                        node_info['generation_mechanism'] = {}

                        # 根据extract_function_weights参数决定是否提取具体参数
                        if extract_function_weights and hasattr(assignment_obj, 'f_function') and assignment_obj.f_function is not None:
                            f_func = assignment_obj.f_function
                            parents = node_info['parents']

                            # 根据函数类型提取不同的参数
                            func_type = node_info['function_config'].get('type', 'mlp')

                            # 添加调试信息
                            print(f"正在提取节点 {node} 的函数参数，类型: {func_type}, f_function类型: {type(f_func)}")

                            # 初始化参数提取成功标志
                            params_extracted = False

                            # 检查是否是线性函数（nn.Linear或具有weight和bias属性的函数）
                            if hasattr(f_func, 'weight') and hasattr(f_func, 'bias'):
                                try:
                                    print(f"  检测到线性函数，提取权重和偏置...")
                                    # 线性函数: y = a1*x1 + a2*x2 + ... + b
                                    weights = f_func.weight.detach().cpu().numpy().flatten().tolist()
                                    bias = f_func.bias.detach().cpu().numpy().item() if f_func.bias is not None else 0.0
                                    print(f"  权重: {weights}, 偏置: {bias}")

                                    # 构建具体的函数表达式
                                    if len(parents) > 0 and len(weights) >= len(parents):
                                        terms = []
                                        # 只使用与父节点数量相匹配的权重
                                        used_weights = weights[:len(parents)]

                                        for parent, weight in zip(parents, used_weights):
                                            if abs(weight) > 1e-6:  # 避免显示非常小的权重
                                                if abs(weight - 1.0) < 1e-6:
                                                    terms.append(f"{parent}")
                                                elif abs(weight + 1.0) < 1e-6:
                                                    terms.append(f"-{parent}")
                                                else:
                                                    terms.append(f"{weight:.4f}*{parent}")

                                        if abs(bias) > 1e-6:  # 避免显示非常小的偏置
                                            if bias > 0:
                                                terms.append(f"{bias:.4f}")
                                            else:
                                                terms.append(f"{bias:.4f}")

                                        expression = " + ".join(terms).replace("+ -", "- ")
                                        if not expression:
                                            expression = "0"

                                        node_info['generation_mechanism'] = {
                                            'expression': f"{node} = {expression}",
                                            'weights': {parent: weight for parent, weight in zip(parents, used_weights)},
                                            'bias': bias,
                                            'parameters': {
                                                'coefficients': used_weights,
                                                'bias': bias
                                            },
                                            'function_type': func_type
                                        }

                                        # 同时更新function_config，添加具体的运行时参数
                                        node_info['function_config'].update({
                                            'w': [used_weights],  # 权重矩阵格式
                                            'b': [bias],  # 偏置向量格式
                                            'g_function': 'identity'  # 线性函数的激活函数是恒等函数
                                        })

                                        params_extracted = True
                                        print(f"  成功提取线性函数参数: {expression}")
                                    else:
                                        # 如果父节点信息不匹配，至少保存权重和偏置
                                        node_info['generation_mechanism'] = {
                                            'expression': f"{node} = linear_function({', '.join(parents)})",
                                            'parameters': {
                                                'coefficients': weights,
                                                'bias': bias
                                            },
                                            'function_type': func_type,
                                            'parents': parents
                                        }

                                        # 更新function_config
                                        node_info['function_config'].update({
                                            'w': [weights],
                                            'b': [bias],
                                            'g_function': 'identity'
                                        })

                                        params_extracted = True
                                        print(f"  保存线性函数基本参数")
                                except Exception as e:
                                    print(f"提取线性函数参数时出错 (节点 {node}): {e}")
                                    node_info['generation_mechanism'] = {
                                        'expression': f"{node} = {func_type}({', '.join(parents)})",
                                        'function_type': func_type,
                                        'parents': parents,
                                        'error': str(e)
                                    }

                            elif func_type in ['random_neural_network', 'mlp'] and not params_extracted:
                                try:
                                    print(f"  检测到神经网络函数，提取网络结构...")
                                    # 神经网络函数：提取网络结构信息和具体参数
                                    if hasattr(f_func, 'network'):
                                        network_info = {
                                            'input_dim': len(parents),
                                            'layers': []
                                        }

                                        detailed_params = {}

                                        for layer_idx, layer in enumerate(f_func.network):
                                            if hasattr(layer, 'weight'):
                                                # 线性层：提取权重和偏置
                                                weights = layer.weight.detach().cpu().numpy().tolist()
                                                bias = layer.bias.detach().cpu().numpy().tolist() if layer.bias is not None else None

                                                layer_info = {
                                                    'type': 'linear',
                                                    'input_size': layer.weight.shape[1],
                                                    'output_size': layer.weight.shape[0],
                                                    'weights_shape': list(layer.weight.shape),
                                                    'has_bias': layer.bias is not None,
                                                    'weights': weights,
                                                    'bias': bias
                                                }
                                                network_info['layers'].append(layer_info)
                                                detailed_params[f'layer_{layer_idx}_weights'] = weights
                                                if bias is not None:
                                                    detailed_params[f'layer_{layer_idx}_bias'] = bias

                                            elif hasattr(layer, '__class__'):
                                                # 激活函数层
                                                layer_info = {
                                                    'type': layer.__class__.__name__.lower(),
                                                }
                                                network_info['layers'].append(layer_info)

                                        node_info['generation_mechanism'] = {
                                            'expression': f"{node} = neural_network({', '.join(parents)})",
                                            'network_structure': network_info,
                                            'detailed_parameters': detailed_params,
                                            'function_type': func_type,
                                            'parents': parents
                                        }

                                        # 更新function_config，添加网络结构信息
                                        node_info['function_config'].update({
                                            'network_layers': len(network_info['layers']),
                                            'layer_details': network_info['layers'],
                                            'g_function': func_config.get('activation', 'tanh') if hasattr(assignment_obj, 'custom_function_config') else 'tanh'
                                        })

                                        # 如果有具体的权重参数，也添加到function_config
                                        if detailed_params:
                                            node_info['function_config']['detailed_weights'] = detailed_params

                                        params_extracted = True
                                        print(f"  成功提取神经网络参数，共{len(network_info['layers'])}层")
                                    else:
                                        print(f"  神经网络函数没有network属性")
                                except Exception as e:
                                    print(f"提取神经网络参数时出错 (节点 {node}): {e}")
                                    node_info['generation_mechanism'] = {
                                        'expression': f"{node} = {func_type}({', '.join(parents)})",
                                        'function_type': func_type,
                                        'parents': parents,
                                        'error': str(e)
                                    }

                            # 尝试提取其他函数类型的参数
                            elif func_type == 'polynomial' and hasattr(f_func, 'coefficients') and hasattr(f_func, 'powers'):
                                try:
                                    # 多项式函数: y = a1*x1^p1 + a2*x2^p2 + ... + b
                                    coefficients = f_func.coefficients.detach().cpu().numpy().tolist()
                                    powers = f_func.powers.detach().cpu().numpy().tolist() if hasattr(f_func.powers, 'detach') else f_func.powers
                                    bias = f_func.bias.detach().cpu().numpy().item() if hasattr(f_func, 'bias') and f_func.bias is not None else 0.0

                                    terms = []
                                    for parent, coef, power in zip(parents, coefficients, powers):
                                        if abs(coef) > 1e-6:
                                            if power == 1:
                                                terms.append(f"{coef:.4f}*{parent}")
                                            else:
                                                terms.append(f"{coef:.4f}*{parent}^{power}")

                                    if abs(bias) > 1e-6:
                                        terms.append(f"{bias:.4f}")

                                    expression = " + ".join(terms).replace("+ -", "- ")

                                    node_info['generation_mechanism'] = {
                                        'expression': f"{node} = {expression}",
                                        'parameters': {
                                            'coefficients': coefficients,
                                            'powers': powers,
                                            'bias': bias
                                        },
                                        'function_type': func_type,
                                        'parents': parents
                                    }
                                except Exception as e:
                                    print(f"提取多项式函数参数时出错 (节点 {node}): {e}")
                                    node_info['generation_mechanism'] = {
                                        'expression': f"{node} = {func_type}({', '.join(parents)})",
                                        'function_type': func_type,
                                        'parents': parents,
                                        'error': str(e)
                                    }

                            elif func_type == 'exponential' and hasattr(f_func, 'coefficients'):
                                try:
                                    # 指数函数: y = a1*exp(b1*x1) + a2*exp(b2*x2) + ... + c
                                    coefficients = f_func.coefficients.detach().cpu().numpy().tolist()
                                    exponents = f_func.exponents.detach().cpu().numpy().tolist() if hasattr(f_func, 'exponents') else [1.0] * len(coefficients)
                                    bias = f_func.bias.detach().cpu().numpy().item() if hasattr(f_func, 'bias') and f_func.bias is not None else 0.0

                                    terms = []
                                    for parent, coef, exp in zip(parents, coefficients, exponents):
                                        if abs(coef) > 1e-6:
                                            terms.append(f"{coef:.4f}*exp({exp:.4f}*{parent})")

                                    if abs(bias) > 1e-6:
                                        terms.append(f"{bias:.4f}")

                                    expression = " + ".join(terms).replace("+ -", "- ")

                                    node_info['generation_mechanism'] = {
                                        'expression': f"{node} = {expression}",
                                        'parameters': {
                                            'coefficients': coefficients,
                                            'exponents': exponents,
                                            'bias': bias
                                        },
                                        'function_type': func_type,
                                        'parents': parents
                                    }
                                except Exception as e:
                                    print(f"提取指数函数参数时出错 (节点 {node}): {e}")
                                    node_info['generation_mechanism'] = {
                                        'expression': f"{node} = {func_type}({', '.join(parents)})",
                                        'function_type': func_type,
                                        'parents': parents,
                                        'error': str(e)
                                    }


                            # 如果还没有提取到参数，尝试通用参数提取
                            if not params_extracted:
                                try:
                                    print(f"  尝试通用参数提取...")
                                    params = {}

                                    # 尝试提取常见的参数属性
                                    common_attrs = ['coefficients', 'weights', 'bias', 'powers', 'exponents',
                                                  'hidden_dim', 'depth', 'lengthscale', 'f_magn', 'a_coeffs',
                                                  'b_coeffs', 'n_terms', 'x_points', 'y_values']

                                    for attr_name in common_attrs:
                                        if hasattr(f_func, attr_name):
                                            attr_value = getattr(f_func, attr_name)
                                            if hasattr(attr_value, 'detach'):  # torch.Tensor
                                                params[attr_name] = attr_value.detach().cpu().numpy().tolist()
                                            elif isinstance(attr_value, (int, float, str, bool)):
                                                params[attr_name] = attr_value
                                            elif isinstance(attr_value, (list, tuple)):
                                                params[attr_name] = list(attr_value)

                                    # 特殊处理：如果是高斯过程函数
                                    if func_type == 'gaussian_process':
                                        if hasattr(f_func, 'kernel') and hasattr(f_func.kernel, 'lengthscale'):
                                            params['kernel_lengthscale'] = f_func.kernel.lengthscale.detach().cpu().numpy().tolist()
                                        if hasattr(f_func, 'kernel') and hasattr(f_func.kernel, 'variance'):
                                            params['kernel_variance'] = f_func.kernel.variance.detach().cpu().numpy().tolist()

                                    node_info['generation_mechanism'] = {
                                        'expression': f"{node} = {func_type}({', '.join(parents)})",
                                        'parameters': params if params else None,
                                        'function_type': func_type,
                                        'parents': parents
                                    }

                                    # 更新function_config，添加提取到的参数
                                    if params:
                                        node_info['function_config'].update(params)

                                        # 根据函数类型添加特定的激活函数信息
                                        if func_type == 'gaussian_process':
                                            node_info['function_config']['g_function'] = 'gaussian_process'
                                        elif func_type == 'polynomial':
                                            node_info['function_config']['g_function'] = 'polynomial'
                                        elif func_type == 'exponential':
                                            node_info['function_config']['g_function'] = 'exponential'
                                        else:
                                            node_info['function_config']['g_function'] = func_type

                                        print(f"  成功提取通用参数: {list(params.keys())}")
                                        params_extracted = True
                                    else:
                                        print(f"  未找到可提取的参数")

                                except Exception as e:
                                    print(f"通用参数提取失败 (节点 {node}): {e}")
                                    node_info['generation_mechanism'] = {
                                        'expression': f"{node} = {func_type}({', '.join(parents)})",
                                        'function_type': func_type,
                                        'parents': parents,
                                        'error': str(e)
                                    }
                        elif hasattr(assignment_obj, 'f_function') and assignment_obj.f_function is not None:
                            # 如果有f_function但不提取权重，只提供基本信息
                            func_type = node_info['function_config'].get('type', 'mlp')
                            print(f"跳过节点 {node} 的函数权重提取（extract_function_weights=False），类型: {func_type}")
                            node_info['generation_mechanism'] = {
                                'expression': f"{node} = {func_type}({', '.join(node_info['parents'])})",
                                'parents': node_info['parents'],
                                'function_type': func_type,
                                'weights_extracted': False  # 标记权重未被提取
                            }
                        else:
                            # 如果没有f_function，提供基本信息
                            node_info['generation_mechanism'] = {
                                'expression': f"{node} = function({', '.join(node_info['parents'])})",
                                'parents': node_info['parents'],
                                'function_type': node_info['function_config'].get('type', 'unknown')
                            }

                        assignment_info[node] = node_info

                info['assignment_info'] = assignment_info

            # 提取其他可序列化属性
            for attr in ['intervention_nodes', 'perturbation_nodes', 'unobserved_nodes']:
                if hasattr(scm, attr):
                    value = getattr(scm, attr)
                    # 确保值是可序列化的
                    if isinstance(value, (list, tuple, set)):
                        info[attr] = list(value)
                    elif isinstance(value, (str, int, float, bool, type(None))):
                        info[attr] = value

            serializable_info[dataset_idx] = info

        except Exception as e:
            # 如果提取失败，至少保存基本信息
            serializable_info[dataset_idx] = {
                'dataset_idx': dataset_idx,
                'error': f"Failed to extract SCM info: {str(e)}"
            }

    return serializable_info

#-------------------------------------------------------------DAG-----------------------------------------------
def should_draw_dag_shared(scm, shared_drawn_dag_signatures):
    """
    检查是否应该绘制DAG图（使用共享字典避免重复绘制相同的DAG）

    Args:
        scm: SCM对象
        shared_drawn_dag_signatures: 共享的已绘制DAG签名字典

    Returns:
        bool: 是否应该绘制DAG图
    """
    dag_signature = create_dag_signature(scm)
    if dag_signature is None:
        return False

    # 将签名转换为字符串，因为复杂对象可能无法作为字典键
    signature_key = str(dag_signature)

    if signature_key in shared_drawn_dag_signatures:
        return False

    # 使用字典模拟集合，设置值为True
    shared_drawn_dag_signatures[signature_key] = True
    return True

def create_dag_signature(scm):
    """
    创建DAG的完整签名，包括图结构、干预/扰动节点和目标节点

    Args:
        scm: SCM对象

    Returns:
        tuple: DAG的完整签名
    """
    if not hasattr(scm, 'dag'):
        return None

    # 图结构签名
    nodes = tuple(sorted(list(scm.dag.nodes())))
    edges = tuple(sorted(list(scm.dag.edges())))

    # 目标节点
    target_node = getattr(scm, 'selected_target', None)

    # 干预/扰动节点
    intervention_nodes = getattr(scm, 'intervention_nodes', None)
    if intervention_nodes is None:
        intervention_nodes = getattr(scm, 'perturbation_nodes', None)

    if intervention_nodes is not None:
        if isinstance(intervention_nodes, (list, set, tuple)):
            intervention_nodes = tuple(sorted(intervention_nodes))
        else:
            intervention_nodes = (intervention_nodes,)

    # 特征节点
    selected_features = getattr(scm, 'selected_features', None)
    if selected_features is not None:
        if isinstance(selected_features, (list, set, tuple)):
            selected_features = tuple(sorted(selected_features))
        else:
            selected_features = (selected_features,)

    # 不可观测节点
    unobserved_nodes = getattr(scm, 'unobserved_nodes', None)
    if unobserved_nodes is not None:
        if isinstance(unobserved_nodes, (list, set, tuple)):
            unobserved_nodes = tuple(sorted(unobserved_nodes))
        else:
            unobserved_nodes = (unobserved_nodes,)

    return (nodes, edges, target_node, intervention_nodes, selected_features, unobserved_nodes)

def create_dag_signature_from_info(scm_info):
    """
    从序列化的SCM信息创建DAG签名

    Args:
        scm_info: 序列化的SCM信息字典

    Returns:
        tuple: DAG的完整签名
    """
    if 'dag_nodes' not in scm_info or 'dag_edges' not in scm_info:
        return None

    # 图结构签名
    nodes = tuple(sorted(scm_info['dag_nodes']))
    edges = tuple(sorted(scm_info['dag_edges']))

    # 目标节点
    target_node = scm_info.get('selected_target', None)

    # 干预/扰动节点
    intervention_nodes = scm_info.get('intervention_nodes', None)
    if intervention_nodes is None:
        intervention_nodes = scm_info.get('perturbation_nodes', None)

    if intervention_nodes is not None:
        if isinstance(intervention_nodes, (list, set, tuple)):
            intervention_nodes = tuple(sorted(intervention_nodes))
        else:
            intervention_nodes = (intervention_nodes,)

    # 特征节点
    selected_features = scm_info.get('selected_features', None)
    if selected_features is not None:
        if isinstance(selected_features, (list, set, tuple)):
            selected_features = tuple(sorted(selected_features))
        else:
            selected_features = (selected_features,)

    # 不可观测节点
    unobserved_nodes = scm_info.get('unobserved_nodes', None)
    if unobserved_nodes is not None:
        if isinstance(unobserved_nodes, (list, set, tuple)):
            unobserved_nodes = tuple(sorted(unobserved_nodes))
        else:
            unobserved_nodes = (unobserved_nodes,)

    return (nodes, edges, target_node, intervention_nodes, selected_features, unobserved_nodes)

def compare_graph_structures(scm_objects, grouping_strategy='by_structure'):
    """
    比较多个SCM对象的图结构是否相同，并根据策略进行分组

    Args:
        scm_objects: {dataset_idx: {config_key: scm_info}} 格式的字典（现在是序列化信息）
        grouping_strategy: 分组策略
            - 'by_structure': 按图结构分组（相同节点数和边数为一组）
            - 'by_config': 按SNR配置分组（相同配置的所有数据集为一组，忽略图结构差异）

    Returns:
        dict: {
            'same_structure': bool,  # 是否所有图结构都相同
            'structure_groups': list,  # 按图结构分组的数据集
            'structure_info': dict,  # 每个结构组的详细信息
            'dag_signatures': dict,  # DAG完整签名信息
        }
    """
    if not scm_objects:
        return {'same_structure': True, 'structure_groups': [], 'structure_info': {}, 'dag_signatures': {}}

    # 收集所有图结构信息和DAG签名
    structure_signatures = {}  # {dataset_idx: {config_key: signature}}
    dag_signatures = {}       # {dataset_idx: {config_key: dag_signature}}

    for dataset_idx, configs in scm_objects.items():
        structure_signatures[dataset_idx] = {}
        dag_signatures[dataset_idx] = {}

        for config_key, scm_info in configs.items():
            # 现在scm_info是序列化的信息字典
            if 'dag_nodes' in scm_info and 'dag_edges' in scm_info:
                # 创建图结构签名：节点数、边数、边的集合
                nodes = sorted(scm_info['dag_nodes'])
                edges = sorted(scm_info['dag_edges'])
                signature = {
                    'nodes': tuple(nodes),
                    'edges': tuple(edges),
                    'node_count': len(nodes),
                    'edge_count': len(edges)
                }
                structure_signatures[dataset_idx][config_key] = signature

                # 创建完整DAG签名（从序列化信息）
                dag_signature = create_dag_signature_from_info(scm_info)
                dag_signatures[dataset_idx][config_key] = dag_signature

    # 分析结构相似性
    all_signatures = []
    for dataset_sigs in structure_signatures.values():
        all_signatures.extend(dataset_sigs.values())

    if not all_signatures:
        return {'same_structure': True, 'structure_groups': [], 'structure_info': {}, 'dag_signatures': dag_signatures}

    # 检查是否所有结构都相同
    first_signature = all_signatures[0]
    same_structure = all(
        sig['nodes'] == first_signature['nodes'] and
        sig['edges'] == first_signature['edges']
        for sig in all_signatures
    )

    # 按结构分组
    structure_groups = {}  # {structure_id: [dataset_indices]}
    structure_info = {}    # {structure_id: signature}

    for dataset_idx, configs in structure_signatures.items():
        for config_key, signature in configs.items():
            # 创建结构ID
            structure_id = f"nodes_{signature['node_count']}_edges_{signature['edge_count']}"

            if structure_id not in structure_groups:
                structure_groups[structure_id] = []
                structure_info[structure_id] = signature

            structure_groups[structure_id].append((dataset_idx, config_key))

    return {
        'same_structure': same_structure,
        'structure_groups': list(structure_groups.values()),
        'structure_info': structure_info,
        'dag_signatures': dag_signatures
    }


#-------------------------------------------------------------json-----------------------------------------------
# 计算相关系数和互信息
def calc_corr_and_mi(xs, ys):
    # 相关系数
    data = np.hstack([xs, ys.reshape(-1, 1)])
    corr = np.corrcoef(data, rowvar=False)
    # 互信息（每个特征与y）
    mi = mutual_info_regression(xs, ys.ravel())
    return corr, mi

def calculate_pairwise_correlations_and_mi(data, feature_names):
    """
    计算数据集中所有特征之间的相关系数和互信息

    特别处理扰动数据集中方差为0的节点（扰动节点在所有样本中值相同）

    Args:
        data: numpy数组，形状为(n_samples, n_features)
        feature_names: 特征名称列表

    Returns:
        dict: 包含相关系数矩阵和互信息矩阵的字典
    """
    n_features = data.shape[1]

    # 检查每个特征的方差，识别扰动节点（方差为0的节点）
    feature_variances = np.var(data, axis=0)
    zero_variance_features = np.where(feature_variances < 1e-10)[0]  # 使用小阈值避免数值误差

    if len(zero_variance_features) > 0:
        print(f"检测到方差为0的特征（可能是扰动节点）: {[feature_names[i] for i in zero_variance_features]}")

    # 计算相关系数矩阵
    corr_matrix = np.corrcoef(data, rowvar=False)

    # 处理方差为0的特征的相关系数
    for i in zero_variance_features:
        for j in range(n_features):
            if i != j:
                corr_matrix[i, j] = 0.0  # 方差为0的特征与其他特征的相关系数设为0
                corr_matrix[j, i] = 0.0

    # 计算互信息矩阵
    mi_matrix = np.zeros((n_features, n_features))
    for i in range(n_features):
        for j in range(n_features):
            if i == j:
                mi_matrix[i, j] = np.nan  # 自己与自己的互信息设为NaN
            elif i in zero_variance_features or j in zero_variance_features:
                # 如果任一特征方差为0，互信息为0
                mi_matrix[i, j] = 0.0
            else:
                # 计算特征i和特征j之间的互信息
                try:
                    mi_value = mutual_info_regression(data[:, [i]], data[:, j])
                    mi_matrix[i, j] = np.asarray(mi_value).item() if np.asarray(mi_value).size == 1 else np.asarray(mi_value)[0]
                except:
                    # 如果计算失败，设为0
                    mi_matrix[i, j] = 0.0

    return {
        'correlation_matrix': corr_matrix,
        'mutual_info_matrix': mi_matrix,
        'feature_names': feature_names,
        'zero_variance_features': [feature_names[i] for i in zero_variance_features]
    }

# 在三种数据集上分别计算目标节点与不同类型节点的相关系数和互信息
def analyze_target_relationships(data, feature_names, target_node_name):
    """
    分析目标节点Y与其他节点的关系，按照父节点、子节点、配偶节点、其他节点分类

    特别处理扰动数据集中方差为0的节点（扰动节点在所有样本中值相同）

    Args:
        data: numpy数组，形状为(n_samples, n_features)
        feature_names: 特征名称列表
        target_node_name: 目标节点名称

    Returns:
        dict: 包含不同类型节点关系的字典
    """
    if target_node_name not in feature_names:
        return None

    target_idx = feature_names.index(target_node_name)
    target_data = data[:, target_idx]

    # 检查每个特征的方差，识别扰动节点（方差为0的节点）
    feature_variances = np.var(data, axis=0)
    zero_variance_features = np.where(feature_variances < 1e-10)[0]  # 使用小阈值避免数值误差

    # 计算目标节点与所有其他节点的相关系数和互信息
    correlations = {}
    mutual_infos = {}

    for i, feature_name in enumerate(feature_names):
        if i != target_idx:  # 排除目标节点自己
            # 检查是否为方差为0的特征（扰动节点）
            if i in zero_variance_features or target_idx in zero_variance_features:
                # 如果当前特征或目标节点方差为0，相关系数和互信息都设为0
                correlations[feature_name] = 0.0
                mutual_infos[feature_name] = 0.0
                print(f"特征 {feature_name} 或目标节点 {target_node_name} 方差为0，相关系数和互信息设为0")
            else:
                # 相关系数
                try:
                    corr_matrix = np.corrcoef(data[:, i], target_data)
                    corr = corr_matrix[0, 1]
                    if np.isnan(corr):
                        corr = 0.0
                    correlations[feature_name] = corr
                except:
                    correlations[feature_name] = 0.0

                # 互信息
                try:
                    mi_value = mutual_info_regression(data[:, [i]], target_data)
                    mi = np.asarray(mi_value).item() if np.asarray(mi_value).size == 1 else np.asarray(mi_value)[0]
                    mutual_infos[feature_name] = mi
                except:
                    mutual_infos[feature_name] = 0.0

    return {
        'target_node': target_node_name,
        'correlations_with_target': correlations,
        'mutual_infos_with_target': mutual_infos
    }

# 获取节点关系分类
def categorize_nodes_by_relationship(scm, target_node_name):
    """
    根据DAG结构将节点分类为父节点、子节点、配偶节点、其他节点
    使用互斥分类确保节点类型不重叠

    Args:
        scm: SCM对象
        target_node_name: 目标节点名称

    Returns:
        dict: 包含不同类型节点列表的字典
    """
    if not hasattr(scm, 'dag') or target_node_name not in scm.dag.nodes():
        return {
            'parents': [],
            'children': [],
            'spouses': [],
            'others': []
        }

    # 使用互斥分类函数

    relationships = get_exclusive_node_relationships(scm.dag, target_node_name)

    return {
        'parents': list(relationships['parents']),
        'children': list(relationships['children']),
        'spouses': list(relationships['spouses']),
        'others': list(relationships['others'])
    }

def reorganize_feature_importance_by_node_type(importance_data, target_node, dag_nodes, dag_edges):
    """
    将特征重要性数据按节点类型重新组织

    Args:
        importance_data: 原始特征重要性数据 {'permutation': {...}, 'builtin': {...}}
        target_node: 目标节点
        dag_nodes: DAG节点列表
        dag_edges: DAG边列表

    Returns:
        重组后的特征重要性数据，按节点类型分组
    """
    if not importance_data or not target_node:
        return importance_data

    # 构建图并获取节点关系
    temp_dag = nx.DiGraph()
    temp_dag.add_nodes_from(dag_nodes)
    temp_dag.add_edges_from(dag_edges)

    if target_node not in temp_dag.nodes():
        return importance_data

    # 获取节点关系
    relationships = get_exclusive_node_relationships(temp_dag, target_node)

    # 重组数据结构
    reorganized_data = {}

    for importance_type in ['permutation', 'builtin']:
        if importance_type in importance_data:
            reorganized_data[importance_type] = {
                'parents': {},
                'children': {},
                'spouses': {},
                'others': {}
            }

            # 按节点类型分类特征重要性
            for feature_name, importance_value in importance_data[importance_type].items():
                if feature_name == target_node:
                    continue  # 跳过目标节点本身

                # 确定节点类型（按优先级）
                if feature_name in relationships['parents']:
                    node_type = 'parents'
                elif feature_name in relationships['children']:
                    node_type = 'children'
                elif feature_name in relationships['spouses']:
                    node_type = 'spouses'
                else:
                    node_type = 'others'

                reorganized_data[importance_type][node_type][feature_name] = importance_value

    return reorganized_data


def process_comprehensive_data_and_visualizations(scm_objects, results, snr_results, importance_results, image_dir, custom_functions_configs=None, save_json=True, generate_plots=True, correlation_results=None, mutual_info_results=None):
    """
    处理综合数据并生成可视化，文件保存与可视化解耦

    Args:
        scm_objects: SCM对象字典
        results: 模型结果列表
        snr_results: SNR结果列表
        importance_results: 特征重要性结果列表
        image_dir: 图像保存目录
        custom_functions_configs: 自定义函数配置字典
        save_json: 是否保存JSON文件
        generate_plots: 是否生成可视化图表
        correlation_results: 相关系数结果列表
        mutual_info_results: 互信息结果列表

    Returns:
        dict: 包含处理后的数据和结果信息
    """
    print(f"开始处理综合数据...")
    print(f"文件保存设置: JSON={save_json}, 可视化={generate_plots}")

    # 调用原有的数据处理函数，只控制JSON保存
    save_format = 'json' if save_json else 'none'

    # 调用原有函数进行数据处理
    result = process_and_save_comprehensive_data(
        scm_objects, results, snr_results, importance_results,
        image_dir, custom_functions_configs, save_format, generate_plots,
        correlation_results, mutual_info_results
    )

    return result

def process_and_save_comprehensive_data(scm_objects, results, snr_results, importance_results, image_dir, custom_functions_configs=None, save_format='json', generate_plots=True, correlation_results=None, mutual_info_results=None):
    """
    处理数据并同时完成保存和可视化，避免重复I/O操作

    Args:
        save_format: 'json'
        generate_plots: 是否同时生成可视化图表
        correlation_results: 相关系数结果列表
        mutual_info_results: 互信息结果列表
    """
    try:
        print(f"开始处理综合数据，保存格式: {save_format}")

        # 只有在需要保存JSON文件时才设置路径
        json_path = None

        if save_format == 'json':
            json_path = os.path.join(image_dir, 'comprehensive_results.json')

        # 从第一个scm_info中获取配置信息
        sample_config = None
        if scm_objects:
            first_dataset = next(iter(scm_objects.values()))
            if first_dataset:
                first_scm_info = next(iter(first_dataset.values()))
                sample_config = first_scm_info.get('config', {})

        # 新的层次化数据结构
        structured_data = {
            "metadata": {
                "total_datasets": len(scm_objects),
                "total_configs": len(custom_functions_configs) if custom_functions_configs else 0,
                "generation_time": "",
                "train_test_split": sample_config.get('train_test_split_ratio', 0.5) if sample_config else 0.5,
                # 添加噪声类型和分布参数
                "noise_config": sample_config.get('noise_config', {
                    "min_noise_std": 0.1,
                    "max_noise_std": 0.5,
                    "use_snr_method": False,
                    "snr_config": {},
                    "use_monte_carlo_precompute": False
                }) if sample_config else {
                    "min_noise_std": 0.1,
                    "max_noise_std": 0.5,
                    "use_snr_method": False,
                    "snr_config": {},
                    "use_monte_carlo_precompute": False
                },
                "root_distribution_config": sample_config.get('root_distribution_config', {
                    "root_distribution": 'gaussian',
                    "root_mean": 0.0,
                    "root_std": 1.0,
                    "sample_root_std": False,
                    "min_root": 0.0,
                    "max_root": 1.0,
                    "sample_cause_ranges": False
                }) if sample_config else {
                    "root_distribution": 'gaussian',
                    "root_mean": 0.0,
                    "root_std": 1.0,
                    "sample_root_std": False,
                    "min_root": 0.0,
                    "max_root": 1.0,
                    "sample_cause_ranges": False
                }
            },
            "results": {}
        }

        # 设置生成时间
        
        structured_data["metadata"]["generation_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        data_rows = []  # 保留用于CSV格式
        snr_plot_data = []  # 专门用于SNR可视化的数据
        total_datasets = len(scm_objects)
        processed_datasets = 0

        print(f"总共需要处理 {total_datasets} 个数据集，{structured_data['metadata']['total_configs']} 种配置")

        for dataset_idx, configs in scm_objects.items():
            processed_datasets += 1
            if processed_datasets % 10 == 0 or processed_datasets == total_datasets:
                print(f"处理进度: {processed_datasets}/{total_datasets} 数据集")

            for config_key, scm_info in configs.items():
                # 现在scm_info是序列化的信息字典而不是完整的SCM对象
                # 获取配置类型名称
                config_type_name = config_key
                if custom_functions_configs and config_key in custom_functions_configs:
                    config_type_name = custom_functions_configs[config_key]['name']

                # 初始化配置结构（如果不存在）
                if config_type_name not in structured_data["results"]:
                    structured_data["results"][config_type_name] = {
                        "config_info": {
                            "name": config_type_name,
                            "description": f"配置: {config_type_name}"
                        },
                        "datasets": {}
                    }

                # 初始化数据集结构
                dataset_name = f"dataset_{dataset_idx}"
                if dataset_name not in structured_data["results"][config_type_name]["datasets"]:
                    # 使用有序字典确保节点按拓扑序排列
                    structured_data["results"][config_type_name]["datasets"][dataset_name] = {
                        "dataset_info": {},
                        "nodes": OrderedDict(),  # 使用有序字典保持拓扑序
                        "model_results": {}
                    }

                # 填充数据集基础信息（只需要填充一次）
                current_dataset = structured_data["results"][config_type_name]["datasets"][dataset_name]
                if not current_dataset["dataset_info"]:  # 如果还没有填充过
                    current_dataset["dataset_info"] = {
                        'num_samples': scm_info.get('num_samples', 'N/A'),
                        'num_features': len(scm_info.get('selected_features', [])),
                        'num_nodes': len(scm_info.get('dag_nodes', [])) if 'dag_nodes' in scm_info else 'N/A'
                    }

                    # 根节点分布信息
                    if 'root_distribution_info' in scm_info and isinstance(scm_info['root_distribution_info'], dict):
                        root_dist_info = scm_info['root_distribution_info']
                        current_dataset["dataset_info"].update({
                            'root_distribution': root_dist_info.get('distribution_type', 'N/A'),
                            'root_mean': root_dist_info.get('mean', 'N/A'),
                            'root_actual_std': root_dist_info.get('std', 'N/A'),
                            'root_sample_std': root_dist_info.get('sample_std', 'N/A'),
                            'root_config_std': root_dist_info.get('configured_std', 'N/A'),
                            'root_min': root_dist_info.get('min_root', 'N/A'),
                            'root_max': root_dist_info.get('max_root', 'N/A'),
                            'sample_cause_ranges': root_dist_info.get('sample_cause_ranges', 'N/A')
                        })

                # 保留旧的数据行格式用于CSV（向后兼容）
                base_dataset_info = {
                    'dataset_id': dataset_idx,
                    'config_key': config_key,
                    'config_type': config_type_name,
                    'num_samples': current_dataset["dataset_info"]['num_samples'],
                    'num_features': current_dataset["dataset_info"]['num_features'],
                    'num_nodes': current_dataset["dataset_info"]['num_nodes']
                }

                # 添加DAG结构类型信息（如果存在）
                if 'dag_structure_type' in scm_info:
                    base_dataset_info['dag_structure_type'] = scm_info['dag_structure_type']

                # 根节点信息（用于CSV）
                root_info = {}
                if 'root_distribution' in current_dataset["dataset_info"]:
                    root_info = {
                        'root_distribution': current_dataset["dataset_info"]['root_distribution'],
                        'root_mean': current_dataset["dataset_info"]['root_mean'],
                        'root_actual_std': current_dataset["dataset_info"]['root_actual_std'],
                        'root_sample_std': current_dataset["dataset_info"]['root_sample_std'],
                        'root_config_std': current_dataset["dataset_info"]['root_config_std'],
                        'root_min': current_dataset["dataset_info"]['root_min'],
                        'root_max': current_dataset["dataset_info"]['root_max'],
                        'sample_cause_ranges': current_dataset["dataset_info"]['sample_cause_ranges']
                    }

                # 获取所有节点（从序列化信息）
                all_nodes = set()
                if 'dag_nodes' in scm_info:
                    all_nodes.update(scm_info['dag_nodes'])
                if 'snr_validation_results' in scm_info and scm_info['snr_validation_results']:
                    snr_results_data = scm_info['snr_validation_results']
                    all_nodes.update(snr_results_data.get('target_snr', {}).keys())
                if 'node_statistics' in scm_info and isinstance(scm_info['node_statistics'], dict):
                    all_nodes.update(scm_info['node_statistics'].keys())

                # 识别根节点（入度为0的节点）
                root_nodes = set()
                if 'dag_nodes' in scm_info and 'dag_edges' in scm_info:
                    all_dag_nodes = set(scm_info['dag_nodes'])
                    nodes_with_parents = set()
                    for parent, child in scm_info['dag_edges']:
                        nodes_with_parents.add(child)
                    root_nodes = all_dag_nodes - nodes_with_parents

                # 按拓扑序排列节点并获取节点类型标记
                ordered_nodes_with_types = []
                if 'dag_nodes' in scm_info and 'dag_edges' in scm_info:
                    # 构建NetworkX图对象进行拓扑排序
                    temp_dag = nx.DiGraph()
                    temp_dag.add_nodes_from(scm_info['dag_nodes'])
                    temp_dag.add_edges_from(scm_info['dag_edges'])

                    # 获取拓扑序
                    try:
                        topo_order = list(nx.topological_sort(temp_dag))
                    except nx.NetworkXError:
                        # 如果图有环，使用原始节点顺序
                        topo_order = sorted(all_nodes)

                    # 获取目标节点
                    target_node = scm_info.get('selected_target', None)

                    # 为每个节点按拓扑序分配类型标记
                    if target_node and target_node in temp_dag.nodes():
                        # 使用互斥分类函数获取节点关系
                        relationships = get_exclusive_node_relationships(temp_dag, target_node)

                        # 按拓扑序遍历节点并分配类型
                        for node in topo_order:
                            if node == target_node:
                                node_type = 'target'
                            elif node in relationships['parents']:
                                node_type = 'parent'
                            elif node in relationships['children']:
                                node_type = 'child'
                            elif node in relationships['spouses']:
                                node_type = 'spouse'
                            else:
                                node_type = 'other'

                            ordered_nodes_with_types.append((node, node_type))
                    else:
                        # 如果没有目标节点，所有节点都标记为other
                        for node in topo_order:
                            ordered_nodes_with_types.append((node, 'other'))
                else:
                    # 如果没有DAG信息，使用原始节点顺序
                    for node in sorted(all_nodes):
                        ordered_nodes_with_types.append((node, 'other'))

                # 为每个节点创建信息（按拓扑序）
                for node, node_type in ordered_nodes_with_types:
                    # 为新的结构化数据填充节点信息
                    if node not in current_dataset["nodes"]:
                        current_dataset["nodes"][node] = {
                            "node_type": node_type,      # 添加节点类型标记
                            "function_config": {},
                            "snr_info": {},
                            "data_statistics": {},
                            "correlation_info": {},  # 相关系数信息
                            "mutual_info": {}        # 互信息信息
                        }

                    current_node = current_dataset["nodes"][node]

                    # 确保节点类型信息存在（如果之前没有设置）
                    if "node_type" not in current_node:
                        current_node["node_type"] = node_type

                    # 节点函数配置（根节点没有函数配置，只有非根节点才有）
                    if node in root_nodes:
                        # 根节点没有函数配置，因为它们直接从分布中采样
                        current_node["function_config"] = None
                    elif 'assignment_info' in scm_info and node in scm_info['assignment_info']:
                        # 非根节点从assignment信息中提取函数配置
                        assignment_node_info = scm_info['assignment_info'][node]
                        current_node["function_config"] = assignment_node_info.get('function_config', {"type": "mlp"})
                    else:
                        # 如果没有assignment信息，设置默认配置（仅对非根节点）
                        current_node["function_config"] = {"type": "mlp"}

                    # 为CSV格式保留旧的行数据
                    row_data = base_dataset_info.copy()
                    row_data.update(root_info)
                    row_data['node_name'] = node
                    row_data['node_type'] = node_type  # 添加节点类型到CSV数据

                    # 处理函数配置（根节点没有函数配置）
                    if current_node["function_config"] is None:
                        # 根节点没有函数配置
                        row_data['function_type'] = None
                    else:
                        row_data['function_type'] = current_node["function_config"].get("type", "mlp")
                        # 添加函数参数到行数据
                        for param_name, param_value in current_node["function_config"].items():
                            if param_name != 'type':
                                row_data[f'func_{param_name}'] = param_value

                    # SNR信息（从序列化信息）
                    if 'snr_validation_results' in scm_info and scm_info['snr_validation_results']:
                        snr_results_data = scm_info['snr_validation_results']
                        target_snr = snr_results_data.get('target_snr', {})
                        configured_noise_std = snr_results_data.get('configured_noise_std', {})
                        actual_noise_std = snr_results_data.get('actual_noise_std', {})
                        actual_snr = snr_results_data.get('actual_snr', {})
                        signal_var = snr_results_data.get('signal_var', {})
                        signal_mean = snr_results_data.get('signal_mean', {})
                        config_noise_mean = snr_results_data.get('config_noise_mean', {})
                        calculated_noise_mean_target = snr_results_data.get('calculated_noise_mean_target', {})
                        actual_noise_mean = snr_results_data.get('actual_noise_mean', {})

                        # 为新结构填充SNR信息
                        node_target_snr = target_snr.get(node, None)
                        node_configured_noise_std = configured_noise_std.get(node, None)
                        node_actual_snr = actual_snr.get(node, None)

                        current_node["snr_info"] = {
                            "target_snr": node_target_snr,
                            "configured_noise_std": node_configured_noise_std,
                            "actual_noise_std": actual_noise_std.get(node, None),
                            "actual_snr": node_actual_snr,
                            "signal_var": signal_var.get(node, None),
                            "signal_mean": signal_mean.get(node, None),
                            "config_noise_mean": config_noise_mean.get(node, None),
                            "calculated_noise_mean_target": calculated_noise_mean_target.get(node, None),
                            "actual_noise_mean": actual_noise_mean.get(node, None)
                        }

                        # 为CSV格式处理SNR数据
                        if node_target_snr is not None:
                            calculated_noise_std_target = node_configured_noise_std
                            config_noise_std = None
                            calculated_noise_std_sample = None
                        else:
                            config_noise_std = node_configured_noise_std
                            calculated_noise_std_target = None
                            calculated_noise_std_sample = None

                        snr_data = {
                            'target_snr': node_target_snr,
                            'config_noise_std': config_noise_std,
                            'calculated_noise_std_sample': calculated_noise_std_sample,
                            'calculated_noise_std_target': calculated_noise_std_target,
                            'actual_noise_std': actual_noise_std.get(node, None),
                            'actual_snr': node_actual_snr,
                            'signal_var': signal_var.get(node, None),
                            'signal_mean': signal_mean.get(node, None),
                            'config_noise_mean': config_noise_mean.get(node, None),
                            'calculated_noise_mean_target': calculated_noise_mean_target.get(node, None),
                            'actual_noise_mean': actual_noise_mean.get(node, None)
                        }
                        row_data.update(snr_data)
                        # print(f"DEBUG: 添加了SNR数据到节点{node}: {list(snr_data.keys())}")

                        # 收集SNR可视化数据
                        if node_actual_snr is not None:
                            snr_plot_data.append({
                                'config_type': config_type_name,
                                'node_name': node,
                                'actual_snr': node_actual_snr,
                                'dataset_id': dataset_idx
                            })
                    else:
                        # 如果没有SNR验证结果，添加默认的SNR字段（值为None）
                        default_snr_data = {
                            'target_snr': None,
                            'config_noise_std': None,
                            'calculated_noise_std_sample': None,
                            'calculated_noise_std_target': None,
                            'actual_noise_std': None,
                            'actual_snr': None,
                            'signal_var': None,
                            'signal_mean': None,
                            'config_noise_mean': None,
                            'calculated_noise_mean_target': None,
                            'actual_noise_mean': None
                        }
                        row_data.update(default_snr_data)
                        print(f"DEBUG: 添加了默认SNR数据到节点{node}: {list(default_snr_data.keys())}")

                    # 节点统计信息（从序列化信息）
                    if 'node_statistics' in scm_info and isinstance(scm_info['node_statistics'], dict):
                        node_stats = scm_info['node_statistics']
                        if node in node_stats:
                            stats = node_stats[node]

                            # 为新结构填充统计信息
                            current_node["data_statistics"] = {
                                'min': stats['min'],
                                'q25': stats['q25'],
                                'median': stats['median'],
                                'q75': stats['q75'],
                                'max': stats['max'],
                                'mean': stats['mean'],
                                'std': stats['std']
                            }

                            # 为CSV格式添加统计信息
                            row_data.update({
                                'data_min': stats['min'],
                                'data_q25': stats['q25'],
                                'data_median': stats['median'],
                                'data_q75': stats['q75'],
                                'data_max': stats['max'],
                                'data_mean': stats['mean'],
                                'data_std': stats['std']
                            })

                    # 处理相关系数和互信息数据（只有在有数据时才处理）
                    if correlation_results and mutual_info_results and len(correlation_results) > 0 and len(mutual_info_results) > 0:
                        # 查找当前数据集的相关系数和互信息结果
                        expected_dataset_name = f'dataset_{dataset_idx}'
                        dataset_corr_result = None
                        dataset_mi_result = None

                        for corr_result in correlation_results:
                            if (corr_result['dataset'] == expected_dataset_name and
                                corr_result['config_key'] == config_key):
                                dataset_corr_result = corr_result
                                break

                        for mi_result in mutual_info_results:
                            if (mi_result['dataset'] == expected_dataset_name and
                                mi_result['config_key'] == config_key):
                                dataset_mi_result = mi_result
                                break

                        # 如果找到了相关系数和互信息数据，添加到节点信息中
                        if dataset_corr_result and dataset_mi_result:
                            # 处理目标节点相关的分类数据（三种数据集分别处理）
                            if (dataset_corr_result.get('target_correlations') and
                                dataset_mi_result.get('target_mutual_infos')):

                                # 合并三种数据集的结果
                                target_corr = dataset_corr_result['target_correlations']
                                target_mi = dataset_mi_result['target_mutual_infos']

                                # 为当前节点计算它自己的相关系数和互信息信息
                                # 从原始的相关系数和互信息矩阵中提取数据
                                node_specific_corr = {}
                                node_specific_mi = {}

                                # 获取当前节点与其他节点的关系分类
                                # 使用互斥分类确保节点类型不重叠
                                current_node_relationships = {}
                                if 'dag_nodes' in scm_info and 'dag_edges' in scm_info:
                                    # 构建NetworkX图对象
                                    temp_dag = nx.DiGraph()
                                    temp_dag.add_nodes_from(scm_info['dag_nodes'])
                                    temp_dag.add_edges_from(scm_info['dag_edges'])

                                    # 使用互斥分类函数
                                    current_node_relationships = get_exclusive_node_relationships(temp_dag, node)

                                    # 转换为列表格式以保持兼容性
                                    current_node_parents = list(current_node_relationships['parents'])
                                    current_node_children = list(current_node_relationships['children'])
                                    current_node_spouses = list(current_node_relationships['spouses'])
                                    current_node_others = list(current_node_relationships['others'])

                                    # 从原始的相关系数和互信息矩阵中提取当前节点的数据
                                    correlation_matrices = dataset_corr_result.get('correlation_matrices', {})
                                    mutual_info_matrices = dataset_mi_result.get('mutual_info_matrices', {})
                                    feature_names = dataset_corr_result.get('feature_names', [])

                                    if node in feature_names:
                                        node_idx = feature_names.index(node)

                                        # 为每种数据集类型提取当前节点的相关系数和互信息
                                        for split_name in ['train', 'test', 'intervention']:
                                            if (split_name in correlation_matrices and
                                                split_name in mutual_info_matrices):

                                                corr_matrix = correlation_matrices[split_name]
                                                mi_matrix = mutual_info_matrices[split_name]

                                                node_specific_corr[split_name] = {
                                                    'parents': {},
                                                    'children': {},
                                                    'spouses': {},
                                                    'others': {}
                                                }
                                                node_specific_mi[split_name] = {
                                                    'parents': {},
                                                    'children': {},
                                                    'spouses': {},
                                                    'others': {}
                                                }

                                                # 遍历所有其他节点，根据关系分类
                                                for other_node in feature_names:
                                                    if other_node != node:
                                                        other_idx = feature_names.index(other_node)

                                                        # 获取相关系数和互信息值
                                                        corr_value = corr_matrix[node_idx, other_idx] if corr_matrix is not None else 0.0
                                                        mi_value = mi_matrix[node_idx, other_idx] if mi_matrix is not None else 0.0

                                                        # 根据当前节点的关系分类
                                                        if other_node in current_node_parents:
                                                            node_specific_corr[split_name]['parents'][other_node] = corr_value
                                                            node_specific_mi[split_name]['parents'][other_node] = mi_value
                                                        elif other_node in current_node_children:
                                                            node_specific_corr[split_name]['children'][other_node] = corr_value
                                                            node_specific_mi[split_name]['children'][other_node] = mi_value
                                                        elif other_node in current_node_spouses:
                                                            node_specific_corr[split_name]['spouses'][other_node] = corr_value
                                                            node_specific_mi[split_name]['spouses'][other_node] = mi_value
                                                        else:
                                                            node_specific_corr[split_name]['others'][other_node] = corr_value
                                                            node_specific_mi[split_name]['others'][other_node] = mi_value

                                # 将节点特定的数据添加到节点信息中
                                current_node["correlation_info"] = node_specific_corr if node_specific_corr else {}
                                current_node["mutual_info"] = node_specific_mi if node_specific_mi else {}

                                # 为CSV格式添加相关系数和互信息的统计信息（合并三种数据集的结果）
                                # 使用节点特定的数据而不是目标节点的数据

                                # 使用节点特定的数据进行统计计算
                                node_corr_data = node_specific_corr if node_specific_corr else target_corr
                                node_mi_data = node_specific_mi if node_specific_mi else target_mi

                                for category in ['parents', 'children', 'spouses', 'others']:
                                    # 收集该类别下所有节点在三种数据集上的数值
                                    category_corr_values = []
                                    category_mi_values = []

                                    # 合并三种数据集的结果
                                    for split_name in ['train', 'test', 'intervention']:
                                        if split_name in node_corr_data and category in node_corr_data[split_name]:
                                            category_corr_values.extend(node_corr_data[split_name][category].values())

                                        if split_name in node_mi_data and category in node_mi_data[split_name]:
                                            category_mi_values.extend(node_mi_data[split_name][category].values())

                                    # 计算统计信息
                                    if category_corr_values:
                                        row_data[f'corr_{category}_mean'] = np.mean(category_corr_values)
                                        row_data[f'corr_{category}_std'] = np.std(category_corr_values) if len(category_corr_values) > 1 else 0.0
                                    else:
                                        row_data[f'corr_{category}_mean'] = None
                                        row_data[f'corr_{category}_std'] = None

                                    if category_mi_values:
                                        row_data[f'mi_{category}_mean'] = np.mean(category_mi_values)
                                        row_data[f'mi_{category}_std'] = np.std(category_mi_values) if len(category_mi_values) > 1 else 0.0
                                    else:
                                        row_data[f'mi_{category}_mean'] = None
                                        row_data[f'mi_{category}_std'] = None

                # 处理模型结果（在节点循环外，避免重复处理）
                dataset_results = []
                expected_dataset_name = f'dataset_{dataset_idx}'
                for r in results:
                    if (r['dataset'] == expected_dataset_name and r['use_snr'] == config_key):
                        dataset_results.append(r)

                # 为每个模型创建结果
                for result in dataset_results:
                    model_type = result['model_type']

                    # 为新结构填充模型结果
                    if model_type not in current_dataset["model_results"]:
                        current_dataset["model_results"][model_type] = {
                            "performance": {},
                            "feature_importance": {}
                        }

                    current_model = current_dataset["model_results"][model_type]
                    current_model["performance"] = {
                        'r2_train': result['r2_train'],
                        'r2_test': result['r2_test'],
                        'r2_intervention': result['r2_intv'],
                        'rmse_train': result['rmse_train'],
                        'rmse_test': result['rmse_test'],
                        'rmse_intervention': result['rmse_intv'],
                        'mape_train': result['mape_train'],
                        'mape_test': result['mape_test'],
                        'mape_intervention': result['mape_intv']
                    }

                    # 处理特征重要性
                    importance_data = []
                    for imp in importance_results:
                        if (imp['dataset'] == expected_dataset_name and
                            imp['use_snr'] == config_key and
                            imp['model_type'] == model_type):
                            importance_data.append(imp)

                    if importance_data:
                        imp_result = importance_data[0]

                        # 获取目标节点和DAG信息
                        target_node = scm_info.get('selected_target', None)
                        dag_nodes = scm_info.get('dag_nodes', [])
                        dag_edges = scm_info.get('dag_edges', [])

                        # 重组特征重要性数据按节点类型分组
                        reorganized_importance = reorganize_feature_importance_by_node_type(
                            imp_result['importance'], target_node, dag_nodes, dag_edges
                        )

                        current_model["feature_importance"] = reorganized_importance

                    # 为CSV格式创建每个节点的行（保持向后兼容）
                    for node in all_nodes:
                        final_row = base_dataset_info.copy()
                        final_row.update(root_info)
                        final_row['node_name'] = node

                        # 添加节点相关信息
                        node_info = None
                        if node in current_dataset["nodes"]:
                            node_info = current_dataset["nodes"][node]
                            if node_info["function_config"] is None:
                                # 根节点没有函数配置
                                final_row['function_type'] = None
                            else:
                                final_row['function_type'] = node_info["function_config"].get("type", "mlp")
                                # 添加函数参数
                                for param_name, param_value in node_info["function_config"].items():
                                    if param_name != 'type':
                                        final_row[f'func_{param_name}'] = param_value
                        else:
                            # 如果节点信息不存在，尝试从assignment_info中获取
                            if 'assignment_info' in scm_info and node in scm_info['assignment_info']:
                                assignment_node_info = scm_info['assignment_info'][node]
                                func_config = assignment_node_info.get('function_config', {"type": "mlp"})
                                final_row['function_type'] = func_config.get("type", "mlp")

                                # 添加函数参数
                                for param_name, param_value in func_config.items():
                                    if param_name != 'type':
                                        final_row[f'func_{param_name}'] = param_value
                            elif node in root_nodes:
                                # 根节点没有函数配置
                                final_row['function_type'] = None
                            else:
                                # 非根节点默认为mlp
                                final_row['function_type'] = "mlp"

                        # 添加SNR信息（使用与JSON保存相同的逻辑）
                        if 'snr_validation_results' in scm_info and scm_info['snr_validation_results']:
                            snr_results_data = scm_info['snr_validation_results']
                            target_snr = snr_results_data.get('target_snr', {})
                            configured_noise_std = snr_results_data.get('configured_noise_std', {})
                            actual_noise_std = snr_results_data.get('actual_noise_std', {})
                            actual_snr = snr_results_data.get('actual_snr', {})
                            signal_var = snr_results_data.get('signal_var', {})
                            signal_mean = snr_results_data.get('signal_mean', {})
                            config_noise_mean = snr_results_data.get('config_noise_mean', {})
                            calculated_noise_mean_target = snr_results_data.get('calculated_noise_mean_target', {})
                            actual_noise_mean = snr_results_data.get('actual_noise_mean', {})

                            # 获取当前节点的SNR数据
                            node_target_snr = target_snr.get(node, None)
                            node_configured_noise_std = configured_noise_std.get(node, None)
                            node_actual_snr = actual_snr.get(node, None)

                            # 处理SNR数据的逻辑（与row_data.update相同）
                            if node_target_snr is not None:
                                calculated_noise_std_target = node_configured_noise_std
                                config_noise_std = None
                                calculated_noise_std_sample = None
                            else:
                                config_noise_std = node_configured_noise_std
                                calculated_noise_std_target = None
                                calculated_noise_std_sample = None

                            final_row.update({
                                'target_snr': node_target_snr,
                                'config_noise_std': config_noise_std,
                                'calculated_noise_std_sample': calculated_noise_std_sample,
                                'calculated_noise_std_target': calculated_noise_std_target,
                                'configured_noise_std': node_configured_noise_std,
                                'actual_noise_std': actual_noise_std.get(node, None),
                                'actual_snr': node_actual_snr,
                                'signal_var': signal_var.get(node, None),
                                'signal_mean': signal_mean.get(node, None),
                                'config_noise_mean': config_noise_mean.get(node, None),
                                'calculated_noise_mean_target': calculated_noise_mean_target.get(node, None),
                                'actual_noise_mean': actual_noise_mean.get(node, None)
                            })
                        else:
                            # 如果没有SNR验证结果，添加默认的SNR字段（值为None）
                            final_row.update({
                                'target_snr': None,
                                'config_noise_std': None,
                                'calculated_noise_std_sample': None,
                                'calculated_noise_std_target': None,
                                'configured_noise_std': None,
                                'actual_noise_std': None,
                                'actual_snr': None,
                                'signal_var': None,
                                'signal_mean': None,
                                'config_noise_mean': None,
                                'calculated_noise_mean_target': None,
                                'actual_noise_mean': None
                            })

                        # 添加统计信息
                        if node_info and "data_statistics" in node_info:
                            for stat_key, stat_value in node_info["data_statistics"].items():
                                final_row[f'data_{stat_key}'] = stat_value

                        # 添加模型性能信息
                        final_row.update({
                            'model_type': model_type,
                            'r2_train': result['r2_train'],
                            'r2_test': result['r2_test'],
                            'r2_intervention': result['r2_intv'],
                            'rmse_train': result['rmse_train'],
                            'rmse_test': result['rmse_test'],
                            'rmse_intervention': result['rmse_intv'],
                            'mape_train': result['mape_train'],
                            'mape_test': result['mape_test'],
                            'mape_intervention': result['mape_intv']
                        })

                        # 添加特征重要性信息
                        if importance_data:
                            imp_result = importance_data[0]
                            if 'permutation' in imp_result['importance']:
                                for feature_name, importance_value in imp_result['importance']['permutation'].items():
                                    final_row[f'perm_importance_{feature_name}'] = importance_value
                            if 'builtin' in imp_result['importance']:
                                for feature_name, importance_value in imp_result['importance']['builtin'].items():
                                    final_row[f'builtin_importance_{feature_name}'] = importance_value

                        data_rows.append(final_row)

        # 保存数据
        print(f"准备保存数据...")
        print(f"  - 结构化数据: {len(structured_data['results'])} 个配置")
        print(f"  - CSV行数据: {len(data_rows)} 行")

        # 保存文件（只支持JSON格式）
        if save_format == 'json':
            print("正在保存JSON文件...")

            # 自定义JSON编码器处理numpy类型
            class NumpyEncoder(json.JSONEncoder):
                    def default(self, obj):
                        if isinstance(obj, np.integer):
                            return int(obj)
                        elif isinstance(obj, np.floating):
                            return float(obj)
                        elif isinstance(obj, np.ndarray):
                            return obj.tolist()
                        return super(NumpyEncoder, self).default(obj)

            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(structured_data, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
            print(f"已保存层次化JSON结果到: {json_path}")
            print(f"JSON文件包含 {len(structured_data['results'])} 个配置的完整数据")
        else:
            print("跳过文件保存（save_format='none'）")

        # 同时生成SNR可视化（如果有SNR数据且需要生成图表）
        if generate_plots and snr_plot_data:
            print(f"正在生成SNR可视化图表，使用 {len(snr_plot_data)} 个SNR数据点...")
            try:
                # 获取配置类型
                config_types = sorted(list(set(item['config_type'] for item in snr_plot_data)))
                generate_snr_plot_from_data(snr_plot_data, config_types, image_dir)
            except Exception as e:
                print(f"生成SNR可视化时出错: {e}")

        # 返回数据以供其他用途
        return {
            'structured_data': structured_data,  # 新的层次化数据
            'data_rows': data_rows,              # 旧的CSV行数据（向后兼容）
            'snr_plot_data': snr_plot_data       # SNR可视化数据
        }

    except Exception as e:
        print(f"保存CSV文件时出错: {str(e)}")

        traceback.print_exc()

def generate_snr_plot_from_data(snr_plot_data, config_types, image_dir):
    """
    直接从内存中的SNR数据生成可视化图表

    Args:
        snr_plot_data: SNR数据列表，每个元素包含 config_type, node_name, actual_snr, dataset_id
        config_types: 配置类型列表
        image_dir: 图片保存目录
    """
    try:

        # 转换为DataFrame便于处理
        df = pd.DataFrame(snr_plot_data)

        # 过滤掉actual_snr为空的行
        df_filtered = df.dropna(subset=['actual_snr'])
        if df_filtered.empty:
            print("没有有效的actual_snr数据，跳过按节点分组的SNR对比图")
            return

        # 获取所有节点名称
        all_nodes = sorted(df_filtered['node_name'].unique())
        n_nodes = len(all_nodes)

        if n_nodes == 0:
            print("没有找到节点数据，跳过按节点分组的SNR对比图")
            return

        # 创建图表，增加高度以容纳标签
        fig_width = max(12, len(config_types) * 3)
        fig_height = 8  # 增加高度
        plt.figure(figsize=(fig_width, fig_height))

        # 为每个配置类型和节点组合准备数据
        box_data = []
        box_labels = []
        box_colors = []

        # 定义节点颜色
        node_colors = plt.cm.tab10(np.linspace(0, 1, n_nodes))

        # 为每个配置类型收集所有节点的数据
        for config_type in config_types:
            for node_idx, node_name in enumerate(all_nodes):
                # 获取该配置类型和节点的所有actual_snr值
                node_data = df_filtered[
                    (df_filtered['config_type'] == config_type) &
                    (df_filtered['node_name'] == node_name)
                ]['actual_snr'].values

                if len(node_data) > 0:
                    box_data.append(node_data)
                    box_labels.append(f'{config_type}\n{node_name}')
                    box_colors.append(node_colors[node_idx])

        if box_data:
            # 绘制箱线图
            bp = plt.boxplot(box_data, labels=box_labels, patch_artist=True,
                            showmeans=True, meanline=True)

            # 设置颜色
            for i, patch in enumerate(bp['boxes']):
                patch.set_facecolor(box_colors[i])
                patch.set_alpha(0.7)

            # 添加均值和标准差标注，紧贴100%分位数（最大值）上方
            for i, values in enumerate(box_data):
                if len(values) > 0:
                    mean_val = np.mean(values)
                    std_val = np.std(values) if len(values) > 1 else 0
                    # 获取100%分位数（最大值）并紧贴其上方标注
                    max_val = max(np.max(values), 0.2)
                    y_pos = max_val + (max_val * 0.01)  # 紧贴最大值上方，只留1%的微小间距
                    plt.text(i+1, y_pos,
                            f'{mean_val:.2f}±{std_val:.2f}',
                            ha='center', va='bottom', fontsize=8)

        plt.ylabel('Real Mean SNR')
        plt.title(f'SNR Comparison by Node Across {len(config_types)} Custom Function Configurations\n(Generated from Memory Data - Boxplot with Mean and Std)',
                 pad=20)  # 增加标题间距
        plt.grid(True, alpha=0.3)

        # 旋转x轴标签以避免重叠
        plt.xticks(rotation=45, ha='right')

        # 调整布局，留出更多空间给标签
        plt.subplots_adjust(bottom=0.2, top=0.9)
        plt.savefig(os.path.join(image_dir, 'merged_custom_functions_snr_comparison_by_node.png'),
                   dpi=300, bbox_inches='tight', pad_inches=0.2)
        plt.close()

        print(f"已生成按节点分组的SNR对比图: merged_custom_functions_snr_comparison_by_node.png")

    except Exception as e:
        print(f"生成SNR可视化时出错: {e}")
        traceback.print_exc()


#-------------------------------------------------------------other-----------------------------------------------
def save_visualization_summary_csv(image_dir, data_rows, custom_dag_type=None, dag_generation_seeds=None,
                                  results=None, r2_threshold=0.5):
    """
    从内存中的comprehensive_results数据统计汇总信息，生成可视化汇总表格CSV文件

    统计每个配置下（每个节点的SNR/函数形式和参数）生成的n个数据集的：
    1. 不同模型在训练集、原始测试集、和干预集上预测结果的均值和标准差
    2. 每个节点在每种预测模型中permutation_importance的均值和标准差
    3. 有效数据集（R2>阈值）上的模型性能指标均值和标准差

    注意：当custom_dag_type是'random_ER', 'random_SF'或者是None，且没有给'dag_generation'一个具体的值时，
    同一个函数和信噪比生成的n个数据集的DAG都是不一样的，
    这时候只保存模型性能指标，跳过节点级别的信息。

    Args:
        image_dir: 图片保存目录
        data_rows: 内存中的数据行列表（必需参数）
        custom_dag_type: 自定义DAG类型
        dag_generation_seeds: DAG生成的种子配置
        results: 模型结果列表，用于计算有效数据集
        r2_threshold: R2阈值，用于筛选有效数据集
    """
    if not data_rows:
        print("没有数据可用于生成可视化汇总CSV文件")
        return

    try:
        print(f"使用内存中的数据生成汇总，包含 {len(data_rows)} 行")
        df = pd.DataFrame(data_rows)

        if df.empty:
            print("comprehensive_results.csv文件为空")
            return

        print(f"原始数据包含 {len(df)} 行，{len(df.columns)} 列")
        print(f"DEBUG: 原始数据的所有列: {list(df.columns)}")

        # 计算有效数据集（如果提供了results）
        effective_datasets = {}
        if results is not None:
            _, effective_datasets = calculate_effective_datasets(results, r2_threshold)
            print(f"计算得到有效数据集: {len(effective_datasets)} 个配置")

        # 检查是否有SNR相关字段
        snr_related_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in ['snr', 'noise', 'signal'])]
        print(f"DEBUG: 原始数据中的SNR相关列: {snr_related_cols}")

        # 判断是否跳过节点级别信息
        # 当custom_dag_type是'random_ER', 'random_SF'或者是None，且没有给'dag_generation'一个具体的值时，
        # 同一个函数和信噪比生成的n个数据集的DAG都是不一样的，这时候跳过节点级别信息
        skip_node_level_info = False

        if custom_dag_type in ['random_ER', 'random_SF', None]:
            # 检查是否有具体的dag_generation种子配置
            has_specific_dag_seeds = (dag_generation_seeds is not None and
                                    isinstance(dag_generation_seeds, list) and
                                    len(dag_generation_seeds) > 0)

            if not has_specific_dag_seeds:
                skip_node_level_info = True
                print(f"检测到随机DAG类型 '{custom_dag_type}' 且无具体种子配置，将跳过节点级别信息")
            else:
                print(f"检测到随机DAG类型 '{custom_dag_type}' 但有具体种子配置，保留节点级别信息")

        # 根据是否跳过节点级别信息选择分组方式
        if skip_node_level_info:
            # 只按配置类型和模型类型分组，跳过节点名称
            groupby_columns = ['config_type', 'model_type']
            print("使用简化分组模式（跳过节点级别信息）")
        else:
            # 按配置类型、节点名称、模型类型分组进行统计
            groupby_columns = ['config_type', 'node_name', 'model_type']
            print("使用完整分组模式（包含节点级别信息）")

        # 检查分组列是否存在
        missing_groupby_columns = [col for col in groupby_columns if col not in df.columns]
        if missing_groupby_columns:
            print(f"缺少分组列: {missing_groupby_columns}")
            print(f"可用的列: {list(df.columns)}")
            return

        # 定义需要统计的性能指标列
        performance_columns = [
            'r2_train', 'r2_test', 'r2_intervention',
            'rmse_train', 'rmse_test', 'rmse_intervention',
            'mape_train', 'mape_test', 'mape_intervention'
        ]

        # 定义需要统计的SNR相关列（与数据行中保存的字段一致）
        snr_columns = [
            'target_snr', 'config_noise_std', 'calculated_noise_std_sample', 'calculated_noise_std_target',
            'actual_noise_std', 'actual_snr',
            'signal_var', 'signal_mean', 'config_noise_mean',
            'calculated_noise_mean_target', 'actual_noise_mean'
        ]

        # 检查哪些列存在
        available_performance_columns = [col for col in performance_columns if col in df.columns]
        available_snr_columns = [col for col in snr_columns if col in df.columns]

        # 找到permutation importance列
        perm_importance_columns = [col for col in df.columns if col.startswith('perm_importance_')]

        print(f"可用的性能指标列: {available_performance_columns}")
        print(f"可用的SNR相关列: {available_snr_columns}")
        print(f"可用的permutation importance列: {perm_importance_columns}")

        # 进行分组统计
        summary_rows = []

        for group_key, group_df in df.groupby(groupby_columns):
            if skip_node_level_info:
                config_type, model_type = group_key
                # 计算该配置下的实际数据集数量（去重dataset_id）
                n_datasets = len(group_df['dataset_id'].unique())
                # 基础信息（跳过节点级别信息）
                row_data = {
                    'config_type': config_type,
                    'model_type': model_type,
                    'n_datasets': n_datasets  # 该配置下的数据集数量
                }
            else:
                config_type, node_name, model_type = group_key
                # 计算该配置下的实际数据集数量（去重dataset_id）
                n_datasets = len(group_df['dataset_id'].unique())
                # 基础信息（包含节点级别信息）
                row_data = {
                    'config_type': config_type,
                    'node_name': node_name,
                    'model_type': model_type,
                    'n_datasets': n_datasets  # 该配置下的数据集数量
                }

            # 只有在不跳过节点级别信息时才统计SNR相关指标
            if not skip_node_level_info:
                # 统计SNR相关指标（保存为均值±标准差格式）
                for col in available_snr_columns:
                    if col in group_df.columns:
                        values = group_df[col].dropna()
                        if len(values) > 0:
                            mean_val = values.mean()
                            std_val = values.std() if len(values) > 1 else 0.0
                            # 保存为组合格式：均值±标准差
                            row_data[col] = f"{mean_val:.4f}±{std_val:.4f}"

            # 统计性能指标（保存为均值±标准差格式）
            for col in available_performance_columns:
                if col in group_df.columns:
                    values = group_df[col].dropna()
                    if len(values) > 0:
                        mean_val = values.mean()
                        std_val = values.std() if len(values) > 1 else 0.0
                        # 保存为组合格式：均值±标准差
                        row_data[col] = f"{mean_val:.4f}±{std_val:.4f}"

                        # 计算有效数据集上的性能指标
                        if effective_datasets and config_type in effective_datasets:
                            # 筛选有效数据集的数据
                            effective_dataset_names = effective_datasets[config_type]
                            if 'dataset_name' in group_df.columns:
                                effective_mask = group_df['dataset_name'].isin(effective_dataset_names)
                                effective_values = group_df[effective_mask][col].dropna()

                                if len(effective_values) > 0:
                                    eff_mean_val = effective_values.mean()
                                    eff_std_val = effective_values.std() if len(effective_values) > 1 else 0.0
                                    # 保存有效数据集的统计信息
                                    row_data[f"{col}_effective"] = f"{eff_mean_val:.4f}±{eff_std_val:.4f}"
                                    row_data[f"{col}_effective_count"] = len(effective_values)

            # 只有在不跳过节点级别信息时才统计permutation importance
            if not skip_node_level_info:
                # 统计permutation importance（保存为均值±标准差格式）
                for col in perm_importance_columns:
                    if col in group_df.columns:
                        values = group_df[col].dropna()
                        if len(values) > 0:
                            mean_val = values.mean()
                            std_val = values.std() if len(values) > 1 else 0.0
                            # 保存为组合格式：均值±标准差
                            row_data[col] = f"{mean_val:.4f}±{std_val:.4f}"

            summary_rows.append(row_data)

        if not summary_rows:
            print("没有数据可以进行统计汇总")
            return

        # 创建汇总DataFrame
        summary_df = pd.DataFrame(summary_rows)

        # 根据是否跳过节点级别信息选择排序列
        if skip_node_level_info:
            # 按配置类型、模型类型排序
            summary_df = summary_df.sort_values(['config_type', 'model_type'])
        else:
            # 按配置类型、节点名称、模型类型排序
            summary_df = summary_df.sort_values(['config_type', 'node_name', 'model_type'])

        # 保存汇总CSV文件
        summary_csv_path = os.path.join(image_dir, 'visualization_summary.csv')
        summary_df.to_csv(summary_csv_path, index=False, encoding='utf-8')

        print(f"已保存可视化汇总CSV文件到: {summary_csv_path}")
        print(f"汇总CSV文件包含 {len(summary_df)} 行数据，{len(summary_df.columns)} 列")

        return summary_csv_path

    except Exception as e:
        print(f"生成可视化汇总CSV文件时出错: {str(e)}")
        traceback.print_exc()
        return None

def save_enhanced_txt_file(scm_objects, results, snr_results, importance_results, image_dir):
    """

    Args:
        scm_objects: SCM对象字典
        results: 模型性能结果
        snr_results: SNR验证结果
        importance_results: 特征重要性结果
        image_dir: 保存目录
    """
    txt_path = os.path.join(image_dir, 'comprehensive_results.txt')

    with open(txt_path, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("综合实验结果报告\n")
        f.write("=" * 80 + "\n\n")

        # 1. 图结构分析
        f.write("1. 图结构分析\n")
        f.write("-" * 40 + "\n")
        structure_analysis = compare_graph_structures(scm_objects)
        f.write(f"所有数据集图结构是否相同: {structure_analysis['same_structure']}\n")
        f.write(f"结构组数量: {len(structure_analysis['structure_groups'])}\n\n")

        for i, group in enumerate(structure_analysis['structure_groups']):
            f.write(f"结构组 {i+1}: {len(group)} 个数据集配置\n")
            for dataset_idx, config_key in group:
                f.write(f"  - 数据集 {dataset_idx}, 配置 {config_key}\n")
            f.write("\n")

        # 1.5. 根节点采样分布信息
        f.write("1.5. 根节点采样分布信息\n")
        f.write("-" * 40 + "\n")
        for dataset_idx, configs in scm_objects.items():
            f.write(f"数据集 {dataset_idx}:\n")
            for config_key, scm_info in configs.items():
                try:
                    if 'root_distribution_info' in scm_info and isinstance(scm_info['root_distribution_info'], dict):
                        root_info = scm_info['root_distribution_info']
                        f.write(f"  配置 {config_key}:\n")
                        f.write(f"    分布类型: {root_info.get('distribution_type', 'N/A')}\n")
                        if root_info.get('distribution_type') == 'gaussian':
                            f.write(f"    均值: {root_info.get('mean', 'N/A')}\n")
                            std_val = root_info.get('std')
                            if std_val is not None:
                                f.write(f"    实际标准差: {std_val:.6f}\n")
                            else:
                                f.write(f"    实际标准差: N/A\n")
                            f.write(f"    是否采样标准差: {root_info.get('sample_std', 'N/A')}\n")
                            config_std = root_info.get('configured_std')
                            if root_info.get('sample_std') and config_std is not None:
                                f.write(f"    配置标准差: {config_std:.6f}\n")
                        elif root_info.get('distribution_type') == 'uniform':
                            f.write(f"    最小值: {root_info.get('min_root', 'N/A')}\n")
                            f.write(f"    最大值: {root_info.get('max_root', 'N/A')}\n")
                        f.write(f"    是否采样范围: {root_info.get('sample_cause_ranges', 'N/A')}\n")
                    else:
                        f.write(f"  配置 {config_key}: 无根节点分布信息\n")
                except Exception as e:
                    f.write(f"  配置 {config_key}: 获取根节点分布信息时出错: {str(e)}\n")
            f.write("\n")

        # 1.6. 节点数据统计信息（分位数、均值、标准差）
        f.write("1.6. 节点数据统计信息（分位数、均值、标准差）\n")
        f.write("-" * 40 + "\n")
        for dataset_idx, configs in scm_objects.items():
            f.write(f"数据集 {dataset_idx}:\n")
            for config_key, scm_info in configs.items():
                try:
                    if 'node_statistics' in scm_info and isinstance(scm_info['node_statistics'], dict):
                        node_stats = scm_info['node_statistics']
                        f.write(f"  配置 {config_key}:\n")
                        if node_stats:
                            for node, stats in node_stats.items():
                                f.write(f"    节点 {node}:\n")
                                f.write(f"      最小值(0%): {stats['min']:.6f}\n")
                                f.write(f"      25%分位数: {stats['q25']:.6f}\n")
                                f.write(f"      中位数(50%): {stats['median']:.6f}\n")
                                f.write(f"      75%分位数: {stats['q75']:.6f}\n")
                                f.write(f"      最大值(100%): {stats['max']:.6f}\n")
                                f.write(f"      均值: {stats['mean']:.6f}\n")
                                f.write(f"      标准差: {stats['std']:.6f}\n")
                        else:
                            f.write(f"    无节点数据\n")
                    else:
                        f.write(f"  配置 {config_key}: 无节点统计信息\n")
                except Exception as e:
                    f.write(f"  配置 {config_key}: 获取节点统计信息时出错: {str(e)}\n")
            f.write("\n")

        # 2. SNR验证结果
        if snr_results:
            f.write("2. SNR验证结果\n")
            f.write("-" * 40 + "\n")
            for result in snr_results:
                f.write(f"数据集: {result.get('dataset', 'N/A')}\n")
                f.write(f"配置: {result.get('use_snr', 'N/A')}\n")

                snr_mean = result.get('snr_mean', 'N/A')
                if isinstance(snr_mean, (int, float)):
                    f.write(f"平均SNR: {snr_mean:.4f}\n")
                else:
                    f.write(f"平均SNR: {snr_mean}\n")

                snr_std = result.get('snr_std', 'N/A')
                if isinstance(snr_std, (int, float)):
                    f.write(f"SNR标准差: {snr_std:.4f}\n")
                else:
                    f.write(f"SNR标准差: {snr_std}\n")
                f.write("\n")

        # 3. 模型性能结果
        if results:
            f.write("3. 模型性能结果\n")
            f.write("-" * 40 + "\n")

            # 按数据集和配置分组
            performance_by_dataset = {}
            for result in results:
                dataset = result['dataset']
                config = result['use_snr']
                model = result['model_type']

                if dataset not in performance_by_dataset:
                    performance_by_dataset[dataset] = {}
                if config not in performance_by_dataset[dataset]:
                    performance_by_dataset[dataset][config] = {}

                performance_by_dataset[dataset][config][model] = result

            for dataset, configs in performance_by_dataset.items():
                f.write(f"\n数据集: {dataset}\n")
                for config, models in configs.items():
                    f.write(f"  配置: {config}\n")
                    for model, metrics in models.items():
                        f.write(f"    {model.upper()}:\n")
                        f.write(f"      R² - Train: {metrics['r2_train']:.4f}, Test: {metrics['r2_test']:.4f}, Intervention: {metrics['r2_intv']:.4f}\n")
                        f.write(f"      RMSE - Train: {metrics['rmse_train']:.4f}, Test: {metrics['rmse_test']:.4f}, Intervention: {metrics['rmse_intv']:.4f}\n")
                        f.write(f"      MAPE - Train: {metrics['mape_train']:.2f}%, Test: {metrics['mape_test']:.2f}%, Intervention: {metrics['mape_intv']:.2f}%\n")

        # 4. 特征重要性结果
        if importance_results:
            f.write("\n4. 特征重要性分析\n")
            f.write("-" * 40 + "\n")

            for result in importance_results:
                f.write(f"\n数据集: {result['dataset']}, 配置: {result['use_snr']}, 模型: {result['model_type'].upper()}\n")

                # Permutation importance
                perm_imp = result['importance']['permutation']
                sorted_features = sorted(perm_imp.items(), key=lambda x: x[1], reverse=True)
                f.write("  Permutation Importance (Top 5):\n")
                for feature, importance in sorted_features[:5]:
                    f.write(f"    {feature}: {importance:.6f}\n")

                # Built-in importance (if available)
                if 'builtin' in result['importance']:
                    builtin_imp = result['importance']['builtin']
                    sorted_builtin = sorted(builtin_imp.items(), key=lambda x: x[1], reverse=True)
                    f.write("  Built-in Importance (Top 5):\n")
                    for feature, importance in sorted_builtin[:5]:
                        f.write(f"    {feature}: {importance:.6f}\n")

        f.write("\n" + "=" * 80 + "\n")
        f.write("报告生成完成\n")

    print(f"已保存综合结果报告到: {txt_path}")





