parents: ['X9'] -> child: X3
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.013600564053123702

parents: ['X0', 'X2', 'X4', 'X5', 'X8'] -> child: X6
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.10982595869406142

parents: ['X7'] -> child: X8
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.03619217024417254

parents: ['X0'] -> child: X9
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.7836666400359228

parents: ['X7'] -> child: X10
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.585797229507175

parents: ['X0', 'X1'] -> child: X11
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.8909629381415796