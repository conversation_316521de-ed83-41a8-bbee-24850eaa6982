{"model1_name": "DEFAULT Model", "model2_name": "MSE Model", "layer_statistics": {"decoder_dict.standard.0.bias": {"DEFAULT Model": {"min": -0.6290776133537292, "median": -0.013756781816482544, "max": 0.18340671062469482, "mean": -0.018459776416420937, "std": 0.07270175218582153, "shape": [768]}, "MSE Model": {"min": -0.1483839601278305, "median": -0.033052921295166016, "max": 0.09463793784379959, "mean": -0.033063169568777084, "std": 0.049818944185972214, "shape": [768]}, "differences": {"min_diff": 0.48069365322589874, "median_diff": 0.01929613947868347, "max_diff": 0.08876877278089523, "mean_diff": 0.014603393152356148, "std_diff": 0.02288280799984932}}, "decoder_dict.standard.0.weight": {"DEFAULT Model": {"min": -1.6628949642181396, "median": -7.891884888522327e-05, "max": 1.203856110572815, "mean": -0.000558450585231185, "std": 0.16451096534729004, "shape": [147456]}, "MSE Model": {"min": -0.3847365379333496, "median": -0.0023514474742114544, "max": 0.39013004302978516, "mean": -0.002788355341181159, "std": 0.05147311091423035, "shape": [147456]}, "differences": {"min_diff": 1.27815842628479, "median_diff": 0.002272528625326231, "max_diff": 0.8137260675430298, "mean_diff": 0.002229904755949974, "std_diff": 0.11303785443305969}}, "decoder_dict.standard.2.bias": {"DEFAULT Model": {"min": -3.062774658203125, "median": -0.6851232051849365, "max": 0.11467898637056351, "mean": -0.8686986565589905, "std": 0.8337796926498413, "shape": [5000]}, "MSE Model": {"min": -0.01852046325802803, "median": -0.01852046325802803, "max": -0.01852046325802803, "mean": -0.01852046325802803, "std": 0.0, "shape": [1]}, "differences": {"min_diff": 3.044254194945097, "median_diff": 0.6666027419269085, "max_diff": 0.13319944962859154, "mean_diff": 0.8501781933009624, "std_diff": 0.8337796926498413}}, "decoder_dict.standard.2.weight": {"DEFAULT Model": {"min": -3.2829511165618896, "median": -0.16349010169506073, "max": 3.35265851020813, "mean": -0.16503825783729553, "std": 0.2343875765800476, "shape": [3840000]}, "MSE Model": {"min": -0.2429419457912445, "median": -2.528997720219195e-05, "max": 0.22352445125579834, "mean": 0.001416530110873282, "std": 0.046997569501399994, "shape": [768]}, "differences": {"min_diff": 3.040009170770645, "median_diff": 0.16346481171785854, "max_diff": 3.1291340589523315, "mean_diff": 0.16645478794816881, "std_diff": 0.1873900070786476}}, "encoder.5.layer.weight": {"DEFAULT Model": {"min": -1.4563103914260864, "median": -0.003034599358215928, "max": 1.4703311920166016, "mean": -0.002747319871559739, "std": 0.37564247846603394, "shape": [768]}, "MSE Model": {"min": -1.235897421836853, "median": -0.009556224569678307, "max": 1.3794125318527222, "mean": -0.004867078736424446, "std": 0.3245048224925995, "shape": [768]}, "differences": {"min_diff": 0.2204129695892334, "median_diff": 0.0065216252114623785, "max_diff": 0.0909186601638794, "mean_diff": 0.002119758864864707, "std_diff": 0.05113765597343445}}, "feature_positional_embedding_embeddings.bias": {"DEFAULT Model": {"min": -1.3058152198791504, "median": -0.051111623644828796, "max": 1.294655680656433, "mean": -0.035168495029211044, "std": 0.4574573338031769, "shape": [192]}, "MSE Model": {"min": -1.190041184425354, "median": 0.02939717285335064, "max": 1.1760274171829224, "mean": 0.0013287352630868554, "std": 0.3289867043495178, "shape": [192]}, "differences": {"min_diff": 0.11577403545379639, "median_diff": 0.08050879649817944, "max_diff": 0.11862826347351074, "mean_diff": 0.0364972302922979, "std_diff": 0.12847062945365906}}, "feature_positional_embedding_embeddings.weight": {"DEFAULT Model": {"min": -0.05505897104740143, "median": 0.0009445412433706224, "max": 0.09494483470916748, "mean": 0.0013036055024713278, "std": 0.011267416179180145, "shape": [9216]}, "MSE Model": {"min": -0.4806135594844818, "median": 0.0006605054950341582, "max": 0.5303832292556763, "mean": 0.0007244510343298316, "std": 0.06357675045728683, "shape": [9216]}, "differences": {"min_diff": 0.4255545884370804, "median_diff": 0.00028403574833646417, "max_diff": 0.4354383945465088, "mean_diff": 0.0005791544681414962, "std_diff": 0.05230933427810669}}, "transformer_encoder.layers.0.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.6202547550201416, "median": -0.0009083705954253674, "max": 0.6829686760902405, "mean": -0.0009278126526623964, "std": 0.07267250865697861, "shape": [147456]}, "MSE Model": {"min": -0.7013128399848938, "median": -0.00014995320816524327, "max": 0.7941488027572632, "mean": 2.0543744540191256e-05, "std": 0.0729360580444336, "shape": [147456]}, "differences": {"min_diff": 0.0810580849647522, "median_diff": 0.0007584173872601241, "max_diff": 0.1111801266670227, "mean_diff": 0.0009483563972025877, "std_diff": 0.00026354938745498657}}, "transformer_encoder.layers.0.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.49236807227134705, "median": 0.00046359520638361573, "max": 0.556418776512146, "mean": 0.0004765714693348855, "std": 0.05164451524615288, "shape": [147456]}, "MSE Model": {"min": -1.071962833404541, "median": 0.0001280018186662346, "max": 0.9922414422035217, "mean": 0.0001324702170677483, "std": 0.0607365146279335, "shape": [147456]}, "differences": {"min_diff": 0.579594761133194, "median_diff": 0.0003355933877173811, "max_diff": 0.43582266569137573, "mean_diff": 0.00034410125226713717, "std_diff": 0.009091999381780624}}, "transformer_encoder.layers.1.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.5573551058769226, "median": -0.0008825825061649084, "max": 0.8042160272598267, "mean": -0.0010356823913753033, "std": 0.06697926670312881, "shape": [147456]}, "MSE Model": {"min": -0.604997992515564, "median": -0.0006158561445772648, "max": 0.7289376258850098, "mean": -0.0008498704410158098, "std": 0.06652823835611343, "shape": [147456]}, "differences": {"min_diff": 0.04764288663864136, "median_diff": 0.0002667263615876436, "max_diff": 0.0752784013748169, "mean_diff": 0.0001858119503594935, "std_diff": 0.00045102834701538086}}, "transformer_encoder.layers.1.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.6008810997009277, "median": 6.159952317830175e-05, "max": 0.4977424442768097, "mean": 0.0003880660515278578, "std": 0.04875701293349266, "shape": [147456]}, "MSE Model": {"min": -0.5980996489524841, "median": -3.449546420597471e-05, "max": 0.6215652823448181, "mean": 2.2477230231743306e-05, "std": 0.04792239889502525, "shape": [147456]}, "differences": {"min_diff": 0.0027814507484436035, "median_diff": 9.609498738427646e-05, "max_diff": 0.12382283806800842, "mean_diff": 0.0003655888212961145, "std_diff": 0.0008346140384674072}}, "transformer_encoder.layers.10.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.5060670375823975, "median": 0.00020019453950226307, "max": 0.5226134061813354, "mean": 0.00046376060345210135, "std": 0.08587190508842468, "shape": [147456]}, "MSE Model": {"min": -0.38884955644607544, "median": -0.0002913216012530029, "max": 0.3798905313014984, "mean": -0.0005104423617012799, "std": 0.07210610806941986, "shape": [147456]}, "differences": {"min_diff": 0.11721748113632202, "median_diff": 0.000491516140755266, "max_diff": 0.14272287487983704, "mean_diff": 0.0009742029651533812, "std_diff": 0.013765797019004822}}, "transformer_encoder.layers.10.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.6602919101715088, "median": -1.963929389603436e-05, "max": 0.7586064338684082, "mean": 1.9295750462333672e-05, "std": 0.08117648214101791, "shape": [147456]}, "MSE Model": {"min": -0.507834792137146, "median": -0.00016328657511621714, "max": 0.47185125946998596, "mean": -3.837968688458204e-05, "std": 0.053374867886304855, "shape": [147456]}, "differences": {"min_diff": 0.1524571180343628, "median_diff": 0.00014364728122018278, "max_diff": 0.28675517439842224, "mean_diff": 5.7675437346915714e-05, "std_diff": 0.02780161425471306}}, "transformer_encoder.layers.11.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.5302445292472839, "median": -0.0005579292774200439, "max": 0.4895784854888916, "mean": -0.0008923267596401274, "std": 0.09355270862579346, "shape": [147456]}, "MSE Model": {"min": -0.39188244938850403, "median": -0.00038377492455765605, "max": 0.31841567158699036, "mean": -0.00038715620758011937, "std": 0.04404769092798233, "shape": [147456]}, "differences": {"min_diff": 0.1383620798587799, "median_diff": 0.0001741543528623879, "max_diff": 0.17116281390190125, "mean_diff": 0.000505170552060008, "std_diff": 0.04950501769781113}}, "transformer_encoder.layers.11.mlp.linear2.weight": {"DEFAULT Model": {"min": -1.553707242012024, "median": -6.766367005184293e-05, "max": 1.4989681243896484, "mean": -0.00012914504623040557, "std": 0.1013529822230339, "shape": [147456]}, "MSE Model": {"min": -0.2903446555137634, "median": -0.00010373848635936156, "max": 0.3909536600112915, "mean": 0.00018758434453047812, "std": 0.022425130009651184, "shape": [147456]}, "differences": {"min_diff": 1.2633625864982605, "median_diff": 3.607481630751863e-05, "max_diff": 1.108014464378357, "mean_diff": 0.0003167293907608837, "std_diff": 0.07892785221338272}}, "transformer_encoder.layers.2.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.5367307066917419, "median": -0.00028362817829474807, "max": 0.5163195729255676, "mean": -0.00046683981781825423, "std": 0.08134003728628159, "shape": [147456]}, "MSE Model": {"min": -0.7854811549186707, "median": -0.00039294408634305, "max": 0.7271926999092102, "mean": -0.0005029800231568515, "std": 0.07374855130910873, "shape": [147456]}, "differences": {"min_diff": 0.2487504482269287, "median_diff": 0.00010931590804830194, "max_diff": 0.21087312698364258, "mean_diff": 3.61402053385973e-05, "std_diff": 0.0075914859771728516}}, "transformer_encoder.layers.2.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.636635422706604, "median": -1.2688131391769275e-05, "max": 0.561224102973938, "mean": 0.00012503931066021323, "std": 0.05405348911881447, "shape": [147456]}, "MSE Model": {"min": -0.7594879269599915, "median": -0.0007502403459511697, "max": 0.6710070371627808, "mean": 0.00011485983122838661, "std": 0.0648195743560791, "shape": [147456]}, "differences": {"min_diff": 0.12285250425338745, "median_diff": 0.0007375522145594005, "max_diff": 0.10978293418884277, "mean_diff": 1.0179479431826621e-05, "std_diff": 0.010766085237264633}}, "transformer_encoder.layers.3.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.5469803214073181, "median": -0.0007722980226390064, "max": 0.47081735730171204, "mean": -0.0007724510505795479, "std": 0.0897751972079277, "shape": [147456]}, "MSE Model": {"min": -0.7449483275413513, "median": -0.0009709357400424778, "max": 0.8156536817550659, "mean": -0.0015809312462806702, "std": 0.07761374861001968, "shape": [147456]}, "differences": {"min_diff": 0.1979680061340332, "median_diff": 0.00019863771740347147, "max_diff": 0.3448363244533539, "mean_diff": 0.0008084801957011223, "std_diff": 0.01216144859790802}}, "transformer_encoder.layers.3.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.433550089597702, "median": -0.000232441583648324, "max": 0.4266194999217987, "mean": -0.00027660970226861537, "std": 0.05945821851491928, "shape": [147456]}, "MSE Model": {"min": -0.8108075261116028, "median": -0.00046572746941819787, "max": 0.8098125457763672, "mean": 6.744459096807986e-05, "std": 0.06698427349328995, "shape": [147456]}, "differences": {"min_diff": 0.37725743651390076, "median_diff": 0.00023328588576987386, "max_diff": 0.3831930458545685, "mean_diff": 0.00034405429323669523, "std_diff": 0.0075260549783706665}}, "transformer_encoder.layers.4.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.4660520851612091, "median": -0.0014119973639026284, "max": 0.5282270908355713, "mean": -0.0015275595942512155, "std": 0.08549092710018158, "shape": [147456]}, "MSE Model": {"min": -0.8740236163139343, "median": -0.0010249728802591562, "max": 0.8158724904060364, "mean": -0.0014741214690729976, "std": 0.08054938912391663, "shape": [147456]}, "differences": {"min_diff": 0.4079715311527252, "median_diff": 0.0003870244836434722, "max_diff": 0.2876453995704651, "mean_diff": 5.343812517821789e-05, "std_diff": 0.004941537976264954}}, "transformer_encoder.layers.4.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.6068646311759949, "median": -5.872422843822278e-05, "max": 0.5727084875106812, "mean": -9.249278082279488e-05, "std": 0.0705329105257988, "shape": [147456]}, "MSE Model": {"min": -0.8151901960372925, "median": -0.00028660724638029933, "max": 0.6007026433944702, "mean": -2.819756900862558e-06, "std": 0.06676040589809418, "shape": [147456]}, "differences": {"min_diff": 0.2083255648612976, "median_diff": 0.00022788301794207655, "max_diff": 0.027994155883789062, "mean_diff": 8.967302392193233e-05, "std_diff": 0.0037725046277046204}}, "transformer_encoder.layers.5.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.6232510209083557, "median": -0.000636812939774245, "max": 0.5580019950866699, "mean": -0.0009380620322190225, "std": 0.0823894515633583, "shape": [147456]}, "MSE Model": {"min": -0.7702049016952515, "median": -0.00016995520854834467, "max": 0.7781743407249451, "mean": -7.596031355205923e-05, "std": 0.07989216595888138, "shape": [147456]}, "differences": {"min_diff": 0.14695388078689575, "median_diff": 0.00046685773122590035, "max_diff": 0.22017234563827515, "mean_diff": 0.0008621017186669633, "std_diff": 0.0024972856044769287}}, "transformer_encoder.layers.5.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.6677958965301514, "median": 8.707646338734776e-05, "max": 0.7921043038368225, "mean": -6.972724804654717e-05, "std": 0.0776594877243042, "shape": [147456]}, "MSE Model": {"min": -0.6301543712615967, "median": -0.00024453920195810497, "max": 0.6625223755836487, "mean": -1.353377228952013e-05, "std": 0.0726442039012909, "shape": [147456]}, "differences": {"min_diff": 0.03764152526855469, "median_diff": 0.0003316156653454527, "max_diff": 0.12958192825317383, "mean_diff": 5.6193475757027045e-05, "std_diff": 0.005015283823013306}}, "transformer_encoder.layers.6.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.6447569131851196, "median": -0.00018362206174060702, "max": 0.6516433358192444, "mean": -0.0002788111742120236, "std": 0.0876910537481308, "shape": [147456]}, "MSE Model": {"min": -1.0152766704559326, "median": 0.00019691021589096636, "max": 0.8572580814361572, "mean": -8.099448314169422e-05, "std": 0.08081231266260147, "shape": [147456]}, "differences": {"min_diff": 0.370519757270813, "median_diff": 0.0003805322776315734, "max_diff": 0.20561474561691284, "mean_diff": 0.0001978166910703294, "std_diff": 0.006878741085529327}}, "transformer_encoder.layers.6.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.6379311680793762, "median": 0.00016689556650817394, "max": 0.6048159599304199, "mean": 2.6112658815691248e-05, "std": 0.07499492913484573, "shape": [147456]}, "MSE Model": {"min": -0.9621119499206543, "median": 6.404342275345698e-05, "max": 0.6171201467514038, "mean": -1.8462460502632894e-05, "std": 0.07646883279085159, "shape": [147456]}, "differences": {"min_diff": 0.3241807818412781, "median_diff": 0.00010285214375471696, "max_diff": 0.012304186820983887, "mean_diff": 4.457511931832414e-05, "std_diff": 0.0014739036560058594}}, "transformer_encoder.layers.7.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.5584651827812195, "median": 0.00012498063733801246, "max": 0.4943373501300812, "mean": 6.710955062771973e-07, "std": 0.08781891316175461, "shape": [147456]}, "MSE Model": {"min": -0.5863614678382874, "median": -0.00014539857511408627, "max": 0.5863838195800781, "mean": -0.00023004582908470184, "std": 0.0778806135058403, "shape": [147456]}, "differences": {"min_diff": 0.02789628505706787, "median_diff": 0.00027037921245209873, "max_diff": 0.09204646944999695, "mean_diff": 0.00023071692459097903, "std_diff": 0.009938299655914307}}, "transformer_encoder.layers.7.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.5156142115592957, "median": 0.00024236782337538898, "max": 0.4692193269729614, "mean": 0.0002128074993379414, "std": 0.07276702672243118, "shape": [147456]}, "MSE Model": {"min": -0.5187013149261475, "median": -6.458139978349209e-05, "max": 0.5596662163734436, "mean": -7.414252468151972e-05, "std": 0.06954040378332138, "shape": [147456]}, "differences": {"min_diff": 0.0030871033668518066, "median_diff": 0.00030694922315888107, "max_diff": 0.09044688940048218, "mean_diff": 0.0002869500240194611, "std_diff": 0.0032266229391098022}}, "transformer_encoder.layers.8.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.437459260225296, "median": 0.00044107704889029264, "max": 0.48136869072914124, "mean": 0.0005636189598590136, "std": 0.08432579785585403, "shape": [147456]}, "MSE Model": {"min": -0.4682266116142273, "median": -8.141181024257094e-05, "max": 0.5283063650131226, "mean": -0.00025401930906809866, "std": 0.07970260828733444, "shape": [147456]}, "differences": {"min_diff": 0.030767351388931274, "median_diff": 0.0005224888591328636, "max_diff": 0.04693767428398132, "mean_diff": 0.0008176382689271122, "std_diff": 0.004623189568519592}}, "transformer_encoder.layers.8.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.439547598361969, "median": 0.00018034176900982857, "max": 0.36915695667266846, "mean": 0.00011099404218839481, "std": 0.07352157682180405, "shape": [147456]}, "MSE Model": {"min": -0.745034396648407, "median": -8.90479059307836e-05, "max": 0.6197081804275513, "mean": -9.748672164278105e-05, "std": 0.06926827877759933, "shape": [147456]}, "differences": {"min_diff": 0.305486798286438, "median_diff": 0.00026938967494061217, "max_diff": 0.2505512237548828, "mean_diff": 0.00020848076383117586, "std_diff": 0.004253298044204712}}, "transformer_encoder.layers.9.mlp.linear1.weight": {"DEFAULT Model": {"min": -0.40311944484710693, "median": 0.00039348663995042443, "max": 0.48771432042121887, "mean": 0.0008643253822810948, "std": 0.08670379221439362, "shape": [147456]}, "MSE Model": {"min": -0.7081663608551025, "median": -0.0004872728022746742, "max": 0.6826735138893127, "mean": -0.0006125121144577861, "std": 0.07652251422405243, "shape": [147456]}, "differences": {"min_diff": 0.3050469160079956, "median_diff": 0.0008807594422250986, "max_diff": 0.19495919346809387, "mean_diff": 0.0014768374967388809, "std_diff": 0.010181277990341187}}, "transformer_encoder.layers.9.mlp.linear2.weight": {"DEFAULT Model": {"min": -0.3777287006378174, "median": 7.947192352730781e-05, "max": 0.45394328236579895, "mean": 2.9685137633350678e-05, "std": 0.07339650392532349, "shape": [147456]}, "MSE Model": {"min": -0.43779996037483215, "median": 2.8354948881315067e-05, "max": 0.440080463886261, "mean": -6.296731589827687e-05, "std": 0.06968622654676437, "shape": [147456]}, "differences": {"min_diff": 0.06007125973701477, "median_diff": 5.111697464599274e-05, "max_diff": 0.013862818479537964, "mean_diff": 9.265245353162754e-05, "std_diff": 0.0037102773785591125}}, "y_encoder.1.layer.bias": {"DEFAULT Model": {"min": -0.7023638486862183, "median": 0.06637305021286011, "max": 0.7053927779197693, "mean": 0.06500954180955887, "std": 0.39511194825172424, "shape": [192]}, "MSE Model": {"min": -0.848537027835846, "median": 0.06650956720113754, "max": 1.0478901863098145, "mean": -0.0016172515461221337, "std": 0.4499574601650238, "shape": [192]}, "differences": {"min_diff": 0.14617317914962769, "median_diff": 0.0001365169882774353, "max_diff": 0.34249740839004517, "mean_diff": 0.066626793355681, "std_diff": 0.05484551191329956}}, "y_encoder.1.layer.weight": {"DEFAULT Model": {"min": -0.7074155807495117, "median": 0.004192407242953777, "max": 0.8778685331344604, "mean": 0.0007864981889724731, "std": 0.411380410194397, "shape": [384]}, "MSE Model": {"min": -1.1661008596420288, "median": -0.014742275699973106, "max": 0.8017269968986511, "mean": -0.023050574585795403, "std": 0.32165271043777466, "shape": [384]}, "differences": {"min_diff": 0.4586852788925171, "median_diff": 0.018934682942926884, "max_diff": 0.07614153623580933, "mean_diff": 0.023837072774767876, "std_diff": 0.08972769975662231}}}}