"""
scm_test.py - SCM模型测试和评估框架

主要测试脚本，提供了完整的模型评估和比较功能：

主要功能：
1. 模型训练和评估：支持多种机器学习模型（OLS、Lasso、CatBoost、XGBoost、TabPFN）
2. 并行处理：支持多进程并行处理不同SNR配置
3. 性能评估：提供R²、MSE、MAPE等多种评估指标
4. 特征重要性：计算和分析各节点的特征重要性
5. 结果可视化：生成各种图表和统计报告
6. 数据导出：将结果保存为CSV、JSON等格式

核心功能：
- process_single_config(): 处理单个函数配置的完整流程
- calculate_feature_importance(): 计算特征重要性
- 并行处理框架：支持GPU和CPU并行计算

支持的模型：
- 线性模型：OLS、Lasso回归
- 树模型：CatBoost、XGBoost
- 神经网络：TabPFN（多个变体）
"""

import os
import gc
import json
import pandas as pd
import numpy as np
import concurrent.futures
import multiprocessing
import pickle
import warnings
import math
import torch
import matplotlib.pyplot as plt
import seaborn as sns
import traceback
import signal
import pdb
import ipdb
import networkx as nx
from datetime import datetime
from collections import OrderedDict
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Any

from scm_data_generator import generate_datasets
from utils_scm import draw_graph
from utils_plot import ImportancePlotter, R2Plotter
from utils_test import (
    extract_serializable_scm_info,
    should_draw_dag_shared,
    calculate_pairwise_correlations_and_mi,
    process_comprehensive_data,
    save_summary_csv
)

from sklearn.metrics import r2_score, mean_squared_error
from sklearn.inspection import permutation_importance
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression, Lasso
import xgboost as xgb


try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    from tabpfn import TabPFNRegressor
    TABPFN_AVAILABLE = True
except ImportError:
    TABPFN_AVAILABLE = False

try:
    from inference.regressor_mse import TabPFNRegressor_mse
    TABPFN_MSE_AVAILABLE = True
except ImportError:
    TABPFN_MSE_AVAILABLE = False

try:
    from inference.regressor_muzero import TabPFNRegressor_muzero
    TABPFN_MUZERO_AVAILABLE = True
except ImportError:
    TABPFN_MUZERO_AVAILABLE = False

import matplotlib
matplotlib.use('Agg')
matplotlib.rcParams['axes.unicode_minus'] = False


@dataclass
class ModelConfig:
    """模型配置类"""
    ols: bool = True
    lasso: bool = False
    catboost: bool = False
    xgboost: bool = True
    lightgbm: bool = True
    tabpfn_default: bool = True
    tabpfn_mse: bool = True
    tabpfn_muzero: bool = True


@dataclass
class FunctionConfig:
    """
    单个函数配置类

    支持的函数类型:
    - 'linear': 线性函数 y = a1*x1 + a2*x2 + ... + b
    - 'polynomial': 多项式函数
    - 'polynomial_even': 偶次多项式函数
    - 'exponential': 指数函数
    - 'logarithmic': 对数函数
    - 'sigmoid_scaled': 缩放的Sigmoid函数
    - 'tanh_scaled': 缩放的Tanh函数
    - 'sine': 正弦函数
    - 'cosine': 余弦函数
    - 'relu_quadratic': ReLU二次函数
    - 'gaussian_rbf': 高斯径向基函数
    - 'gaussian_process': 高斯过程函数（需要GPy）
    - 'fourier_series': 傅里叶级数函数
    - 'piecewise_linear': 分段线性函数
    - 'random_neural_network': 随机神经网络函数

    噪声配置优先级:
    1. target_snr: 基于信噪比动态计算噪声标准差
    2. noise_std: 固定噪声标准差
    3. 如果都未指定，使用全局SNR配置
    """
    function_type: str = 'linear'  # 默认使用线性函数，最简单且稳定
    hidden_dim: int = 2  # 神经网络隐藏层维度（仅对neural_network类型有效）
    depth: int = 3  # 神经网络深度（仅对neural_network类型有效）
    activation: str = 'tanh'  # 激活函数（仅对neural_network类型有效）
    target_snr: Optional[float] = None  # 目标信噪比
    noise_std: Optional[float] = None  # 固定噪声标准差（与target_snr互斥）
    noise_mean: Optional[float] = None  # 噪声均值配置
    noise_mean_mode: str = 'fixed'  # 噪声均值模式：'fixed' 或 'signal_mean'

    # 函数特定参数（根据function_type使用）
    coefficients: Optional[List[float]] = None  # 线性函数系数（linear类型）
    bias: Optional[float] = None  # 偏置项
    degree: Optional[int] = None  # 多项式次数（polynomial类型）
    scale: Optional[float] = None  # 缩放因子
    frequency: Optional[float] = None  # 频率参数（sine/cosine类型）
    n_components: Optional[int] = None  # 组件数量（fourier_series/piecewise_linear类型）
    centers: Optional[List[List[float]]] = None  # 中心点（gaussian_rbf类型）

    # g函数配置（后非线性变换）
    g_function_type: str = 'identity'  # 简单g函数类型，支持: identity, sigmoid, tanh, softplus, elu+1, scaled_tanh
    g_function_config: Optional[Dict] = None  # 完整g函数配置，支持所有function_generator.py中的函数类型


@dataclass
class NodeFunctionConfigs:
    """节点函数配置类 - 支持不同节点类型使用不同函数"""
    target_config: FunctionConfig = None  # target节点（parent）的函数配置
    target_child_config: FunctionConfig = None  # target_child节点的函数配置
    other_config: FunctionConfig = None  # Other_type节点的函数配置

    def __post_init__(self):
        # 如果没有指定，使用默认配置
        if self.target_config is None:
            self.target_config = FunctionConfig()
        if self.target_child_config is None:
            self.target_child_config = FunctionConfig()
        if self.other_config is None:
            self.other_config = FunctionConfig()


@dataclass
class SNRConfig:
    """SNR配置类"""
    parent_snr_values: List[float] = None
    child_snr_multipliers: List[float] = None
    other_snr_multipliers: List[float] = None

    def __post_init__(self):
        if self.parent_snr_values is None:
            self.parent_snr_values = [2.0]
        if self.child_snr_multipliers is None:
            self.child_snr_multipliers = [1.0]
        if self.other_snr_multipliers is None:
            self.other_snr_multipliers = [1.0]


@dataclass
class TestConfig:
    """测试配置类"""
    n_datasets: int = 2
    intervention_node_type: str = 'all_non_family'
    intervention_value_method: str = 'sample'
    custom_dag_type: str = 'random_SF'
    custom_dag_size: str = 'small'
    avg_in_degree: Tuple[float, float] = (2.2, 2.7)
    device: str = 'cuda'
    num_workers: int = 8
    r2_threshold: float = 0.5
    save_json_file: bool = True
    calculate_correlation_mi: bool = True
    extract_function_weights: bool = True
    annotate_all_datasets: bool = True
    annotate_effective_datasets: bool = True

    # SNR和函数配置
    snr_config: SNRConfig = None
    node_function_configs: NodeFunctionConfigs = None

    def __post_init__(self):
        if self.snr_config is None:
            self.snr_config = SNRConfig()
        if self.node_function_configs is None:
            self.node_function_configs = NodeFunctionConfigs()


class DataProcessor:
    """数据处理工具类"""
    
    @staticmethod
    def convert_tensors_to_numpy(*tensors):
        """将tensor转换为numpy数组"""
        return [tensor.clone().cpu().numpy() if torch.is_tensor(tensor) else tensor for tensor in tensors]
    
    @staticmethod
    def split_train_test(x, y, split_ratio=0.7):
        """分割训练测试数据"""
        eval_position = int(x.shape[0] * split_ratio)
        return (x[:eval_position], y[:eval_position], 
                x[eval_position:], y[eval_position:])
    
    @staticmethod
    def apply_scaling(scaler, *arrays):
        """应用数据缩放"""
        if scaler is None:
            return arrays
        return [scaler.transform(arr) for arr in arrays]


class ModelTrainer:
    """单个模型训练器基类"""
    
    def __init__(self, model_type: str, device: str = 'cpu'):
        self.model_type = model_type
        self.device = device
        self.scaler = None
        self.model = None
    
    def needs_scaling(self) -> bool:
        """判断是否需要数据归一化"""
        return self.model_type in ['ols', 'lasso']
    
    def prepare_train_data(self, train_x, train_y):
        """准备训练数据"""
        if self.needs_scaling():
            self.scaler = StandardScaler()
            train_x_processed = self.scaler.fit_transform(train_x.copy())
        else:
            train_x_processed = train_x
            self.scaler = None

        return train_x_processed, train_y

    def prepare_test_data(self, test_x):
        """准备测试数据 - 使用训练时的预处理参数"""
        if self.scaler is not None:
            test_x_processed = self.scaler.transform(test_x.copy())
        else:
            test_x_processed = test_x

        return test_x_processed
    
    def create_model(self):
        """创建模型实例"""
        raise NotImplementedError
    
    def train(self, train_x, train_y):
        """训练模型"""
        # 准备训练数据
        train_x_proc, train_y_proc = self.prepare_train_data(train_x, train_y)

        # 创建并训练模型
        self.model = self.create_model()
        self.model.fit(train_x_proc, train_y_proc)

        return self.model

    def predict(self, test_x):
        """使用训练好的模型进行预测"""
        if not hasattr(self, 'model') or self.model is None:
            raise ValueError("模型尚未训练，请先调用train方法")

        # 准备测试数据
        test_x_proc = self.prepare_test_data(test_x)

        # 预测
        pred_test = self.model.predict(test_x_proc)

        return pred_test
    



class OLSTrainer(ModelTrainer):
    """OLS模型训练器"""
    
    def create_model(self):
        return LinearRegression()


class LassoTrainer(ModelTrainer):
    """Lasso模型训练器"""
    
    def create_model(self):
        return Lasso(alpha=10, random_state=0)


class XGBoostTrainer(ModelTrainer):
    """XGBoost模型训练器"""
    
    def create_model(self):
        return xgb.XGBRegressor(
            random_state=0,
            verbosity=0,
            n_jobs=1,
            tree_method='gpu_hist'
        )


class CatBoostTrainer(ModelTrainer):
    """CatBoost模型训练器"""
    
    def create_model(self):
        if not CATBOOST_AVAILABLE:
            raise ValueError("CatBoost is not available")
        return CatBoostRegressor(
            random_seed=0,
            verbose=False,
            allow_writing_files=False,
            thread_count=1,
            task_type='GPU'
        )


class LightGBMTrainer(ModelTrainer):
    """LightGBM模型训练器"""
    
    def create_model(self):
        if not LIGHTGBM_AVAILABLE:
            raise ValueError("LightGBM is not available")
        warnings.filterwarnings('ignore', message='X does not have valid feature names')
        return lgb.LGBMRegressor(
            random_state=0,
            verbosity=-1,
            force_col_wise=True,
            n_jobs=1
        )


class TabPFNTrainer(ModelTrainer):
    """TabPFN模型训练器基类"""
    
    def __init__(self, model_type: str, device: str = 'cpu'):
        super().__init__(model_type, device)
        self.model_class = None
        self.model_path = None
        self._setup_model_config()
    
    def _setup_model_config(self):
        """设置模型配置"""
        if self.model_type == 'tabpfn_default':
            if not TABPFN_AVAILABLE:
                raise ValueError("TabPFN is not available")
            self.model_class = TabPFNRegressor
            self.model_path = '/root/LDM_test/tabpfn-v2-regressor.ckpt'
        elif self.model_type == 'tabpfn_mse':
            if not TABPFN_MSE_AVAILABLE:
                raise ValueError("TabPFNRegressor_mse is not available")
            self.model_class = TabPFNRegressor_mse
            self.model_path = './new_model_checkpoints/mse_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
        elif self.model_type == 'tabpfn_muzero':
            if not TABPFN_MUZERO_AVAILABLE:
                raise ValueError("TabPFNRegressor_muzero is not available")
            self.model_class = TabPFNRegressor_muzero
            self.model_path = './new_model_checkpoints/muzero_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
    
    def prepare_train_data(self, train_x, train_y):
        """TabPFN训练数据准备 - 处理数据大小限制"""
        # TabPFN不需要归一化，但需要处理数据大小限制
        max_samples = 10000
        if train_x.shape[0] > max_samples:
            indices = np.random.choice(train_x.shape[0], max_samples, replace=False)
            train_x_sample = train_x[indices]
            train_y_sample = train_y[indices]
        else:
            train_x_sample = train_x
            train_y_sample = train_y

        self.scaler = None  # TabPFN不使用scaler
        return train_x_sample, train_y_sample

    def prepare_test_data(self, test_x):
        """TabPFN测试数据准备 - 直接返回"""
        return test_x
    
    def create_model(self):
        """创建TabPFN模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Model weight file {self.model_path} not found")
        
        # 清理GPU内存
        if self.device == 'cuda':
            torch.cuda.empty_cache()
            gc.collect()
        
        return self.model_class(device=self.device, ignore_pretraining_limits=True, model_path=self.model_path)
    
    def train(self, train_x, train_y):
        """TabPFN训练"""
        # 准备训练数据
        train_x_proc, train_y_proc = self.prepare_train_data(train_x, train_y)

        # 设置超时机制
        def timeout_handler(signum, frame):
            raise TimeoutError("TabPFN训练超时")

        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(600)  # 600秒超时

        try:
            print(f"  开始创建TabPFN模型，设备: {self.device}")
            self.model = self.create_model()
            print(f"  {self.model_type}模型创建成功")

            print(f"  开始训练{self.model_type}模型...")
            self.model.fit(train_x_proc, train_y_proc)
            print(f"  {self.model_type}模型训练成功")

            # 保存原始训练数据用于预测
            self.train_x_orig = train_x
            self.train_y_orig = train_y

            return self.model

        except Exception as e:
            self._handle_tabpfn_error(e, train_x_proc, train_y_proc)
            raise
        finally:
            signal.alarm(0)  # 取消超时

    def predict(self, test_x):
        """TabPFN预测"""
        if not hasattr(self, 'model') or self.model is None:
            raise ValueError("模型尚未训练，请先调用train方法")

        try:
            print(f"  开始{self.model_type}预测...")
            pred_test = self.model.predict(test_x)
            print(f"  {self.model_type}预测完成")

            return pred_test

        except Exception as e:
            print(f"  {self.model_type}预测出错: {str(e)}")
            raise


    
    def _handle_tabpfn_error(self, error, train_x_sample, train_y_sample):
        """处理TabPFN错误"""
        print(f"  {self.model_type}训练/预测出错: {str(error)}")
        print(f"  错误类型: {type(error).__name__}")
        traceback.print_exc()
        
        # 检查是否是序列化相关错误
        error_msg = str(error).lower()
        if "serialize" in error_msg or "pickle" in error_msg or "un-serialize" in error_msg:
            print("\n" + "="*80)
            print(f"检测到{self.model_type}序列化错误，启动调试模式")
            print("="*80)
            
            # 打印详细的调试信息
            print(f"错误详情: {str(error)}")
            print(f"数据信息:")
            print(f"  train_x_sample: shape={train_x_sample.shape}, dtype={train_x_sample.dtype}")
            print(f"  train_y_sample: shape={train_y_sample.shape}, dtype={train_y_sample.dtype}")
            print(f"  内存连续性: X_C={train_x_sample.flags['C_CONTIGUOUS']}, Y_C={train_y_sample.flags['C_CONTIGUOUS']}")
            
            # 尝试重新序列化测试
            try:
                pickle.dumps(train_x_sample)
                pickle.dumps(train_y_sample)
                print("  数据本身可以序列化")
            except Exception as pickle_err:
                print(f"  数据序列化测试失败: {pickle_err}")
            
            # 导入调试库并设置断点
            print("\n启动Python调试器...")
            try:
                print("已导入pdb调试器")
                print("可用的调试变量:")
                print("  - train_x_sample: 训练特征数据")
                print("  - train_y_sample: 训练目标数据")
                print("  - error: TabPFN异常对象")
                print("  - device: 设备信息")
                print("输入 'c' 继续执行，'q' 退出调试器")
                pdb.set_trace()  # 设置断点，暂停程序执行
            except ImportError:
                print("无法导入pdb调试器")
                try:
                    print("已导入ipdb调试器")
                    ipdb.set_trace()
                except ImportError:
                    print("无法导入ipdb调试器，使用input()暂停")
                    input("按Enter键继续...")
            
            print("="*80)
            print("调试模式结束")
            print("="*80)


class ModelManager:
    """模型管理器，统一管理所有模型的训练和评估"""

    def __init__(self, device: str = 'cpu'):
        self.device = device
        self.trainers = {
            'ols': OLSTrainer,
            'lasso': LassoTrainer,
            'xgboost': XGBoostTrainer,
            'catboost': CatBoostTrainer,
            'lightgbm': LightGBMTrainer,
            'tabpfn_default': TabPFNTrainer,
            'tabpfn_mse': TabPFNTrainer,
            'tabpfn_muzero': TabPFNTrainer
        }

        self.availability = {
            'ols': True,
            'lasso': True,
            'xgboost': True,
            'catboost': CATBOOST_AVAILABLE,
            'lightgbm': LIGHTGBM_AVAILABLE,
            'tabpfn_default': TABPFN_AVAILABLE,
            'tabpfn_mse': TABPFN_MSE_AVAILABLE,
            'tabpfn_muzero': TABPFN_MUZERO_AVAILABLE
        }

    def get_available_models(self, model_config: ModelConfig) -> List[str]:
        """获取可用的模型列表"""
        available_models = []

        # 按照固定顺序添加启用的模型
        model_order = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm',
                      'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']

        for model_type in model_order:
            if getattr(model_config, model_type) and self.availability[model_type]:
                available_models.append(model_type)

        return available_models

    def create_trainer(self, model_type: str) -> 'ModelTrainer':
        """创建模型训练器"""
        if model_type not in self.trainers:
            raise ValueError(f"Unsupported model type: {model_type}")

        if not self.availability[model_type]:
            raise ValueError(f"{model_type} is not available")

        return self.trainers[model_type](model_type, self.device)

    def calculate_metrics(self, y_true, y_pred):
        """计算评估指标（公开方法）"""
        return self._calculate_metrics(y_true, y_pred)

    def _calculate_metrics(self, y_true, y_pred) -> Dict[str, float]:
        """计算评估指标"""
        r2 = r2_score(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mape = np.mean(np.abs((y_true - y_pred) / (y_true + 1e-8))) * 100

        return {'r2': r2, 'rmse': rmse, 'mape': mape}


class FeatureImportanceCalculator:
    """特征重要性计算器"""

    @staticmethod
    def calculate_importance(model, test_x, test_y, feature_names, model_type='xgboost') -> Dict[str, Dict[str, float]]:
        """计算特征重要性"""
        importance_dict = {}

        # Permutation Importance（适用于所有模型）
        n_jobs = 1 if model_type.startswith('tabpfn') else 4
        perm_importance = permutation_importance(
            model, test_x, test_y,
            n_repeats=3, random_state=42
        ) # , n_jobs=n_jobs
        importance_dict['permutation'] = {
            name: importance for name, importance in
            zip(feature_names, perm_importance.importances_mean)
        }

        # 模型内置重要性
        builtin_importance = FeatureImportanceCalculator._get_builtin_importance(
            model, feature_names, model_type
        )
        if builtin_importance:
            importance_dict['builtin'] = builtin_importance

        return importance_dict

    @staticmethod
    def _get_builtin_importance(model, feature_names, model_type) -> Optional[Dict[str, float]]:
        """获取模型内置重要性"""
        if model_type == 'xgboost':
            return {name: importance for name, importance in
                   zip(feature_names, model.feature_importances_)}
        elif model_type in ['ols', 'lasso']:
            return {name: abs(coef) for name, coef in
                   zip(feature_names, model.coef_)}
        elif model_type in ['catboost', 'lightgbm']:
            if hasattr(model, 'feature_importances_'):
                return {name: importance for name, importance in
                       zip(feature_names, model.feature_importances_)}

        return None


class ResultCollector:
    """结果收集器，统一管理各种结果的收集和存储"""

    def __init__(self):
        self.results = []
        self.snr_results = []
        self.importance_results = []
        self.correlation_results = []
        self.mutual_info_results = []
        self.scm_objects = {}

    def add_model_result(self, dataset_name: str, model_type: str, config_key: str,
                        metrics_train: Dict, metrics_test: Dict, metrics_intv: Dict):
        """添加模型结果"""
        result_dict = {
            'dataset': dataset_name,
            'model_type': model_type,
            'use_snr': config_key,
            'r2_train': metrics_train['r2'],
            'r2_test': metrics_test['r2'],
            'r2_intv': metrics_intv['r2'],
            'rmse_train': metrics_train['rmse'],
            'rmse_test': metrics_test['rmse'],
            'rmse_intv': metrics_intv['rmse'],
            'mape_train': metrics_train['mape'],
            'mape_test': metrics_test['mape'],
            'mape_intv': metrics_intv['mape']
        }
        self.results.append(result_dict)

    def add_snr_result(self, dataset_name: str, config_key: str, snr_mean: float):
        """添加SNR结果"""
        self.snr_results.append({
            'dataset': dataset_name,
            'use_snr': config_key,
            'snr_mean': snr_mean
        })

    def add_importance_result(self, dataset_name: str, model_type: str, config_key: str, importance: Dict):
        """添加特征重要性结果"""
        self.importance_results.append({
            'dataset': dataset_name,
            'model_type': model_type,
            'use_snr': config_key,
            'importance': importance
        })

    def add_correlation_result(self, dataset_name: str, config_key: str, correlation_matrices: Dict,
                              target_node: str, feature_names: List[str]):
        """添加相关系数结果"""
        self.correlation_results.append({
            'dataset': dataset_name,
            'config_key': config_key,
            'correlation_matrices': correlation_matrices,
            'target_node': target_node,
            'feature_names': feature_names
        })

    def add_mutual_info_result(self, dataset_name: str, config_key: str, mutual_info_matrices: Dict,
                              target_node: str, feature_names: List[str]):
        """添加互信息结果"""
        self.mutual_info_results.append({
            'dataset': dataset_name,
            'config_key': config_key,
            'mutual_info_matrices': mutual_info_matrices,
            'target_node': target_node,
            'feature_names': feature_names
        })

    def add_scm_object(self, dataset_idx: int, config_key: str, scm_info: Dict):
        """添加SCM对象信息"""
        if dataset_idx not in self.scm_objects:
            self.scm_objects[dataset_idx] = {}
        self.scm_objects[dataset_idx][config_key] = scm_info

    def extend_from_config_result(self, config_result: Dict):
        """从配置结果中扩展数据"""
        self.results.extend(config_result['results'])
        self.snr_results.extend(config_result['snr_results'])
        self.importance_results.extend(config_result['importance_results'])
        self.correlation_results.extend(config_result['correlation_results'])
        self.mutual_info_results.extend(config_result['mutual_info_results'])

        # 合并SCM对象信息
        for dataset_idx, scm_info in config_result['scm_objects_info'].items():
            self.add_scm_object(dataset_idx, config_result['config_key'], scm_info)


class CorrelationCalculator:
    """相关系数和互信息计算器"""

    @staticmethod
    def calculate_correlations_and_mi(train_data, test_data, intervention_data,
                                    feature_names, target_node) -> Tuple[Dict, Dict]:
        """计算三种数据集的相关系数和互信息"""
        # 完整的特征名称（包括目标变量）
        all_feature_names = feature_names + [target_node] if target_node not in feature_names else feature_names

        # 在三种数据集上分别计算相关系数和互信息矩阵
        dataset_correlations = {}
        dataset_mutual_infos = {}

        for split_name, data in [('train', train_data), ('test', test_data), ('intervention', intervention_data)]:
            print(f"计算{split_name}数据集的相关系数和互信息（样本数: {data.shape[0]}）")
            corr_mi_result = calculate_pairwise_correlations_and_mi(data, all_feature_names)
            dataset_correlations[split_name] = corr_mi_result['correlation_matrix']
            dataset_mutual_infos[split_name] = corr_mi_result['mutual_info_matrix']

        return dataset_correlations, dataset_mutual_infos


def calculate_effective_datasets(results, r2_threshold=0.5):
    """基于R2阈值计算有效数据集"""
    if not results:
        return {}, {}

    # 按配置分组数据
    config_data = {}
    for r in results:
        config_key = r.get('use_snr', 'unknown')
        dataset_name = r.get('dataset', 'unknown')
        model_type = r.get('model_type', 'unknown')

        if config_key not in config_data:
            config_data[config_key] = {}
        if dataset_name not in config_data[config_key]:
            config_data[config_key][dataset_name] = {}

        # 提取R2值 - 使用r2_train
        r2_train = r.get('r2_train', None)
        config_data[config_key][dataset_name][model_type] = r2_train

    # 计算有效数据集
    effective_datasets = {}
    for config_key, datasets in config_data.items():
        effective_dataset_set = set()

        # 记录该配置下的总数据集数量
        total_datasets_count = len(datasets)

        # 转换为DataFrame进行处理
        dataset_records = []
        for dataset_name, models in datasets.items():
            record = {
                'dataset': dataset_name,
                'xgb_r2': models.get('xgboost', None),
                'tabpfn_default_r2': models.get('tabpfn_default', None),
                'tabpfn_mse_r2': models.get('tabpfn_mse', None),
                'tabpfn_muzero_r2': models.get('tabpfn_muzero', None)
            }
            dataset_records.append(record)

        if not dataset_records:
            # 如果没有数据记录，设置eff_equ_all为False
            effective_datasets[config_key] = {'datasets': set(), 'eff_equ_all': False}
            continue

        df = pd.DataFrame(dataset_records)

        # 检查有效性 - 检查XGBoost是否有有效值
        xgb_valid_mask = df['xgb_r2'].notna()

        # 检查TabPFN是否有任何有效值（不为None且大于0.001）
        tabpfn_valid_mask = (
            (df['tabpfn_default_r2'].notna() & (df['tabpfn_default_r2'] > 0.001)) |
            (df['tabpfn_mse_r2'].notna() & (df['tabpfn_mse_r2'] > 0.001)) |
            (df['tabpfn_muzero_r2'].notna() & (df['tabpfn_muzero_r2'] > 0.001))
        )

        # 计算最佳TabPFN R2值
        df['best_tabpfn_r2'] = df[['tabpfn_default_r2', 'tabpfn_mse_r2', 'tabpfn_muzero_r2']].max(axis=1, skipna=True)
        df.loc[~tabpfn_valid_mask, 'best_tabpfn_r2'] = None

        # 对于有TabPFN结果的数据集，要求两个模型都满足阈值
        both_valid_mask = xgb_valid_mask & tabpfn_valid_mask
        both_valid_df = df[both_valid_mask]

        if len(both_valid_df) > 0:
            both_r2_mask = (both_valid_df['xgb_r2'] >= r2_threshold) & (both_valid_df['best_tabpfn_r2'] >= r2_threshold)
            both_effective = both_valid_df[both_r2_mask]['dataset'].tolist()
            effective_dataset_set.update(both_effective)

        # 对于只有XGBoost结果的数据集，只检查XGBoost
        only_xgb_mask = xgb_valid_mask & ~tabpfn_valid_mask
        only_xgb_df = df[only_xgb_mask]

        if len(only_xgb_df) > 0:
            xgb_r2_mask = only_xgb_df['xgb_r2'] >= r2_threshold
            xgb_effective = only_xgb_df[xgb_r2_mask]['dataset'].tolist()
            effective_dataset_set.update(xgb_effective)

        # 计算有效数据集数量并添加eff_equ_all标志
        effective_count = len(effective_dataset_set)
        eff_equ_all = (effective_count == total_datasets_count)

        # 创建包含数据集和eff_equ_all标志的字典
        effective_datasets[config_key] = {
            'datasets': effective_dataset_set,
            'eff_equ_all': eff_equ_all
        }

    return config_data, effective_datasets


def process_single_configuration(config_key, config_info, h_config, n, intervention_node_type,
                                intervention_value_method, custom_dag_type, model_types,
                                device, gpu_id, custom_dag_size=None, image_dir=None,
                                shared_drawn_dag_signatures=None, avg_in_degree=(1.2, 1.7),
                                calculate_correlation_mi=True, extract_function_weights=True):
    """
    处理单个配置的所有数据集
    """
    # 设置GPU环境变量
    os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
    print(f"工作进程开始处理配置 {config_key}，使用GPU {gpu_id}")

    # 重新初始化CUDA上下文
    if device == 'cuda' and torch.cuda.is_available():
        try:
            torch.cuda.empty_cache()
            torch.cuda.set_device(0)
            print(f"配置 {config_key}: CUDA设备已设置为GPU {gpu_id}")
        except RuntimeError as e:
            print(f"配置 {config_key}: CUDA初始化失败: {e}")
            print(f"配置 {config_key}: 回退到CPU模式")
            device = 'cpu'

    config_name = config_info['name']
    custom_functions = config_info['config']

    print(f"\n{'='*30}\n配置: {config_name} ({config_key})\n{'='*30}")
    print("自定义函数配置:")
    for node, node_config in custom_functions.items():
        print(f"  节点 {node}: {node_config}")

    print(f"开始生成数据集，配置: {config_name}")

    # 生成数据集
    datasets = generate_datasets(
        num_dataset=n,
        h_config=h_config,
        perturbation_type='counterfactual',
        perturbation_node_type=intervention_node_type,
        perturbation_value_method=intervention_value_method,
        custom_dag_type=custom_dag_type,
        custom_functions=custom_functions,
        custom_dag_size=custom_dag_size,
        node_unobserved=False,
        seed=42,
        allow_skip=True,
        avg_in_degree=avg_in_degree
    )

    # 初始化组件
    model_manager = ModelManager(device)
    result_collector = ResultCollector()
    importance_calculator = FeatureImportanceCalculator()
    correlation_calculator = CorrelationCalculator()

    # 串行处理该配置下的所有数据集
    for idx, dataset in enumerate(datasets):
        dataset_name = dataset[0]
        xs_original, ys_original = DataProcessor.convert_tensors_to_numpy(dataset[1], dataset[2])
        xs_intervention, ys_intervention = DataProcessor.convert_tensors_to_numpy(dataset[3], dataset[4])
        scm = dataset[5]

        print(f"配置 {config_name}: 处理数据集 {idx+1}/{len(datasets)} ({dataset_name})")

        # 获取特征名
        feature_names = scm.selected_features if hasattr(scm, 'selected_features') else [f'X{i}' for i in range(xs_original.shape[1])]

        # 绘制DAG图
        draw_dag_if_needed(scm, idx, config_key, config_name, image_dir, shared_drawn_dag_signatures)

        # 处理SNR结果
        snr_mean = extract_snr_mean(scm)
        result_collector.add_snr_result(dataset_name, config_key, snr_mean)

        # 分割训练测试数据
        train_test_split_ratio = h_config.get('train_test_split_ratio', 0.5)
        train_xs, train_ys, test_xs_original, test_ys_original = DataProcessor.split_train_test(
            xs_original, ys_original, train_test_split_ratio
        )
        _, _, test_xs_intervention, test_ys_intervention = DataProcessor.split_train_test(
            xs_intervention, ys_intervention, train_test_split_ratio
        )

        # 计算相关系数和互信息
        if calculate_correlation_mi:
            process_correlation_and_mi(
                train_xs, train_ys, test_xs_original, test_ys_original,
                test_xs_intervention, test_ys_intervention, feature_names, scm,
                dataset_name, config_key, config_name, idx, result_collector, correlation_calculator
            )

        # 处理所有模型
        for model_type in model_types:
            process_single_model(
                model_type, model_manager, importance_calculator,
                train_xs, train_ys, test_xs_original, test_ys_original,
                test_xs_intervention, test_ys_intervention, feature_names,
                dataset_name, config_key, config_name, idx, result_collector
            )

    print(f"配置 {config_name} 处理完成，共处理 {len(datasets)} 个数据集")

    # 返回结果
    return {
        'config_key': config_key,
        'config_name': config_name,
        'results': result_collector.results,
        'snr_results': result_collector.snr_results,
        'importance_results': result_collector.importance_results,
        'correlation_results': result_collector.correlation_results if calculate_correlation_mi else [],
        'mutual_info_results': result_collector.mutual_info_results if calculate_correlation_mi else [],
        'scm_objects_info': extract_serializable_scm_info(
            {idx: datasets[idx][5] for idx in range(len(datasets))}, extract_function_weights
        )
    }


def draw_dag_if_needed(scm, idx, config_key, config_name, image_dir, shared_drawn_dag_signatures):
    """绘制DAG图（如果需要）"""
    if image_dir is not None and shared_drawn_dag_signatures is not None:
        if should_draw_dag_shared(scm, shared_drawn_dag_signatures):
            dag_filename = f'dag_dataset_{idx}_{config_key}.png'
            dag_path = os.path.join(image_dir, dag_filename)

            try:
                intervention_or_perturb_nodes = getattr(scm, 'intervention_nodes', None)
                if intervention_or_perturb_nodes is None:
                    intervention_or_perturb_nodes = getattr(scm, 'perturbation_nodes', None)

                draw_graph(
                    scm.dag,
                    dag_path,
                    target_node=getattr(scm, 'selected_target', None),
                    intervention_nodes=intervention_or_perturb_nodes,
                    unobserved_nodes=getattr(scm, 'unobserved_nodes', None),
                    selected_features=getattr(scm, 'selected_features', None),
                    assignment=getattr(scm, 'assignment', None),
                    scm=scm,
                    model_results=None
                )
                print(f"配置 {config_name}, 数据集 {idx+1}: 已绘制DAG图 -> {dag_path}")
            except Exception as e:
                print(f"配置 {config_name}, 数据集 {idx+1}: 绘制DAG图时出错: {str(e)}")
        else:
            print(f"配置 {config_name}, 数据集 {idx+1}: 跳过DAG图绘制（相同结构已存在）")
    else:
        print(f"配置 {config_name}, 数据集 {idx+1}: 跳过DAG图绘制（未提供image_dir或shared_drawn_dag_signatures）")


def extract_snr_mean(scm) -> float:
    """提取SNR均值"""
    snr_mean = np.nan
    if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
        actual_snr = scm.snr_validation_results.get('actual_snr', {})
        if actual_snr:
            valid_snr_values = [v for v in actual_snr.values() if v is not None and not np.isnan(v)]
            if valid_snr_values:
                snr_mean = np.mean(valid_snr_values)
    return snr_mean


def process_correlation_and_mi(train_xs, train_ys, test_xs_original, test_ys_original,
                               test_xs_intervention, test_ys_intervention, feature_names, scm,
                               dataset_name, config_key, config_name, idx, result_collector, correlation_calculator):
    """处理相关系数和互信息计算"""
    print(f"配置 {config_name}, 数据集 {idx+1}: 计算相关系数和互信息...")

    # 获取目标节点名称
    target_node = getattr(scm, 'selected_target', None)
    if target_node is None and feature_names:
        target_node = feature_names[-1]

    # 准备三种数据集
    train_data = np.hstack([train_xs, train_ys.reshape(-1, 1)])
    test_data = np.hstack([test_xs_original, test_ys_original.reshape(-1, 1)])
    intervention_data = np.hstack([test_xs_intervention, test_ys_intervention.reshape(-1, 1)])

    # 计算相关系数和互信息
    dataset_correlations, dataset_mutual_infos = correlation_calculator.calculate_correlations_and_mi(
        train_data, test_data, intervention_data, feature_names, target_node
    )

    # 完整的特征名称
    all_feature_names = feature_names + [target_node] if target_node not in feature_names else feature_names

    # 存储结果
    result_collector.add_correlation_result(dataset_name, config_key, dataset_correlations, target_node, all_feature_names)
    result_collector.add_mutual_info_result(dataset_name, config_key, dataset_mutual_infos, target_node, all_feature_names)


def process_single_model(model_type, model_manager, importance_calculator,
                         train_xs, train_ys, test_xs_original, test_ys_original,
                         test_xs_intervention, test_ys_intervention, feature_names,
                         dataset_name, config_key, config_name, idx, result_collector):
    """处理单个模型的训练和评估"""
    print(f"配置 {config_name}, 数据集 {idx+1}: 训练 {model_type.upper()} 模型...")

    try:
        # 创建并训练模型
        trainer = model_manager.create_trainer(model_type)
        trainer.train(train_xs, train_ys)

        # 评估模型性能
        pred_train = trainer.predict(train_xs)
        pred_test = trainer.predict(test_xs_original)
        pred_intv = trainer.predict(test_xs_intervention)

        metrics_train = model_manager.calculate_metrics(train_ys, pred_train)
        metrics_test = model_manager.calculate_metrics(test_ys_original, pred_test)
        metrics_intv = model_manager.calculate_metrics(test_ys_intervention, pred_intv)

        # 存储结果
        result_collector.add_model_result(
            dataset_name, model_type, config_key,
            metrics_train, metrics_test, metrics_intv
        )

        # 计算特征重要性
        print(f"配置 {config_name}, 数据集 {idx+1}: 计算 {model_type.upper()} 特征重要性...")

        # 对于需要归一化的模型，使用归一化后的测试数据
        if trainer.needs_scaling():
            test_x_for_importance = trainer.scaler.transform(test_xs_original)
        else:
            test_x_for_importance = test_xs_original

        importance = importance_calculator.calculate_importance(
            trainer.model, test_x_for_importance, test_ys_original, feature_names, model_type
        )

        result_collector.add_importance_result(dataset_name, model_type, config_key, importance)

        print(f"配置 {config_name}, 数据集 {idx+1}, {model_type.upper()} 训练完成 - "
              f"R2 Train: {metrics_train['r2']:.3f}, "
              f"Test: {metrics_test['r2']:.3f}, "
              f"Intv: {metrics_intv['r2']:.3f}")

    except Exception as e:
        print(f"配置 {config_name}, 数据集 {idx+1}: 训练 {model_type.upper()} 时出错: {str(e)}")
        traceback.print_exc()


def check_unavailable_models(model_config: ModelConfig):
    """检查不可用的模型"""
    if model_config.catboost and not CATBOOST_AVAILABLE:
        print("⚠️  CatBoost已启用但不可用，请安装: pip install catboost")
    if model_config.lightgbm and not LIGHTGBM_AVAILABLE:
        print("⚠️  LightGBM已启用但不可用，请安装: pip install lightgbm")
    if model_config.tabpfn_default and not TABPFN_AVAILABLE:
        print("⚠️  TabPFN默认模型已启用但不可用，请安装: pip install tabpfn")
    if model_config.tabpfn_mse and not TABPFN_MSE_AVAILABLE:
        print("⚠️  TabPFN MSE变体已启用但不可用，请检查inference.regressor_mse模块")
    if model_config.tabpfn_muzero and not TABPFN_MUZERO_AVAILABLE:
        print("⚠️  TabPFN Muzero变体已启用但不可用，请检查inference.regressor_muzero模块")


def generate_custom_functions_configs(snr_config: SNRConfig, node_function_configs: NodeFunctionConfigs) -> Dict[str, Dict]:
    """生成自定义函数配置"""
    custom_functions_configs = {}

    # 使用配置类生成SNR配置
    for i in snr_config.parent_snr_values:
        for j_mult in snr_config.child_snr_multipliers:
            for k_mult in snr_config.other_snr_multipliers:
                j = i * j_mult
                k = max(i, j) * k_mult

                key = f'parent_snr_{i}_child_snr_{j}_other_snr_{k}'
                parent_snr = float(i)
                child_snr = float(j)
                other_snr = float(k)

                # 为每个节点类型生成不同的函数配置
                def create_node_config(node_config, snr_value):
                    """创建单个节点的配置字典"""
                    config = {
                        'type': node_config.function_type
                    }

                    # 神经网络相关参数（仅对neural_network类型有效）
                    if node_config.function_type == 'random_neural_network':
                        config['hidden_dim'] = node_config.hidden_dim
                        config['depth'] = node_config.depth
                        config['activation'] = node_config.activation

                    # 函数特定参数
                    if node_config.coefficients is not None:
                        config['coefficients'] = node_config.coefficients
                    if node_config.bias is not None:
                        config['bias'] = node_config.bias
                    if node_config.degree is not None:
                        config['degree'] = node_config.degree
                    if node_config.scale is not None:
                        config['scale'] = node_config.scale
                    if node_config.frequency is not None:
                        config['frequency'] = node_config.frequency
                    if node_config.n_components is not None:
                        config['n_components'] = node_config.n_components
                    if node_config.centers is not None:
                        config['centers'] = node_config.centers

                    # SNR和noise_std互斥配置
                    if node_config.target_snr is not None:
                        # 如果节点配置中指定了target_snr，使用节点配置的值
                        config['target_snr'] = node_config.target_snr
                    elif node_config.noise_std is not None:
                        # 如果节点配置中指定了noise_std，使用固定噪声标准差
                        config['noise_std'] = node_config.noise_std
                    else:
                        # 否则使用SNR配置生成的值
                        config['target_snr'] = snr_value

                    # 噪声均值配置
                    if node_config.noise_mean is not None:
                        config['noise_mean'] = node_config.noise_mean

                    # 噪声均值模式配置
                    if node_config.noise_mean_mode != 'fixed':
                        config['noise_mean_mode'] = node_config.noise_mean_mode

                    # g函数配置
                    if node_config.g_function_config is not None:
                        # 如果有完整的g函数配置，使用完整配置
                        config['g_function_config'] = node_config.g_function_config
                    elif node_config.g_function_type != 'identity':
                        # 否则使用简单的g函数类型
                        config['g_function_type'] = node_config.g_function_type

                    return config

                target_function_config = create_node_config(node_function_configs.target_config, parent_snr)
                target_child_function_config = create_node_config(node_function_configs.target_child_config, child_snr)
                other_function_config = create_node_config(node_function_configs.other_config, other_snr)

                custom_functions_configs[key] = {
                    'name': key,
                    'config': {
                        'target': target_function_config,
                        'target_child': target_child_function_config,
                        'Other_type': other_function_config
                    }
                }

    return custom_functions_configs


def generate_visualizations_and_save_results(main_result_collector: ResultCollector,
                                            image_dir: str, test_config: TestConfig,
                                            custom_functions_configs: Dict):
    """生成可视化图表和保存结果"""
    print("\n生成可视化图表...")

    # 计算有效数据集
    print("\n计算有效数据集...")
    _, effective_datasets = calculate_effective_datasets(main_result_collector.results, test_config.r2_threshold)
    print(f"计算得到有效数据集: {len(effective_datasets)} 个配置")

    # 生成自适应可视化
    print("\n生成自适应可视化...")

    # R²性能对比图
    r2_plotter = R2Plotter()
    r2_plotter.plot_r2_comparison(
        main_result_collector.results, image_dir, test_config.r2_threshold,
        test_config.annotate_all_datasets, test_config.annotate_effective_datasets,
        effective_datasets=effective_datasets
    )

    # Permutation importance箱线图对比
    importance_plotter = ImportancePlotter()
    importance_plotter.plot_permutation_importance_comparison(
        main_result_collector.importance_results, image_dir,
        scm_objects=main_result_collector.scm_objects,
        custom_functions_configs=custom_functions_configs,
        results=main_result_collector.results,
        r2_threshold=test_config.r2_threshold,
        annotate_all_datasets=test_config.annotate_all_datasets,
        annotate_effective_datasets=test_config.annotate_effective_datasets,
        effective_datasets=effective_datasets
    )

    print("\n处理综合数据并保存文件...")

    # 保存json文件
    comprehensive_result = process_comprehensive_data(
        main_result_collector.scm_objects, main_result_collector.results,
        main_result_collector.importance_results, image_dir, custom_functions_configs,
        save_json=test_config.save_json_file,
        correlation_results=main_result_collector.correlation_results,
        mutual_info_results=main_result_collector.mutual_info_results
    )

    # 生成汇总CSV文件
    print("\n生成汇总CSV文件...")
    if comprehensive_result and 'structured_data' in comprehensive_result:
        save_summary_csv(
            image_dir, comprehensive_result['structured_data'],
            custom_dag_type=test_config.custom_dag_type,
            results=main_result_collector.results,
            r2_threshold=test_config.r2_threshold,
            annotate_all_datasets=test_config.annotate_all_datasets,
            annotate_effective_datasets=test_config.annotate_effective_datasets,
            effective_datasets=effective_datasets
        )
    else:
        print("警告：没有内存数据可用于生成汇总CSV文件")

def main():
    
    # 基础配置
    h_config = {
        'device': 'cpu',
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'sample_root_std': False,
        'min_root': 0.0,
        'max_root': 1.0,
        'max_range': 0.5,
        'sample_cause_ranges': False,
        'sample_std': False,
        'min_num_samples': 1000,
        'max_num_samples': 1000,
        'train_test_split_ratio': 0.7,
        'task': 'regression',
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        'min_num_node': 5,
        'max_num_node': 20,
        'num_layers': 3,
        'max_num_children': 10,
        'max_num_classes': 5,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,
        'use_monte_carlo_precompute': False
    }

    # SNR配置
    snr_config = SNRConfig(
        parent_snr_values=[2.0, 4.0],  # 可以修改为 [1.0, 2.0, 3.0] 等多个值
        child_snr_multipliers=[0.1, 1.0],  # 子节点SNR倍数
        other_snr_multipliers=[0.1, 1.0]   # 其他节点SNR倍数
    )

    # 节点函数配置 - 支持不同节点类型使用不同函数和g函数
    node_function_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'  
        ),

        target_child_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'  
        ),
        
        other_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'  
        )
    )

    # 测试配置
    test_config = TestConfig(
        n_datasets=2,
        intervention_node_type='all_non_family',
        intervention_value_method='sample',
        custom_dag_type='random_SF',
        custom_dag_size='small',
        avg_in_degree=(1.2, 1.7),
        device='cuda' if torch.cuda.is_available() else 'cpu',
        num_workers=min(multiprocessing.cpu_count(), 8),
        r2_threshold=0.5,
        save_json_file=True,
        calculate_correlation_mi=True,
        extract_function_weights=True,
        annotate_all_datasets=True,
        annotate_effective_datasets=True,
        snr_config=snr_config,
        node_function_configs=node_function_configs
    )

    # 模型配置
    model_config = ModelConfig(
        ols=True,
        lasso=False,
        catboost=False,
        xgboost=True,
        lightgbm=True,
        tabpfn_default=True,
        tabpfn_mse=True,
        tabpfn_muzero=True
    )

    # 动态获取可用GPU列表
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        gpu_ids = list(range(gpu_count))
        print(f"检测到 {gpu_count} 个可用GPU: {gpu_ids}")
    else:
        gpu_ids = [0]
        print("未检测到CUDA，将使用CPU模式")

    print(f"并行处理配置: {test_config.num_workers} 个工作进程, GPU分配策略: {gpu_ids}")

    # 获取可用模型
    model_manager = ModelManager(test_config.device)
    model_types = model_manager.get_available_models(model_config)

    print(f"模型配置: {model_config}")
    print(f"启用的模型类型: {model_types}")

    # 检查不可用的模型
    check_unavailable_models(model_config)

    # 创建输出目录
    run_time = datetime.now().strftime('%Y%m%d_%H%M%S')
    image_dir = os.path.join('images', run_time)
    os.makedirs(image_dir, exist_ok=True)
    print(f"输出目录: {image_dir}")

    # 初始化结果收集器
    main_result_collector = ResultCollector()

    # 创建共享的drawn_dag_signatures字典
    with multiprocessing.Manager() as manager:
        shared_drawn_dag_signatures = manager.dict()

        # 生成配置
        custom_functions_configs = generate_custom_functions_configs(
            test_config.snr_config, test_config.node_function_configs
        )

        print(f"\n{'='*50}")
        print(f"测试模式: 不同custom_functions配置比较 (DAG类型: {test_config.custom_dag_type})")
        print(f"{'='*50}")

        # 并行处理不同配置
        print(f"开始并行处理 {len(custom_functions_configs)} 个配置...")

        # 准备任务参数
        task_args = []
        for task_index, (config_key, config_info) in enumerate(custom_functions_configs.items()):
            assigned_gpu = gpu_ids[task_index % len(gpu_ids)]
            task_args.append((
                config_key, config_info, h_config, test_config.n_datasets,
                test_config.intervention_node_type, test_config.intervention_value_method,
                test_config.custom_dag_type, model_types, test_config.device, assigned_gpu,
                test_config.custom_dag_size, image_dir, shared_drawn_dag_signatures,
                test_config.avg_in_degree, test_config.calculate_correlation_mi,
                test_config.extract_function_weights
            ))

        # 使用进程池并行执行
        with concurrent.futures.ProcessPoolExecutor(max_workers=test_config.num_workers) as executor:
            future_to_config = {
                executor.submit(process_single_configuration, *args): args[0]
                for args in task_args
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_config):
                config_key = future_to_config[future]
                try:
                    config_result = future.result()
                    main_result_collector.extend_from_config_result(config_result)
                    print(f"配置 {config_result['config_name']} 处理完成")
                except Exception as exc:
                    print(f"配置 {config_key} 处理时发生异常: {exc}")
                    traceback.print_exc()

        print(f"所有配置并行处理完成！")

        # 生成可视化和保存结果
        generate_visualizations_and_save_results(
            main_result_collector, image_dir, test_config, custom_functions_configs
        )

    print(f"\n所有图表已保存到: {image_dir}")
    
if __name__ == '__main__':
    # Linux系统上设置multiprocessing启动方法为spawn
    multiprocessing.set_start_method('spawn', force=True)
    main()
