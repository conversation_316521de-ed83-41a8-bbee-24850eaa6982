import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from scipy.stats import dirichlet


def generate_random_dag(n_nodes, er_degree=1):
    """
    生成一个随机DAG
    n_nodes: 节点数量
    er_degree: Erdős–Rényi图的期望度数
    """
    # 首先生成一个下三角矩阵
    prob = er_degree / (n_nodes - 1) if n_nodes > 1 else 0
    adjacency = np.zeros((n_nodes, n_nodes))
    for i in range(1, n_nodes):
        for j in range(i):
            if np.random.random() < prob:
                adjacency[i, j] = 1

    # 随机排列节点顺序以确保随机性
    perm = np.random.permutation(n_nodes)
    adjacency = adjacency[perm, :][:, perm]

    return adjacency


def generate_data(adjacency, data_generator, n_samples, intervention_nodes=None, intervention_type='hard'):
    """
    生成数据（可以是观测数据或干预数据）
    adjacency: 邻接矩阵
    data_generator: 数据生成器函数
    n_samples: 样本数量
    intervention_nodes: 干预节点列表，None表示生成观测数据
    intervention_type: 干预类型 ('hard' 或 'soft')
    """
    n_nodes = adjacency.shape[0]
    data = []
    intervention_targets = []

    if intervention_nodes is None:
        # 生成观测数据
        obs_data = data_generator(adjacency, n_samples, None, intervention_type)
        for i in range(n_samples):
            data.append(obs_data[i])
            intervention_targets.append(-1)  # -1表示无干预
    else:
        # 生成干预数据
        samples_per_node = n_samples // len(intervention_nodes)
        remaining_samples = n_samples % len(intervention_nodes)

        for idx, node in enumerate(intervention_nodes):
            # 为最后一个节点分配剩余样本
            current_samples = samples_per_node
            if idx == len(intervention_nodes) - 1:
                current_samples += remaining_samples

            node_data = data_generator(adjacency, current_samples, node, intervention_type)
            for i in range(current_samples):
                data.append(node_data[i])
                intervention_targets.append(node)

    # 转换为numpy数组
    data = np.array(data)
    intervention_targets = np.array(intervention_targets)

    return data, intervention_targets


def generate_linear_data(adjacency, n_samples, intervention_node=None, intervention_type='hard'):
    """
    生成线性模型数据
    """
    n_nodes = adjacency.shape[0]
    data = np.zeros((n_samples, n_nodes))

    # 初始化权重
    weights = np.zeros_like(adjacency, dtype=float)
    for i in range(n_nodes):
        for j in range(n_nodes):
            if adjacency[i, j] == 1:
                weights[i, j] = np.random.normal(0, 1.5)  # 截断正态分布

    # 初始化偏置项
    biases = np.random.uniform(-0.5, 0.5, n_nodes)

    # 生成数据
    for s in range(n_samples):
        # 按照拓扑顺序生成数据
        for i in range(n_nodes):
            if intervention_node is not None and i == intervention_node and intervention_type == 'hard':
                # 硬干预：设置为随机值，忽略父节点
                data[s, i] = np.random.uniform(-1, 1)
            else:
                # 计算父节点的加权和
                parent_contribution = np.sum(weights[i, :] * data[s, :])
                # 添加噪声
                noise = np.random.normal(0, 0.1)
                data[s, i] = parent_contribution + biases[i] + noise

    return data


def generate_anm_data(adjacency, n_samples, intervention_node=None, intervention_type='hard'):
    """
    生成非线性加性噪声模型(ANM)数据
    """
    n_nodes = adjacency.shape[0]
    data = np.zeros((n_samples, n_nodes))

    # 为每个节点创建一个小型神经网络
    class ANMNetwork(nn.Module):
        def __init__(self, n_nodes):
            super(ANMNetwork, self).__init__()
            self.layers = nn.ModuleList([
                nn.Linear(n_nodes, 10) for _ in range(n_nodes)
            ])

        def forward(self, x, node_idx):
            h = self.layers[node_idx](x)
            return F.leaky_relu(h, 0.25).sum(dim=1)

    networks = ANMNetwork(n_nodes)

    # 随机初始化网络权重
    for param in networks.parameters():
        nn.init.normal_(param, 0, 1)

    # 生成数据
    for s in range(n_samples):
        # 按照拓扑顺序生成数据
        for i in range(n_nodes):
            if intervention_node is not None and i == intervention_node and intervention_type == 'hard':
                # 硬干预：设置为随机值，忽略父节点
                data[s, i] = np.random.normal(2, 1)
            else:
                # 计算非线性函数值
                x = torch.FloatTensor(data[s:s + 1, :])
                with torch.no_grad():
                    parent_contribution = networks.forward(x, i).numpy()[0]

                # 添加噪声
                noise_std = np.random.uniform(1, 2)
                noise = np.random.normal(0, noise_std)
                data[s, i] = parent_contribution + 0.4 * noise

    return data


def generate_nn_data(adjacency, n_samples, intervention_node=None, intervention_type='hard'):
    """
    生成非线性非加性噪声神经网络模型(NN)数据
    """
    n_nodes = adjacency.shape[0]
    data = np.zeros((n_samples, n_nodes))

    # 为每个节点创建一个小型神经网络
    class NNNetwork(nn.Module):
        def __init__(self, n_nodes):
            super(NNNetwork, self).__init__()
            self.layers = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(n_nodes + 1, 20),  # +1 for noise input
                    nn.Tanh(),
                    nn.Linear(20, 1)
                ) for _ in range(n_nodes)
            ])

        def forward(self, x, noise, node_idx):
            inputs = torch.cat([x, noise.unsqueeze(1)], dim=1)
            return self.layers[node_idx](inputs).squeeze()

    networks = NNNetwork(n_nodes)

    # 随机初始化网络权重
    for param in networks.parameters():
        nn.init.normal_(param, 0, 1)

    # 生成数据
    for s in range(n_samples):
        # 按照拓扑顺序生成数据
        for i in range(n_nodes):
            if intervention_node is not None and i == intervention_node and intervention_type == 'hard':
                # 硬干预：设置为随机值，忽略父节点
                data[s, i] = np.random.normal(2, 1)
            else:
                # 生成噪声
                noise_std = np.random.uniform(1, 2)
                noise = np.random.normal(0, noise_std)

                # 计算非线性函数值
                x = torch.FloatTensor(data[s:s + 1, :])
                noise_tensor = torch.FloatTensor([noise])
                with torch.no_grad():
                    data[s, i] = networks.forward(x, noise_tensor, i).numpy()

    return data


def generate_mlp_data(adjacency, n_samples, intervention_node=None, intervention_type='hard', n_categories=3):
    """
    生成MLP离散数据
    """
    n_nodes = adjacency.shape[0]
    # 修改：初始化为1而不是0，避免在one-hot编码时出错
    data = np.ones((n_samples, n_nodes), dtype=int)

    # 为每个节点创建一个小型MLP网络
    class MLPNetwork(nn.Module):
        def __init__(self, n_nodes, n_categories):
            super(MLPNetwork, self).__init__()
            self.layers = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(n_nodes * n_categories, 32),
                    nn.ReLU(),
                    nn.Linear(32, 32),
                    nn.ReLU(),
                    nn.Linear(32, n_categories)
                ) for _ in range(n_nodes)
            ])
            self.n_categories = n_categories

        def forward(self, x, node_idx):
            # 将离散值转换为one-hot编码
            x_one_hot = F.one_hot(x.long(), self.n_categories).float()
            x_flat = x_one_hot.reshape(x.size(0), -1)
            logits = self.layers[node_idx](x_flat)
            return logits

    networks = MLPNetwork(n_nodes, n_categories)

    # 随机初始化网络权重
    for param in networks.parameters():
        nn.init.normal_(param, 0, 0.1)

    # 生成数据
    for s in range(n_samples):
        # 按照拓扑顺序生成数据
        for i in range(n_nodes):
            if intervention_node is not None and i == intervention_node and intervention_type == 'hard':
                # 硬干预：随机选择一个类别
                data[s, i] = np.random.randint(1, n_categories + 1)
            else:
                # 计算条件概率
                x = torch.LongTensor(data[s:s + 1, :])
                with torch.no_grad():
                    logits = networks.forward(x, i).numpy()[0]

                # 使用softmax计算概率
                probs = np.exp(logits) / np.sum(np.exp(logits))

                # 根据概率采样类别
                data[s, i] = np.random.choice(np.arange(1, n_categories + 1), p=probs)

    return data


def generate_dirichlet_data(adjacency, n_samples, intervention_node=None, intervention_type='hard', n_categories=3,
                            alpha=0.1):
    """
    生成Dirichlet离散数据
    """
    n_nodes = adjacency.shape[0]
    # 修改：初始化为1而不是0，确保所有值都在有效范围内
    data = np.ones((n_samples, n_nodes), dtype=int)

    # 为每个节点和父节点组合创建条件概率表
    cpt = {}
    for i in range(n_nodes):
        parents = np.where(adjacency[i, :] == 1)[0]
        parent_configs = 1
        if len(parents) > 0:
            parent_configs = n_categories ** len(parents)

        # 为每种父节点配置生成一个条件概率分布
        for j in range(parent_configs):
            if intervention_type == 'soft' and intervention_node == i:
                # 软干预：使用不同的alpha值
                alpha_value = np.random.choice([0.1, 0.3, 0.5, 0.7, 0.9])
                cpt[(i, j)] = dirichlet.rvs([alpha_value] * n_categories)[0]
            else:
                cpt[(i, j)] = dirichlet.rvs([alpha] * n_categories)[0]

    # 生成数据
    for s in range(n_samples):
        # 按照拓扑顺序生成数据
        for i in range(n_nodes):
            if intervention_node is not None and i == intervention_node and intervention_type == 'hard':
                # 硬干预：随机选择一个类别
                data[s, i] = np.random.randint(1, n_categories + 1)
            else:
                # 找到父节点
                parents = np.where(adjacency[i, :] == 1)[0]

                # 计算父节点配置的索引
                parent_config = 0
                if len(parents) > 0:
                    for p_idx, p in enumerate(parents):
                        # 确保父节点值在有效范围内（1到n_categories）
                        parent_value = max(1, min(n_categories, data[s, p]))
                        parent_config += (parent_value - 1) * (n_categories ** p_idx)

                # 获取对应的条件概率分布
                probs = cpt[(i, parent_config)]

                # 根据概率采样类别
                data[s, i] = np.random.choice(np.arange(1, n_categories + 1), p=probs)

    return data


def generate_dataset_split(n_nodes, er_degree, data_type, train_samples=1000, obs_test_samples=200,
                           int_test_samples=300, alpha=0.1, n_categories=3, intervention_type='hard'):
    """
    生成完整的数据集，并分为训练集、观测测试集和干预测试集

    参数:
    n_nodes: 节点数量
    er_degree: Erdős–Rényi图的期望度数
    data_type: 数据类型 ('linear', 'anm', 'nn', 'mlp', 'dirichlet')
    train_samples: 训练集样本数量
    obs_test_samples: 观测测试集样本数量
    int_test_samples: 干预测试集样本数量
    alpha: Dirichlet分布的参数
    n_categories: 离散数据的类别数量
    intervention_type: 干预类型 ('hard' 或 'soft')

    返回:
    包含训练集、观测测试集和干预测试集的字典
    """
    # 生成随机DAG
    adjacency = generate_random_dag(n_nodes, er_degree)

    # 根据数据类型选择生成器
    if data_type == 'linear':
        data_generator = generate_linear_data
    elif data_type == 'anm':
        data_generator = generate_anm_data
    elif data_type == 'nn':
        data_generator = generate_nn_data
    elif data_type == 'mlp':
        data_generator = lambda adj, n, node, type: generate_mlp_data(adj, n, node, type, n_categories)
    elif data_type == 'dirichlet':
        data_generator = lambda adj, n, node, type: generate_dirichlet_data(adj, n, node, type, n_categories, alpha)
    else:
        raise ValueError(f"Unknown data type: {data_type}")

    # 生成训练集（混合观测和干预数据）
    # 80%观测数据，20%干预数据
    obs_train_samples = int(train_samples * 0.8)
    int_train_samples = train_samples - obs_train_samples

    # 生成观测训练数据
    train_obs_data, train_obs_targets = generate_data(adjacency, data_generator, obs_train_samples)

    # 生成干预训练数据（随机选择节点进行干预）
    intervention_nodes = np.random.choice(n_nodes, size=min(n_nodes, int(n_nodes * 0.5)), replace=False)
    train_int_data, train_int_targets = generate_data(adjacency, data_generator, int_train_samples,
                                                      intervention_nodes, intervention_type)

    # 合并观测和干预训练数据
    train_data = np.vstack([train_obs_data, train_int_data])
    train_targets = np.concatenate([train_obs_targets, train_int_targets])

    # 打乱训练数据顺序
    train_indices = np.random.permutation(len(train_data))
    train_data = train_data[train_indices]
    train_targets = train_targets[train_indices]

    # 生成观测测试数据
    test_obs_data, test_obs_targets = generate_data(adjacency, data_generator, obs_test_samples)

    # 生成干预测试数据（对所有节点进行干预）
    intervention_nodes = list(range(n_nodes))  # 对所有节点进行干预
    test_int_data, test_int_targets = generate_data(adjacency, data_generator, int_test_samples,
                                                    intervention_nodes, intervention_type)

    return {
        'adjacency': adjacency,
        'train': {
            'data': train_data,
            'intervention_targets': train_targets
        },
        'test_obs': {
            'data': test_obs_data,
            'intervention_targets': test_obs_targets
        },
        'test_int': {
            'data': test_int_data,
            'intervention_targets': test_int_targets
        }
    }


def generate_multiple_datasets(n_datasets, n_nodes, er_degree, data_type, train_samples=1000,
                               obs_test_samples=200, int_test_samples=300, alpha=0.1,
                               n_categories=3, intervention_type='hard'):
    """
    生成多个数据集，每个数据集包含训练集、观测测试集和干预测试集
    """
    datasets = []
    for i in range(n_datasets):
        dataset = generate_dataset_split(
            n_nodes, er_degree, data_type,
            train_samples, obs_test_samples, int_test_samples,
            alpha, n_categories, intervention_type
        )
        datasets.append(dataset)
    return datasets


def main():
    # 设置可调参数
    n_nodes = 10
    er_degree = 1
    data_type = 'dirichlet'
    alpha = 0.25
    n_categories = 3
    train_samples = 1000
    obs_test_samples = 200
    int_test_samples = 300
    intervention_type = 'hard'

    # 生成训练数据集
    print("生成训练数据集...")
    train_datasets = generate_multiple_datasets(
        n_datasets=5,  # 为了演示，只生成5个数据集
        n_nodes=n_nodes,
        er_degree=er_degree,
        data_type=data_type,
        train_samples=train_samples,
        obs_test_samples=obs_test_samples,
        int_test_samples=int_test_samples,
        alpha=alpha,
        n_categories=n_categories,
        intervention_type=intervention_type
    )

    print(f"生成了 {len(train_datasets)} 个数据集")

    # 示例：查看第一个数据集的信息
    print("\n第一个数据集信息:")
    print(f"邻接矩阵形状: {train_datasets[0]['adjacency'].shape}")
    print(f"训练集数据形状: {train_datasets[0]['train']['data'].shape}")
    print(f"训练集干预目标形状: {train_datasets[0]['train']['intervention_targets'].shape}")
    print(f"观测测试集数据形状: {train_datasets[0]['test_obs']['data'].shape}")
    print(f"观测测试集干预目标形状: {train_datasets[0]['test_obs']['intervention_targets'].shape}")
    print(f"干预测试集数据形状: {train_datasets[0]['test_int']['data'].shape}")
    print(f"干预测试集干预目标形状: {train_datasets[0]['test_int']['intervention_targets'].shape}")

    # 统计边的数量
    edges = np.sum(train_datasets[0]['adjacency'])
    print(f"图中的边数: {edges}")

    # 统计训练集中的干预类型
    train_interventions = train_datasets[0]['train']['intervention_targets']
    obs_count = np.sum(train_interventions == -1)
    int_count = len(train_interventions) - obs_count
    print(f"训练集中观测样本数: {obs_count}")
    print(f"训练集中干预样本数: {int_count}")

    # 统计干预测试集中的干预节点分布
    int_test_interventions = train_datasets[0]['test_int']['intervention_targets']
    unique_interventions, counts = np.unique(int_test_interventions, return_counts=True)
    print("\n干预测试集中干预节点分布:")
    for node, count in zip(unique_interventions, counts):
        print(f"节点 {node}: {count} 个样本")


if __name__ == "__main__":
    main()
