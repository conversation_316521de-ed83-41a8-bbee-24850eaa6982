import numpy as np
import torch
import networkx as nx
from typing import Dict, List, Optional, Any, Tuple
import hashlib
import json
from function_generator import FunctionGenerator

class NoiseVarianceCalculator:
    """
    基于信噪比(SNR)的噪声方差计算器
    
    该类实现了基于图结构深度和复杂度的噪声方差计算方法，
    通过控制信噪比来确保生成的因果数据既具有足够的复杂性，又能被机器学习模型有效学习。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化噪声方差计算器
        
        参数:
            config: 配置字典，包含SNR计算的各种参数
        """
        # 默认配置
        self.default_config = {
            'base_snr': 8.0,                    # 根节点基准SNR
            'depth_decay': 0.85,                # 深度衰减因子
            'min_snr': [11.0, 9.0, 7.0, 5.0, 3.0],  # 各深度最小SNR限制
            'max_snr': [12.0, 10.0, 8.0, 6.0, 4.0], # 各深度最大SNR限制
            'complexity_weight': 0.3,           # 复杂度权重
            'use_snr_method': False             # 是否使用SNR方法（默认关闭，保持向后兼容）
        }
        
        # 合并用户配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
            
        # 验证配置
        self._validate_config()
    
    def _validate_config(self):
        """验证配置参数的合理性"""
        if self.config['base_snr'] <= 0:
            raise ValueError("base_snr必须大于0")
        
        if not 0 < self.config['depth_decay'] <= 1:
            raise ValueError("depth_decay必须在(0, 1]范围内")
            
        if self.config['complexity_weight'] < 0:
            raise ValueError("complexity_weight必须非负")
            
        if len(self.config['min_snr']) != len(self.config['max_snr']):
            raise ValueError("min_snr和max_snr列表长度必须相同")
            
        for i, (min_val, max_val) in enumerate(zip(self.config['min_snr'], self.config['max_snr'])):
            if min_val >= max_val:
                raise ValueError(f"深度{i}的min_snr({min_val})必须小于max_snr({max_val})")
    
    def get_node_depth(self, dag: nx.DiGraph, node: Any) -> int:
        """
        计算节点在DAG中的深度（从根节点开始的最短路径长度）
        
        参数:
            dag: NetworkX有向无环图
            node: 目标节点
            
        返回:
            节点深度
        """
        # 找到所有根节点（入度为0的节点）
        root_nodes = [n for n in dag.nodes() if dag.in_degree(n) == 0]
        
        if not root_nodes:
            return 0
            
        # 如果节点本身是根节点
        if node in root_nodes:
            return 0
            
        # 计算从所有根节点到目标节点的最短路径
        min_depth = float('inf')
        for root in root_nodes:
            try:
                depth = nx.shortest_path_length(dag, root, node)
                min_depth = min(min_depth, depth)
            except nx.NetworkXNoPath:
                continue
                
        return min_depth if min_depth != float('inf') else 0
    
    def calculate_theoretical_snr(self, dag: nx.DiGraph) -> Dict[int, float]:
        """
        计算各深度的理论SNR值
        
        参数:
            dag: NetworkX有向无环图
            
        返回:
            深度到SNR的映射字典
        """
        # 计算图的最大深度
        max_depth = 0
        for node in dag.nodes():
            depth = self.get_node_depth(dag, node)
            max_depth = max(max_depth, depth)
        
        theoretical_snr = {}
        
        for depth in range(max_depth + 1):
            # 基于深度衰减计算SNR
            snr = self.config['base_snr'] * (self.config['depth_decay'] * depth + 1)
            
            # 应用深度相关的SNR限制
            if depth < len(self.config['min_snr']):
                min_snr = self.config['min_snr'][depth]
                max_snr = self.config['max_snr'][depth]
            else:
                # 对于超出预定义范围的深度，使用最后一个值
                min_snr = self.config['min_snr'][-1]
                max_snr = self.config['max_snr'][-1]
            
            snr = np.clip(snr, min_snr, max_snr)
            theoretical_snr[depth] = snr
            
        return theoretical_snr
    
    
    def calculate_theoretical_target_snr(self, dag: nx.DiGraph) -> Dict[Any, float]:
        """
        计算各节点的理论目标SNR（不涉及实际数据生成）

        参数:
            dag: NetworkX有向无环图

        返回:
            节点到目标SNR的映射字典
        """
        # 第一阶段：计算各深度的理论SNR
        theoretical_snr_by_depth = self.calculate_theoretical_snr(dag)

        # 第二阶段：为每个节点计算目标SNR（应用复杂度调整）
        target_snr_by_node = {}

        for node in dag.nodes():
            depth = self.get_node_depth(dag, node)
            target_snr = theoretical_snr_by_depth.get(depth, self.config['base_snr'])

            # 复杂度调整（仅对非根节点）
            if dag.in_degree(node) > 0:  # 非根节点
                in_degree = dag.in_degree(node)
                complexity_factor = 1 + self.config['complexity_weight'] * np.log(1 + in_degree)
                target_snr /= complexity_factor

            target_snr_by_node[node] = target_snr

        return target_snr_by_node

    def calculate_noise_variance_for_node(self, node: Any, signal_var: float, target_snr: float) -> float:
        """
        为单个节点计算噪声方差

        参数:
            node: 节点
            signal_var: 实际信号方差
            target_snr: 目标SNR

        返回:
            噪声方差
        """

        # 计算噪声方差: noise_var = signal_var / SNR
        noise_variance = signal_var / target_snr

        return noise_variance


class SNRValidator:
    """
    SNR验证器，用于验证实际SNR是否符合设计目标
    """

    def __init__(self):
        self.validation_results = {}

    def calculate_actual_snr(self, data: torch.Tensor, noise_data: torch.Tensor) -> float:
        """
        计算实际的信噪比

        参数:
            data: 包含信号和噪声的总数据
            noise_data: 纯噪声数据

        返回:
            实际SNR值
        """
        # 计算信号功率（总数据方差 - 噪声方差）
        total_var = torch.var(data).item()
        noise_var = torch.var(noise_data).item()
        signal_var = max(total_var - noise_var, 1e-8)  # 避免负值

        # 计算SNR
        snr = signal_var / noise_var if noise_var > 0 else float('inf')
        return snr

    def validate_snr_batch(self, generated_data: Dict[Any, torch.Tensor],
                          noise_data: Dict[Any, torch.Tensor],
                          target_snr: Dict[Any, float]) -> Dict[str, Any]:
        """
        批量验证多个节点的SNR

        参数:
            generated_data: 生成的数据字典 {node: data}
            noise_data: 噪声数据字典 {node: noise}
            target_snr: 目标SNR字典 {node: target_snr}

        返回:
            验证结果字典
        """
        results = {
            'actual_snr': {},
            'target_snr': {},
            'snr_error': {},
            'relative_error': {},
            'summary': {}
        }

        errors = []
        relative_errors = []

        for node in generated_data.keys():
            if node in noise_data and node in target_snr:
                actual_snr = self.calculate_actual_snr(generated_data[node], noise_data[node])
                target = target_snr[node]

                error = abs(actual_snr - target)
                rel_error = error / target if target > 0 else float('inf')

                results['actual_snr'][node] = actual_snr
                results['target_snr'][node] = target
                results['snr_error'][node] = error
                results['relative_error'][node] = rel_error

                errors.append(error)
                relative_errors.append(rel_error)

        # 计算统计摘要
        if errors:
            results['summary'] = {
                'mean_absolute_error': np.mean(errors),
                'max_absolute_error': np.max(errors),
                'mean_relative_error': np.mean(relative_errors),
                'max_relative_error': np.max(relative_errors),
                'num_nodes': len(errors)
            }

        return results

    def generate_snr_report(self, validation_results: Dict[str, Any]) -> str:
        """
        生成SNR验证报告

        参数:
            validation_results: 验证结果字典

        返回:
            格式化的报告字符串
        """
        report = []
        report.append("=" * 60)
        report.append("SNR验证报告")
        report.append("=" * 60)

        if 'summary' in validation_results and validation_results['summary']:
            summary = validation_results['summary']
            report.append(f"验证节点数量: {summary['num_nodes']}")
            report.append(f"平均绝对误差: {summary['mean_absolute_error']:.4f}")
            report.append(f"最大绝对误差: {summary['max_absolute_error']:.4f}")
            report.append(f"平均相对误差: {summary['mean_relative_error']:.4f}")
            report.append(f"最大相对误差: {summary['max_relative_error']:.4f}")
            report.append("")

        # 详细节点信息
        report.append("各节点SNR详情:")
        report.append("-" * 60)
        report.append(f"{'节点':<10} {'目标SNR':<10} {'实际SNR':<10} {'绝对误差':<10} {'相对误差':<10}")
        report.append("-" * 60)

        for node in validation_results.get('actual_snr', {}):
            target = validation_results['target_snr'][node]
            actual = validation_results['actual_snr'][node]
            abs_error = validation_results['snr_error'][node]
            rel_error = validation_results['relative_error'][node]

            report.append(f"{str(node):<10} {target:<10.4f} {actual:<10.4f} {abs_error:<10.4f} {rel_error:<10.4f}")

        return "\n".join(report)




class MonteCarloCache:
    """Manages caching of Monte Carlo precomputation results"""

    def __init__(self):
        self.cache = {}  # fingerprint -> precomputed_results

    def get_cached_result(self, fingerprint):
        """Get cached result if available"""
        return self.cache.get(fingerprint, None)

    def store_result(self, fingerprint, result):
        """Store precomputation result in cache"""
        self.cache[fingerprint] = result

    def clear_cache(self):
        """Clear all cached results"""
        self.cache.clear()

    def get_cache_size(self):
        """Get number of cached configurations"""
        return len(self.cache)

    def print_cache_statistics(self):
        """Print Monte Carlo cache usage statistics"""
        cache_size = len(self.cache)
        print(f"Monte Carlo缓存统计: {cache_size} 个配置已缓存")

        for fingerprint, result in self.cache.items():
            print(f"  - {fingerprint[:8]}...: {len(result)} 个节点")


# Global cache instance
monte_carlo_cache = MonteCarloCache()


def create_configuration_fingerprint(dag, root_config, custom_functions_config):
    """
    Create a unique fingerprint for the current configuration

    Args:
        dag: NetworkX DAG structure
        root_config: Root node distribution configuration
        custom_functions_config: Custom functions configuration

    Returns:
        str: Unique hash representing the configuration
    """
    fingerprint_data = {
        # (1) Root node distribution
        'root_distribution': root_config.get('root_distribution', 'uniform'),
        'root_mean': root_config.get('root_mean', 0.0),
        'root_std': root_config.get('root_std', 1.0),
        'min_root': root_config.get('min_root', 0.0),
        'max_root': root_config.get('max_root', 1.0),
        'sample_cause_ranges': root_config.get('sample_cause_ranges', False),

        # (2) DAG structure
        'nodes': sorted(list(dag.nodes())),
        'edges': sorted(list(dag.edges())),
        'dag_hash': nx.weisfeiler_lehman_graph_hash(dag),

        # (3) Function types and parameters
        'custom_functions': custom_functions_config if custom_functions_config else {}
    }

    # Create hash from the fingerprint data
    fingerprint_str = json.dumps(fingerprint_data, sort_keys=True, default=str)
    return hashlib.md5(fingerprint_str.encode()).hexdigest()


def generate_root_node_samples(root_config, monte_carlo_samples, device='cpu'):
    """
    Generate root node samples based on distribution configuration

    Args:
        root_config: Root node distribution configuration
        monte_carlo_samples: Number of samples to generate
        device: Device for tensor operations

    Returns:
        torch.Tensor: Root node samples [monte_carlo_samples, num_root_nodes]
    """
    root_distribution = root_config.get('root_distribution', 'uniform')

    if root_distribution == 'gaussian':
        root_mean = root_config.get('root_mean', 0.0)
        root_std = root_config.get('root_std', 1.0)

        # For simplicity, assume single root node for now
        # This can be extended to handle multiple root nodes
        root_samples = torch.normal(
            mean=root_mean,
            std=root_std,
            size=(monte_carlo_samples, 1),
            device=device
        )
    else:  # uniform distribution
        min_root = root_config.get('min_root', 0.0)
        max_root = root_config.get('max_root', 1.0)

        root_samples = (max_root - min_root) * torch.rand(
            (monte_carlo_samples, 1),
            device=device
        ) + min_root

    return root_samples.float()


def generate_signal_samples(node, parent_data, custom_functions_config, device='cpu'):
    """
    Generate signal samples for a node using its assignment function

    Args:
        node: Current node
        parent_data: Parent node data [monte_carlo_samples, num_parents]
        custom_functions_config: Custom functions configuration
        device: Device for tensor operations

    Returns:
        torch.Tensor: Signal samples [monte_carlo_samples, 1]
    """
    # Get custom function configuration for this node
    node_config = custom_functions_config.get(node, {}) if custom_functions_config else {}

    signal_samples = apply_custom_function(parent_data, node_config, device)

    return signal_samples


def apply_custom_function(parent_data, node_config, device='cpu'):
    """
    应用自定义函数到父节点数据
    使用与实际数据生成相同的函数实现
    
    参数:
        parent_data: 父节点数据，numpy数组
        node_config: 节点的自定义函数配置
        device: 计算设备
        
    返回:
        应用函数后的结果，numpy数组
    """
    # 获取输入输出维度
    if len(parent_data.shape) == 1:
        parent_data = parent_data.reshape(-1, 1)
    
    in_dim = parent_data.shape[1]
    out_dim = 1  # 通常输出维度为1
    
    # 使用FunctionGenerator创建函数
    # 注意：在蒙特卡洛计算中，init_std不重要，因为系数通常已经在配置中指定
    f_function, _ = FunctionGenerator.create_f_function(
        node_config,
        in_dim,
        out_dim,
        device,
        init_std=0.1,  # 默认值，通常不会用到
        seed=42)
    
    if f_function is None:
        raise ValueError(f"无法创建自定义函数，配置: {node_config}")
    
    # 将数据转换为torch tensor
    if not isinstance(parent_data, torch.Tensor):
        parent_data_tensor = torch.tensor(parent_data, dtype=torch.float32, device=device)
    else:
        parent_data_tensor = parent_data.to(device)
    
    # 应用函数
    with torch.no_grad():
        result = f_function(parent_data_tensor)
    
    # 转换回numpy
    if isinstance(result, torch.Tensor):
        result = result.cpu().numpy()
    
    # 确保输出形状正确
    if result.shape[1] == 1:
        result = result.squeeze(1)
    
    return result


def calculate_noise_std_from_config(node, signal_var, custom_functions_config, h_config):
    """
    Calculate noise standard deviation based on configuration

    Args:
        node: Current node
        signal_var: Signal variance
        custom_functions_config: Custom functions configuration
        h_config: General hyperparameters

    Returns:
        float: Noise standard deviation
    """
    # Get node-specific configuration
    node_config = custom_functions_config.get(node, {}) if custom_functions_config else {}

    # Check if node has target SNR
    if 'target_snr' in node_config:
        target_snr = node_config['target_snr']
        noise_variance = signal_var / target_snr
        return np.sqrt(noise_variance)

    # Check if node has fixed noise_std
    if 'noise_std' in node_config:
        return node_config['noise_std']

    # Use default noise_std from h_config
    return h_config.get('noise_std', 0.1)


def generate_noise_samples(noise_std, monte_carlo_samples, h_config, device='cpu'):
    """
    Generate noise samples based on configuration

    Args:
        noise_std: Noise standard deviation
        monte_carlo_samples: Number of samples
        h_config: General hyperparameters
        device: Device for tensor operations

    Returns:
        torch.Tensor: Noise samples [monte_carlo_samples, 1]
    """
    noise_mean_mode = h_config.get('monte_carlo_noise_mean_mode', 'fixed')

    if noise_mean_mode == 'fixed':
        noise_mean = 0.0
    else:  # signal_mean mode - for simplicity, use 0 in Monte Carlo precomputation
        noise_mean = 0.0

    # Generate Gaussian noise
    noise_samples = torch.normal(
        mean=noise_mean,
        std=noise_std,
        size=(monte_carlo_samples, 1),
        device=device
    )

    return noise_samples


def resample_parent_data(node_samples, parents, monte_carlo_samples, resampling_indices=None):
    """
    Resample parent node data for current node

    Args:
        node_samples: Dictionary of node samples
        parents: List of parent nodes
        monte_carlo_samples: Number of samples needed
        resampling_indices: Optional dict of pre-generated resampling indices {parent: indices}

    Returns:
        torch.Tensor: Resampled parent data [monte_carlo_samples, num_parents]
    """
    parent_data_list = []

    for parent in parents:
        parent_samples = node_samples[parent]

        if resampling_indices is not None and parent in resampling_indices:
            # Use pre-generated indices for consistent resampling across child nodes
            indices = resampling_indices[parent]
        else:
            # Generate new indices (fallback to original behavior)
            indices = torch.randint(0, parent_samples.shape[0], (monte_carlo_samples,))

        resampled_data = parent_samples[indices]
        parent_data_list.append(resampled_data)

    return torch.cat(parent_data_list, dim=1)


def get_or_compute_monte_carlo_noise_params(dag, root_config, custom_functions_config, h_config):
    """
    Get cached Monte Carlo results or compute new ones if configuration changed

    Args:
        dag: Current DAG structure
        root_config: Root node configuration
        custom_functions_config: Custom functions configuration
        h_config: General hyperparameters

    Returns:
        dict: Precomputed noise parameters for all nodes
    """
    # Create configuration fingerprint
    fingerprint = create_configuration_fingerprint(dag, root_config, custom_functions_config)

    # Check if we have cached results for this configuration
    cached_result = monte_carlo_cache.get_cached_result(fingerprint)

    if cached_result is not None:
        print(f"使用缓存的Monte Carlo结果 (fingerprint: {fingerprint[:8]}...)")
        return cached_result

    # Configuration changed, need to recalculate
    print(f"配置已更改，重新计算Monte Carlo结果 (fingerprint: {fingerprint[:8]}...)")

    # Perform Monte Carlo precomputation
    result = perform_monte_carlo_precomputation(dag, root_config, custom_functions_config, h_config)

    # Cache the result
    monte_carlo_cache.store_result(fingerprint, result)

    return result


def perform_monte_carlo_precomputation(dag, root_config, custom_functions_config, h_config):
    """
    Perform the actual Monte Carlo precomputation following user's detailed plan

    Args:
        dag: DAG structure
        root_config: Root node configuration
        custom_functions_config: Custom functions configuration
        h_config: General hyperparameters

    Returns:
        dict: Precomputed noise parameters for all nodes
    """
    monte_carlo_samples = h_config.get('monte_carlo_samples', 10000)
    monte_carlo_seed = h_config.get('monte_carlo_seed', 42)
    device = h_config.get('device', 'cpu')

    # Set fixed seed for reproducibility
    np.random.seed(monte_carlo_seed)
    torch.manual_seed(monte_carlo_seed)

    print(f"开始Monte Carlo预计算，样本数: {monte_carlo_samples}, 种子: {monte_carlo_seed}")

    # Step 1: Generate root node samples
    root_nodes = [node for node in dag.nodes() if dag.in_degree(node) == 0]
    print(f"根节点: {root_nodes}")

    # For multiple root nodes, generate samples for each
    root_samples_dict = {}
    for root_node in root_nodes:
        root_samples = generate_root_node_samples(root_config, monte_carlo_samples, device)
        root_samples_dict[root_node] = root_samples

    # Step 2: Process nodes in topological order
    topo_order = list(nx.topological_sort(dag))
    precomputed_params = {}
    node_samples = {}

    # Initialize root node samples
    for root_node in root_nodes:
        node_samples[root_node] = root_samples_dict[root_node]

    # Step 2.1: Pre-generate consistent resampling indices for all parent nodes (if resampling is enabled)
    resampling_indices = {}
    if h_config.get('monte_carlo_resample_parents', False):
        # For each node that has children, generate resampling indices
        # These indices will be used consistently by all children of that node
        for node in dag.nodes():
            if dag.out_degree(node) > 0:  # Nodes that have children
                # Generate resampling indices for this parent node
                indices = torch.randint(0, monte_carlo_samples, (monte_carlo_samples,))
                resampling_indices[node] = indices

    # Process non-root nodes
    for node in topo_order:
        if dag.in_degree(node) == 0:  # Skip root nodes
            continue

        print(f"处理节点: {node}")

        # Get parent nodes
        parents = list(dag.predecessors(node))

        # Resample parent data if configured
        if h_config.get('monte_carlo_resample_parents', False):
            parent_data = resample_parent_data(node_samples, parents, monte_carlo_samples, resampling_indices)
        else:
            parent_data = torch.cat([node_samples[p] for p in parents], dim=1)

        # Generate signal samples (noise_std = 0)
        signal_samples = generate_signal_samples(node, parent_data, custom_functions_config, device)

        # Ensure signal_samples is a PyTorch tensor
        if not isinstance(signal_samples, torch.Tensor):
            signal_samples = torch.tensor(signal_samples, dtype=torch.float32, device=device)
        else:
            signal_samples = signal_samples.to(device)

        # Calculate signal variance and mean
        signal_var = torch.var(signal_samples).item()
        signal_mean = torch.mean(signal_samples).item()

        print(f"  节点 {node} 信号方差: {signal_var:.6f}, 信号均值: {signal_mean:.6f}")

        # Calculate noise standard deviation
        noise_std = calculate_noise_std_from_config(node, signal_var, custom_functions_config, h_config)

        print(f"  节点 {node} 噪声标准差: {noise_std:.6f}")

        # Store precomputed parameters
        precomputed_params[node] = {
            'noise_std': noise_std,
            'signal_var': signal_var,
            'signal_mean': signal_mean
        }

        # Generate full node data (signal + noise) for child nodes
        noise_samples = generate_noise_samples(noise_std, monte_carlo_samples, h_config, device)
        node_samples[node] = signal_samples + noise_samples

    print(f"Monte Carlo预计算完成，处理了 {len(precomputed_params)} 个非根节点")
    return precomputed_params

