parents: ['U'] -> child: X
  f函数:
Linear
w=[[0.5364794135093689]]
b=[2.1350362300872803]
  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.2695464530748255
  === SNR验证结果 ===
  实际信号方差: 0.290621
  实际信号标准差: 0.539093
  实际噪声方差: 0.067385
  实际噪声标准差: 0.259587
  实际SNR: 4.312823898431599
  目标SNR: 4.0

parents: ['U'] -> child: Y
  f函数:
Linear
w=[[-5.328620433807373]]
b=[-0.8128214478492737]
  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 2.6772895961123475
  === SNR验证结果 ===
  实际信号方差: 28.671518
  实际信号标准差: 5.354579
  实际噪声方差: 6.691730
  实际噪声标准差: 2.586838
  实际SNR: 4.284620011979842
  目标SNR: 4.0