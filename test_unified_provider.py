"""
测试统一数据提供器的简化使用方式
"""

import torch
from scm_data_provider import DataProvider, DataGenerationConfig, SNRConfig, NodeFunctionConfigs, FunctionConfig

def test_unified_provider():
    """测试统一数据提供器"""
    print("测试统一数据提供器")
    print("=" * 50)

    # 基础配置
    h_config = {
        'device': 'cpu',
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'sample_root_std': False,
        'min_root': 0.0,
        'max_root': 1.0,
        'max_range': 0.5,
        'sample_cause_ranges': False,
        'sample_std': False,
        'min_num_samples': 1000,
        'max_num_samples': 1000,
        'train_test_split_ratio': 0.7,
        'task': 'regression',
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        'min_num_node': 5,
        'max_num_node': 10,
        'num_layers': 3,
        'max_num_children': 10,
        'max_num_classes': 5,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,
        'use_monte_carlo_precompute': False
    }

    # 简化的配置
    snr_config = SNRConfig(
        parent_snr_values=[2.0],
        child_snr_multipliers=[1.0],
        other_snr_multipliers=[1.0]
    )

    node_function_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(function_type='linear'),
        target_child_config=FunctionConfig(function_type='linear'),
        other_config=FunctionConfig(function_type='linear')
    )

    data_config = DataGenerationConfig(
        n_datasets=2,
        dataset_type='perturbation',
        seed=42,
        custom_dag_size='small',
        snr_config=snr_config,
        node_function_configs=node_function_configs
    )

    # 创建数据提供器并生成数据集
    print("1. 生成数据集...")
    provider = DataProvider(h_config, data_config)
    provider.generate_datasets()

    # 使用统一方法获取所有信息
    print("\n2. 获取统一数据集信息...")
    unified_info = provider.get_unified_dataset_info()
    
    print(f"获取了 {len(unified_info)} 个配置的数据集信息")
    
    # 展示统一信息的结构
    for config_key, config_datasets in unified_info.items():
        print(f"\n配置 {config_key}:")
        for i, dataset_info in enumerate(config_datasets):
            print(f"  数据集 {i}:")
            print(f"    名称: {dataset_info['dataset_name']}")
            print(f"    类型: {dataset_info['dataset_type']}")
            print(f"    特征数: {len(dataset_info['feature_names'])}")
            
            # 数据分割信息
            data_splits = dataset_info['data_splits']
            print(f"    训练数据: {data_splits['train_xs'].shape}")
            if dataset_info['dataset_type'] == 'standard':
                print(f"    测试数据: {data_splits['test_xs'].shape}")
            else:
                print(f"    原始测试数据: {data_splits['test_xs_original'].shape}")
                print(f"    修改测试数据: {data_splits['test_xs_modified'].shape}")
            
            # 节点关系信息
            node_rel = dataset_info['node_relationships']
            print(f"    目标节点: {node_rel['target_node']}")
            print(f"    马尔可夫毯大小: {len(node_rel['markov_blanket'])}")
            print(f"    父节点数: {len(node_rel['relationships']['parents'])}")
            print(f"    子节点数: {len(node_rel['relationships']['children'])}")
            print(f"    其他节点数: {len(node_rel['relationships']['others'])}")

    # 保存数据集
    print("\n3. 保存数据集到CSV...")
    all_saved_paths = provider.save_dataset_to_csv(unified_info)
    print(f"保存了 {len(all_saved_paths)} 个配置的数据集")

    # 绘制DAG图
    print("\n4. 绘制DAG图...")
    all_dag_paths = provider.draw_dag()
    print(f"绘制了 {len(all_dag_paths)} 个配置的DAG图")

    print("\n测试完成！")
    print("=" * 50)

    return unified_info

if __name__ == "__main__":
    unified_info = test_unified_provider()
