parents: ['U'] -> child: X
  f函数:
Linear
w=[[10.0]]
b=[0.0]
  g函数: identity
  init_std: 1.7033334970474243
  noise_std: 3.177682875466218
  === SNR验证结果 ===
  实际信号方差: 100.976685
  实际信号标准差: 10.048716
  实际噪声方差: 9.365251
  实际噪声标准差: 3.060270
  实际SNR: 10.782059019913795
  目标SNR: 10.0

parents: ['U'] -> child: Y
  f函数:
Linear
w=[[1.0]]
b=[0.0]
  g函数: identity
  init_std: 0.09044691920280457
  noise_std: 0.10048715425828794
  === SNR验证结果 ===
  实际信号方差: 1.009767
  实际信号标准差: 1.004872
  实际噪声方差: 0.009427
  实际噪声标准差: 0.097092
  实际SNR: 107.11547772507878
  目标SNR: 100.0