parents: ['X9'] -> child: X3
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.001358345031207989

parents: ['X0', 'X2', 'X4', 'X5', 'X8'] -> child: X6
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.07928560181523689

parents: ['X7'] -> child: X8
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.025579240246387597

parents: ['X0'] -> child: X9
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.17389120607521522

parents: ['X7'] -> child: X10
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.1307530611803116

parents: ['X0', 'X1'] -> child: X11
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.19870756050061428