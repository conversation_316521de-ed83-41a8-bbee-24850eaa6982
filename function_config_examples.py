"""
函数配置示例 - 展示所有支持的函数类型和参数配置

基于function_generator.py中支持的函数类型，展示如何配置不同的函数
"""

from scm_test import FunctionConfig, NodeFunctionConfigs, TestConfig, SNRConfig

def example_linear_functions():
    """线性函数配置示例"""
    print("=" * 60)
    print("线性函数配置示例")
    print("=" * 60)
    
    # 不同的线性函数配置
    node_configs = NodeFunctionConfigs(
        # 指定系数的线性函数
        target_config=FunctionConfig(
            function_type='linear',
            coefficients=[1.5, -0.8, 2.0],  # y = 1.5*x1 - 0.8*x2 + 2.0*x3 + bias
            bias=0.5,
            target_snr=2.0
        ),
        # 随机系数的线性函数
        target_child_config=FunctionConfig(
            function_type='linear',
            # 不指定coefficients，将随机生成
            noise_std=0.1
        ),
        # 简单线性函数
        other_config=FunctionConfig(
            function_type='linear',
            coefficients=[1.0, 1.0],  # y = x1 + x2
            bias=0.0,
            target_snr=1.5
        )
    )
    
    print("线性函数配置:")
    print(f"  Target: 指定系数 {node_configs.target_config.coefficients}, bias={node_configs.target_config.bias}")
    print(f"  Target_child: 随机系数, noise_std={node_configs.target_child_config.noise_std}")
    print(f"  Other: 简单加法 {node_configs.other_config.coefficients}")
    
    return node_configs

def example_polynomial_functions():
    """多项式函数配置示例"""
    print("\n" + "=" * 60)
    print("多项式函数配置示例")
    print("=" * 60)
    
    node_configs = NodeFunctionConfigs(
        # 二次多项式
        target_config=FunctionConfig(
            function_type='polynomial',
            degree=2,
            scale=1.0,
            target_snr=2.5
        ),
        # 偶次多项式
        target_child_config=FunctionConfig(
            function_type='polynomial_even',
            degree=4,
            scale=0.5,
            noise_std=0.08
        ),
        # 高次多项式
        other_config=FunctionConfig(
            function_type='polynomial',
            degree=3,
            scale=1.2,
            target_snr=1.8
        )
    )
    
    print("多项式函数配置:")
    print(f"  Target: {node_configs.target_config.degree}次多项式, scale={node_configs.target_config.scale}")
    print(f"  Target_child: {node_configs.target_child_config.degree}次偶多项式, scale={node_configs.target_child_config.scale}")
    print(f"  Other: {node_configs.other_config.degree}次多项式, scale={node_configs.other_config.scale}")
    
    return node_configs

def example_trigonometric_functions():
    """三角函数配置示例"""
    print("\n" + "=" * 60)
    print("三角函数配置示例")
    print("=" * 60)
    
    node_configs = NodeFunctionConfigs(
        # 正弦函数
        target_config=FunctionConfig(
            function_type='sine',
            frequency=2.0,  # 频率
            scale=1.5,      # 幅度
            target_snr=2.0
        ),
        # 余弦函数
        target_child_config=FunctionConfig(
            function_type='cosine',
            frequency=1.5,
            scale=1.0,
            noise_std=0.12
        ),
        # 缩放的tanh函数
        other_config=FunctionConfig(
            function_type='tanh_scaled',
            scale=2.0,
            target_snr=1.8
        )
    )
    
    print("三角函数配置:")
    print(f"  Target: sine, freq={node_configs.target_config.frequency}, scale={node_configs.target_config.scale}")
    print(f"  Target_child: cosine, freq={node_configs.target_child_config.frequency}, scale={node_configs.target_child_config.scale}")
    print(f"  Other: tanh_scaled, scale={node_configs.other_config.scale}")
    
    return node_configs

def example_neural_network_functions():
    """神经网络函数配置示例"""
    print("\n" + "=" * 60)
    print("神经网络函数配置示例")
    print("=" * 60)
    
    node_configs = NodeFunctionConfigs(
        # 深层网络
        target_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=8,
            depth=4,
            activation='tanh',
            target_snr=2.5
        ),
        # 宽层网络
        target_child_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=16,
            depth=2,
            activation='relu',
            noise_std=0.1
        ),
        # 简单网络
        other_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=4,
            depth=2,
            activation='sigmoid',
            target_snr=1.5
        )
    )
    
    print("神经网络函数配置:")
    print(f"  Target: 深层网络 {node_configs.target_config.depth}层×{node_configs.target_config.hidden_dim}维, {node_configs.target_config.activation}")
    print(f"  Target_child: 宽层网络 {node_configs.target_child_config.depth}层×{node_configs.target_child_config.hidden_dim}维, {node_configs.target_child_config.activation}")
    print(f"  Other: 简单网络 {node_configs.other_config.depth}层×{node_configs.other_config.hidden_dim}维, {node_configs.other_config.activation}")
    
    return node_configs

def example_advanced_functions():
    """高级函数配置示例"""
    print("\n" + "=" * 60)
    print("高级函数配置示例")
    print("=" * 60)
    
    node_configs = NodeFunctionConfigs(
        # 高斯径向基函数
        target_config=FunctionConfig(
            function_type='gaussian_rbf',
            centers=[[0.0, 0.0], [1.0, 1.0], [-1.0, 1.0]],  # RBF中心点
            scale=1.0,
            target_snr=2.0
        ),
        # 傅里叶级数
        target_child_config=FunctionConfig(
            function_type='fourier_series',
            n_components=5,  # 傅里叶组件数量
            frequency=1.0,
            noise_std=0.1
        ),
        # 分段线性函数
        other_config=FunctionConfig(
            function_type='piecewise_linear',
            n_components=3,  # 分段数量
            target_snr=1.8
        )
    )
    
    print("高级函数配置:")
    print(f"  Target: 高斯RBF, {len(node_configs.target_config.centers)}个中心点")
    print(f"  Target_child: 傅里叶级数, {node_configs.target_child_config.n_components}个组件")
    print(f"  Other: 分段线性, {node_configs.other_config.n_components}段")
    
    return node_configs

def example_mixed_function_types():
    """混合函数类型配置示例"""
    print("\n" + "=" * 60)
    print("混合函数类型配置示例")
    print("=" * 60)
    
    node_configs = NodeFunctionConfigs(
        # 指数函数
        target_config=FunctionConfig(
            function_type='exponential',
            scale=0.5,
            target_snr=2.2
        ),
        # 对数函数
        target_child_config=FunctionConfig(
            function_type='logarithmic',
            scale=1.0,
            noise_std=0.15
        ),
        # ReLU二次函数
        other_config=FunctionConfig(
            function_type='relu_quadratic',
            scale=1.5,
            target_snr=1.6
        )
    )
    
    print("混合函数类型配置:")
    print(f"  Target: {node_configs.target_config.function_type}, scale={node_configs.target_config.scale}")
    print(f"  Target_child: {node_configs.target_child_config.function_type}, scale={node_configs.target_child_config.scale}")
    print(f"  Other: {node_configs.other_config.function_type}, scale={node_configs.other_config.scale}")
    
    return node_configs

def example_complete_test_config():
    """完整的测试配置示例"""
    print("\n" + "=" * 60)
    print("完整的测试配置示例")
    print("=" * 60)
    
    # 使用线性函数配置
    node_configs = example_linear_functions()
    
    # SNR配置
    snr_config = SNRConfig(
        parent_snr_values=[1.5, 2.0, 2.5],
        child_snr_multipliers=[0.8, 1.0],
        other_snr_multipliers=[1.0, 1.2]
    )
    
    # 完整测试配置
    test_config = TestConfig(
        n_datasets=3,
        snr_config=snr_config,
        node_function_configs=node_configs,
        device='cpu',
        num_workers=2
    )
    
    total_configs = (len(snr_config.parent_snr_values) * 
                    len(snr_config.child_snr_multipliers) * 
                    len(snr_config.other_snr_multipliers))
    
    print(f"完整配置将生成 {total_configs} 种组合")
    print(f"每种组合生成 {test_config.n_datasets} 个数据集")
    print(f"总计 {total_configs * test_config.n_datasets} 个数据集")
    
    return test_config

def main():
    """展示所有函数配置示例"""
    print("函数配置示例 - 支持的所有函数类型")
    print("=" * 80)
    
    # 展示各种函数配置
    example_linear_functions()
    example_polynomial_functions()
    example_trigonometric_functions()
    example_neural_network_functions()
    example_advanced_functions()
    example_mixed_function_types()
    example_complete_test_config()
    
    print("\n" + "=" * 80)
    print("支持的函数类型总结:")
    print("=" * 80)
    
    function_types = [
        ('linear', '线性函数', 'y = a1*x1 + a2*x2 + ... + b'),
        ('polynomial', '多项式函数', '支持任意次数的多项式'),
        ('polynomial_even', '偶次多项式', '只包含偶次项的多项式'),
        ('exponential', '指数函数', '指数增长/衰减函数'),
        ('logarithmic', '对数函数', '对数函数'),
        ('sigmoid_scaled', '缩放Sigmoid', '可调节幅度的Sigmoid函数'),
        ('tanh_scaled', '缩放Tanh', '可调节幅度的Tanh函数'),
        ('sine', '正弦函数', '可调频率和幅度的正弦函数'),
        ('cosine', '余弦函数', '可调频率和幅度的余弦函数'),
        ('relu_quadratic', 'ReLU二次', 'ReLU激活的二次函数'),
        ('gaussian_rbf', '高斯RBF', '高斯径向基函数'),
        ('gaussian_process', '高斯过程', '高斯过程函数（需要GPy）'),
        ('fourier_series', '傅里叶级数', '傅里叶级数展开'),
        ('piecewise_linear', '分段线性', '分段线性函数'),
        ('random_neural_network', '随机神经网络', '可配置结构的神经网络')
    ]
    
    for func_type, name, desc in function_types:
        print(f"  {func_type:20} - {name:12} - {desc}")
    
    print("\n配置参数说明:")
    print("• coefficients: 线性函数系数（linear类型）")
    print("• bias: 偏置项")
    print("• degree: 多项式次数（polynomial类型）")
    print("• scale: 缩放因子")
    print("• frequency: 频率参数（sine/cosine类型）")
    print("• n_components: 组件数量（fourier_series/piecewise_linear类型）")
    print("• centers: 中心点坐标（gaussian_rbf类型）")
    print("• hidden_dim: 神经网络隐藏层维度")
    print("• depth: 神经网络深度")
    print("• activation: 激活函数（tanh/relu/sigmoid）")
    
    print("\n噪声配置:")
    print("• target_snr: 目标信噪比（动态计算噪声）")
    print("• noise_std: 固定噪声标准差")
    print("• noise_mean: 噪声均值")
    print("• noise_mean_mode: 噪声均值模式（fixed/signal_mean）")

if __name__ == "__main__":
    main()
