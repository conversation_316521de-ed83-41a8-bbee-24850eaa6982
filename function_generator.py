"""
function_generator.py - 自定义函数生成器

该文件实现了SCM框架中的自定义函数生成系统，支持多种函数类型和配置：

主要功能：
1. 函数类型支持：线性函数、MLP、神经网络、高斯过程等
2. 参数配置：支持手动指定参数或随机初始化
3. SNR控制：基于目标SNR的噪声标准差计算
4. 噪声生成：多种噪声类型（高斯、拉普拉斯等）
5. 函数组合：支持f函数和g函数的组合使用

支持的函数类型：
- linear: 线性函数 y = ax + b
- mlp: 多层感知机
- random_neural_network: 随机神经网络
- gaussian_process: 高斯过程（需要GPy）
- fourier_transform: 傅里叶变换函数
- piecewise_linear: 分段线性函数
- polynomial: 多项式函数
- trigonometric: 三角函数

核心类：
- FunctionGenerator: 主要的函数生成器类
- LinearFunction: 线性函数实现
- MLPFunction: MLP函数实现
- 各种自定义函数类

"""

import torch
import torch.nn as nn
import numpy as np
import math
try:
    import GPy
    GPY_AVAILABLE = True
except ImportError:
    GPY_AVAILABLE = False


class FunctionGenerator:
    """独立的函数生成器，避免循环依赖"""
    
    @staticmethod
    def create_f_function(custom_function_config, in_dim, out_dim, device, init_std, seed=None):
        """
        创建f函数，供数据生成和蒙特卡洛计算共同使用
        
        参数:
            custom_function_config: 自定义函数配置
            in_dim: 输入维度
            out_dim: 输出维度
            device: 计算设备
            init_std: 初始化标准差
            seed: 随机种子
            
        返回:
            f_function: 生成的函数
            selected_f_name: 函数名称
        """
        if custom_function_config is None:
            return None, None
            
        function_type = custom_function_config.get('type', 'linear')
        
        # 调用对应的函数创建方法
        if function_type == 'linear':
            return FunctionGenerator._create_linear_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'polynomial':
            return FunctionGenerator._create_polynomial_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'polynomial_even':
            return FunctionGenerator._create_polynomial_even_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'exponential':
            return FunctionGenerator._create_exponential_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'logarithmic':
            return FunctionGenerator._create_logarithmic_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'sigmoid_scaled':
            return FunctionGenerator._create_sigmoid_scaled_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'tanh_scaled':
            return FunctionGenerator._create_tanh_scaled_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'sine':
            return FunctionGenerator._create_sine_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'cosine':
            return FunctionGenerator._create_cosine_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'relu_quadratic':
            return FunctionGenerator._create_relu_quadratic_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'gaussian_rbf':
            return FunctionGenerator._create_gaussian_rbf_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'gaussian_process':
            return FunctionGenerator._create_gaussian_process_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'fourier_series':
            return FunctionGenerator._create_fourier_series_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'piecewise_linear':
            return FunctionGenerator._create_piecewise_linear_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        elif function_type == 'random_neural_network':
            return FunctionGenerator._create_random_neural_network_function(
                custom_function_config, in_dim, out_dim, device, init_std, seed
            )
        else:
            raise ValueError(f"不支持的函数类型: {function_type}")
    
    @staticmethod
    def _create_linear_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建线性函数: y = a1*x1 + a2*x2 + ... + b"""
        coefficients = config.get('coefficients', None)
        bias = config.get('bias', 0.0)
        
        if coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 10)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 10)
            coefficients = torch.normal(0, init_std, (in_dim,), device=device)
            bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        else:
            # 使用指定的系数
            coefficients = torch.tensor(coefficients, device=device, dtype=torch.float32)
            bias = float(bias)
            
            # 检查系数数量是否与输入维度匹配
            if len(coefficients) != in_dim:
                raise ValueError(f"自定义函数系数数量({len(coefficients)})与输入维度({in_dim})不匹配")
        
        # 创建线性层并设置参数
        # 保存当前随机状态（因为nn.Linear创建时会使用随机初始化）
        torch_state_for_layer = torch.get_rng_state()
        cuda_state_for_layer = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

        f_function = nn.Linear(in_dim, out_dim, device=device)

        # 恢复随机状态（避免nn.Linear的随机初始化影响全局状态）
        torch.set_rng_state(torch_state_for_layer)
        if torch.cuda.is_available() and cuda_state_for_layer is not None:
            torch.cuda.set_rng_state(cuda_state_for_layer)

        with torch.no_grad():
            # 确保权重形状正确：(out_dim, in_dim)
            if len(coefficients.shape) == 1:
                f_function.weight.copy_(coefficients.unsqueeze(0))
            else:
                f_function.weight.copy_(coefficients)
            f_function.bias.fill_(bias)
            
        selected_f_name = f"linear_custom_{coefficients.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_polynomial_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建多项式函数 (可逆): y = a1*x1^p1 + a2*x2^p2 + ... + b"""
        coefficients = config.get('coefficients', None)
        powers = config.get('powers', [3] * in_dim)  # 默认3次幂
        bias = config.get('bias', 0.0)

        if coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 11)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 11)
            coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        else:
            coefficients = coefficients
        
        # 确保所有幂次都是奇数以保证可逆性
        powers = [p if p % 2 == 1 else p + 1 for p in powers]
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        class PolynomialFunction(nn.Module):
            def __init__(self, coefficients, powers, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.powers = powers
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, power) in enumerate(zip(self.coefficients, self.powers)):
                    if i < x.shape[1]:
                        result += coef * torch.pow(x[:, i:i+1], power)
                return result + self.bias
                
        f_function = PolynomialFunction(coefficients, powers, bias)
        selected_f_name = f"polynomial_invertible_coef_{coefficients.tolist()}_powers_{powers}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_polynomial_even_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建偶次多项式函数 (不可逆): y = a1*x1^p1 + a2*x2^p2 + ... + b"""
        coefficients = config.get('coefficients', None)
        powers = config.get('powers', [2] * in_dim)  # 默认2次幂
        bias = config.get('bias', 0.0)

        if coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 12)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 12)
            coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        else:
            coefficients = coefficients
        
        # 确保所有幂次都是偶数
        powers = [p if p % 2 == 0 else p + 1 for p in powers]
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        class PolynomialEvenFunction(nn.Module):
            def __init__(self, coefficients, powers, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.powers = powers
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, power) in enumerate(zip(self.coefficients, self.powers)):
                    if i < x.shape[1]:
                        result += coef * torch.pow(x[:, i:i+1], power)
                return result + self.bias
                
        f_function = PolynomialEvenFunction(coefficients, powers, bias)
        selected_f_name = f"polynomial_non_invertible_coef_{coefficients.tolist()}_powers_{powers}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_exponential_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建指数函数 (可逆): y = a1*exp(b1*x1) + a2*exp(b2*x2) + ... + c"""
        coefficients = config.get('coefficients', None)
        exp_coefficients = config.get('exp_coefficients', None)
        bias = config.get('bias', 0.0)

        if coefficients is None or exp_coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 13)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 13)

            if coefficients is None:
                coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if exp_coefficients is None:
                exp_coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if bias == 0.0:  # 如果bias是默认值，也随机生成
                bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        if not isinstance(exp_coefficients, torch.Tensor):
            exp_coefficients = torch.tensor(exp_coefficients, dtype=torch.float32, device=device)
        else:
            exp_coefficients = exp_coefficients.to(device)
            
        class ExponentialFunction(nn.Module):
            def __init__(self, coefficients, exp_coefficients, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.register_buffer('exp_coefficients', exp_coefficients)
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, exp_coef) in enumerate(zip(self.coefficients, self.exp_coefficients)):
                    if i < x.shape[1]:
                        result += coef * torch.exp(exp_coef * x[:, i:i+1])
                return result + self.bias
                
        f_function = ExponentialFunction(coefficients, exp_coefficients, bias)
        selected_f_name = f"exponential_invertible_coef_{coefficients.tolist()}_exp_coef_{exp_coefficients.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_logarithmic_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建对数函数 (可逆): y = a1*log(b1*|x1|+1) + a2*log(b2*|x2|+1) + ... + c"""
        coefficients = config.get('coefficients', None)
        log_coefficients = config.get('log_coefficients', None)
        bias = config.get('bias', 0.0)

        if coefficients is None or log_coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 14)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 14)

            if coefficients is None:
                coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if log_coefficients is None:
                log_coefficients = torch.abs(torch.normal(0, init_std, (in_dim,), device=device)).tolist()  # 确保为正数
            if bias == 0.0:  # 如果bias是默认值，也随机生成
                bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        if not isinstance(log_coefficients, torch.Tensor):
            log_coefficients = torch.tensor(log_coefficients, dtype=torch.float32, device=device)
        else:
            log_coefficients = log_coefficients.to(device)
            
        class LogarithmicFunction(nn.Module):
            def __init__(self, coefficients, log_coefficients, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.register_buffer('log_coefficients', log_coefficients)
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, log_coef) in enumerate(zip(self.coefficients, self.log_coefficients)):
                    if i < x.shape[1]:
                        # 使用 log(|x|+1) 确保输入为正且在x=0处连续
                        result += coef * torch.log(log_coef * torch.abs(x[:, i:i+1]) + 1)
                return result + self.bias
                
        f_function = LogarithmicFunction(coefficients, log_coefficients, bias)
        selected_f_name = f"logarithmic_invertible_coef_{coefficients.tolist()}_log_coef_{log_coefficients.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_sigmoid_scaled_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建缩放sigmoid函数 (可逆): y = a1*sigmoid(b1*x1) + a2*sigmoid(b2*x2) + ... + c"""
        coefficients = config.get('coefficients', None)
        scale_coefficients = config.get('scale_coefficients', None)
        bias = config.get('bias', 0.0)

        if coefficients is None or scale_coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 15)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 15)

            if coefficients is None:
                coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if scale_coefficients is None:
                scale_coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if bias == 0.0:  # 如果bias是默认值，也随机生成
                bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        if not isinstance(scale_coefficients, torch.Tensor):
            scale_coefficients = torch.tensor(scale_coefficients, dtype=torch.float32, device=device)
        else:
            scale_coefficients = scale_coefficients.to(device)
            
        class SigmoidScaledFunction(nn.Module):
            def __init__(self, coefficients, scale_coefficients, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.register_buffer('scale_coefficients', scale_coefficients)
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, scale_coef) in enumerate(zip(self.coefficients, self.scale_coefficients)):
                    if i < x.shape[1]:
                        result += coef * torch.sigmoid(scale_coef * x[:, i:i+1])
                return result + self.bias
                
        f_function = SigmoidScaledFunction(coefficients, scale_coefficients, bias)
        selected_f_name = f"sigmoid_scaled_invertible_coef_{coefficients.tolist()}_scale_{scale_coefficients.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_tanh_scaled_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建缩放tanh函数 (可逆): y = a1*tanh(b1*x1) + a2*tanh(b2*x2) + ... + c"""
        coefficients = config.get('coefficients', None)
        scale_coefficients = config.get('scale_coefficients', None)
        bias = config.get('bias', 0.0)

        if coefficients is None or scale_coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 16)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 16)

            if coefficients is None:
                coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if scale_coefficients is None:
                scale_coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if bias == 0.0:  # 如果bias是默认值，也随机生成
                bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        if not isinstance(scale_coefficients, torch.Tensor):
            scale_coefficients = torch.tensor(scale_coefficients, dtype=torch.float32, device=device)
        else:
            scale_coefficients = scale_coefficients.to(device)
            
        class TanhScaledFunction(nn.Module):
            def __init__(self, coefficients, scale_coefficients, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.register_buffer('scale_coefficients', scale_coefficients)
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, scale_coef) in enumerate(zip(self.coefficients, self.scale_coefficients)):
                    if i < x.shape[1]:
                        result += coef * torch.tanh(scale_coef * x[:, i:i+1])
                return result + self.bias
                
        f_function = TanhScaledFunction(coefficients, scale_coefficients, bias)
        selected_f_name = f"tanh_scaled_invertible_coef_{coefficients.tolist()}_scale_{scale_coefficients.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_sine_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建正弦函数 (不可逆): y = a1*sin(b1*x1) + a2*sin(b2*x2) + ... + c"""
        coefficients = config.get('coefficients', None)
        frequency_coefficients = config.get('frequency_coefficients', None)
        bias = config.get('bias', 0.0)

        if coefficients is None or frequency_coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 17)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 17)

            if coefficients is None:
                coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if frequency_coefficients is None:
                frequency_coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if bias == 0.0:  # 如果bias是默认值，也随机生成
                bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        if not isinstance(frequency_coefficients, torch.Tensor):
            frequency_coefficients = torch.tensor(frequency_coefficients, dtype=torch.float32, device=device)
        else:
            frequency_coefficients = frequency_coefficients.to(device)
            
        class SineFunction(nn.Module):
            def __init__(self, coefficients, frequency_coefficients, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.register_buffer('frequency_coefficients', frequency_coefficients)
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, freq_coef) in enumerate(zip(self.coefficients, self.frequency_coefficients)):
                    if i < x.shape[1]:
                        result += coef * torch.sin(freq_coef * x[:, i:i+1])
                return result + self.bias
                
        f_function = SineFunction(coefficients, frequency_coefficients, bias)
        selected_f_name = f"sine_non_invertible_coef_{coefficients.tolist()}_freq_{frequency_coefficients.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_cosine_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建余弦函数 (不可逆): y = a1*cos(b1*x1) + a2*cos(b2*x2) + ... + c"""
        coefficients = config.get('coefficients', None)
        frequency_coefficients = config.get('frequency_coefficients', None)
        bias = config.get('bias', 0.0)

        if coefficients is None or frequency_coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 18)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 18)

            if coefficients is None:
                coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if frequency_coefficients is None:
                frequency_coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if bias == 0.0:  # 如果bias是默认值，也随机生成
                bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        if not isinstance(frequency_coefficients, torch.Tensor):
            frequency_coefficients = torch.tensor(frequency_coefficients, dtype=torch.float32, device=device)
        else:
            frequency_coefficients = frequency_coefficients.to(device)
            
        class CosineFunction(nn.Module):
            def __init__(self, coefficients, frequency_coefficients, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.register_buffer('frequency_coefficients', frequency_coefficients)
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, freq_coef) in enumerate(zip(self.coefficients, self.frequency_coefficients)):
                    if i < x.shape[1]:
                        result += coef * torch.cos(freq_coef * x[:, i:i+1])
                return result + self.bias
                
        f_function = CosineFunction(coefficients, frequency_coefficients, bias)
        selected_f_name = f"cosine_non_invertible_coef_{coefficients.tolist()}_freq_{frequency_coefficients.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_relu_quadratic_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建ReLU + 二次项组合 (不可逆): y = a1*ReLU(x1) + b1*x1^2 + a2*ReLU(x2) + b2*x2^2 + ... + c"""
        relu_coefficients = config.get('relu_coefficients', None)
        quad_coefficients = config.get('quad_coefficients', None)
        bias = config.get('bias', 0.0)

        if relu_coefficients is None or quad_coefficients is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 19)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 19)

            if relu_coefficients is None:
                relu_coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if quad_coefficients is None:
                quad_coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if bias == 0.0:  # 如果bias是默认值，也随机生成
                bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        
        if not isinstance(relu_coefficients, torch.Tensor):
            relu_coefficients = torch.tensor(relu_coefficients, dtype=torch.float32, device=device)
        else:
            relu_coefficients = relu_coefficients.to(device)
            
        if not isinstance(quad_coefficients, torch.Tensor):
            quad_coefficients = torch.tensor(quad_coefficients, dtype=torch.float32, device=device)
        else:
            quad_coefficients = quad_coefficients.to(device)
            
        class ReLUQuadraticFunction(nn.Module):
            def __init__(self, relu_coefficients, quad_coefficients, bias):
                super().__init__()
                self.register_buffer('relu_coefficients', relu_coefficients)
                self.register_buffer('quad_coefficients', quad_coefficients)
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (relu_coef, quad_coef) in enumerate(zip(self.relu_coefficients, self.quad_coefficients)):
                    if i < x.shape[1]:
                        result += relu_coef * torch.relu(x[:, i:i+1]) + quad_coef * torch.pow(x[:, i:i+1], 2)
                return result + self.bias
                
        f_function = ReLUQuadraticFunction(relu_coefficients, quad_coefficients, bias)
        selected_f_name = f"relu_quadratic_non_invertible_relu_{relu_coefficients.tolist()}_quad_{quad_coefficients.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_gaussian_rbf_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建高斯径向基函数 (不可逆): y = a1*exp(-b1*(x1-c1)^2) + a2*exp(-b2*(x2-c2)^2) + ... + d"""
        coefficients = config.get('coefficients', None)
        width_coefficients = config.get('width_coefficients', None)
        centers = config.get('centers', None)
        bias = config.get('bias', 0.0)

        if coefficients is None or width_coefficients is None or centers is None:
            # 如果没有指定系数，随机生成
            # 保存当前torch随机状态
            torch_state = torch.get_rng_state()
            cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

            if seed is not None:
                torch.manual_seed(seed + 20)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(seed + 20)

            if coefficients is None:
                coefficients = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if width_coefficients is None:
                width_coefficients = torch.abs(torch.normal(0, init_std, (in_dim,), device=device)).tolist()  # 确保为正数
            if centers is None:
                centers = torch.normal(0, init_std, (in_dim,), device=device).tolist()
            if bias == 0.0:  # 如果bias是默认值，也随机生成
                bias = torch.normal(0, init_std, (1,), device=device).item()

            # 恢复torch随机状态
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)
        
        if not isinstance(coefficients, torch.Tensor):
            coefficients = torch.tensor(coefficients, dtype=torch.float32, device=device)
        else:
            coefficients = coefficients.to(device)
            
        if not isinstance(width_coefficients, torch.Tensor):
            width_coefficients = torch.tensor(width_coefficients, dtype=torch.float32, device=device)
        else:
            width_coefficients = width_coefficients.to(device)
            
        if not isinstance(centers, torch.Tensor):
            centers = torch.tensor(centers, dtype=torch.float32, device=device)
        else:
            centers = centers.to(device)
            
        class GaussianRBFFunction(nn.Module):
            def __init__(self, coefficients, width_coefficients, centers, bias):
                super().__init__()
                self.register_buffer('coefficients', coefficients)
                self.register_buffer('width_coefficients', width_coefficients)
                self.register_buffer('centers', centers)
                self.bias = bias
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                for i, (coef, width, center) in enumerate(zip(self.coefficients, self.width_coefficients, self.centers)):
                    if i < x.shape[1]:
                        result += coef * torch.exp(-width * torch.pow(x[:, i:i+1] - center, 2))
                return result + self.bias
                
        f_function = GaussianRBFFunction(coefficients, width_coefficients, centers, bias)
        selected_f_name = f"gaussian_rbf_non_invertible_coef_{coefficients.tolist()}_width_{width_coefficients.tolist()}_centers_{centers.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_gaussian_process_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建高斯过程函数 (不可逆): 使用GPy库生成高斯过程样本"""
        if not GPY_AVAILABLE:
            raise ValueError("高斯过程函数需要安装GPy库: pip install GPy")
            
        lengthscale = config.get('lengthscale', 1.0)
        f_magn = config.get('f_magn', 3.0)
        
        class GaussianProcessFunction(nn.Module):
            def __init__(self, lengthscale, f_magn, input_dim, seed=None):
                super().__init__()
                self.lengthscale = lengthscale
                self.f_magn = f_magn
                self.input_dim = input_dim
                self.seed = seed

            def forward(self, x):
                # 保存当前numpy随机状态
                np_state = np.random.get_state()

                # 基于输入内容生成确定性种子以确保相同输入产生相同输出
                if self.seed is not None:
                    input_hash = hash(x.cpu().numpy().tobytes())
                    np.random.seed(self.seed + 20 + (input_hash % 10000))

                # 直接使用sampleGP的逻辑
                # 将输入转换为numpy数组
                X = x.cpu().numpy()

                # 创建RBF核，使用实际输入维度
                ker = GPy.kern.RBF(input_dim=X.shape[1], lengthscale=self.lengthscale, variance=self.f_magn)

                # 计算协方差矩阵
                C = ker.K(X, X)

                # 添加数值稳定性项
                C += 1e-6 * np.eye(len(X))

                # 从多元高斯分布采样
                X_sample = np.random.multivariate_normal(np.zeros(len(X)), C, size=1)

                # 恢复numpy随机状态
                np.random.set_state(np_state)

                # 转换回torch tensor并调整形状
                result = torch.tensor(X_sample[0], dtype=torch.float32, device=x.device).unsqueeze(1)

                return result
                
        f_function = GaussianProcessFunction(lengthscale, f_magn, in_dim, seed)
        selected_f_name = f"gaussian_process_lengthscale_{lengthscale}_magn_{f_magn}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_fourier_series_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建傅里叶级数函数 (不可逆): y = sum(a_i*cos(i*x) + b_i*sin(i*x))"""
        n_terms = config.get('n_terms', 5)
        a_coeffs = config.get('a_coeffs', None)
        b_coeffs = config.get('b_coeffs', None)
        bias = config.get('bias', 0.0)
        
        # 如果没有指定系数，随机生成
        if a_coeffs is None or b_coeffs is None:
            # 保存当前numpy随机状态
            np_state = np.random.get_state()

            if seed is not None:
                np.random.seed(seed + 21)
            a_coeffs = np.random.randn(n_terms).tolist()
            b_coeffs = np.random.randn(n_terms).tolist()

            # 恢复numpy随机状态
            np.random.set_state(np_state)
            
        if not isinstance(a_coeffs, torch.Tensor):
            a_coeffs = torch.tensor(a_coeffs, dtype=torch.float32, device=device)
        else:
            a_coeffs = a_coeffs.to(device)
            
        if not isinstance(b_coeffs, torch.Tensor):
            b_coeffs = torch.tensor(b_coeffs, dtype=torch.float32, device=device)
        else:
            b_coeffs = b_coeffs.to(device)
            
        class FourierSeriesFunction(nn.Module):
            def __init__(self, a_coeffs, b_coeffs, n_terms, bias, input_dim):
                super().__init__()
                self.register_buffer('a_coeffs', a_coeffs)
                self.register_buffer('b_coeffs', b_coeffs)
                self.n_terms = n_terms
                self.bias = bias
                self.input_dim = input_dim
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                
                # 对每个输入维度应用傅里叶级数
                for dim in range(min(x.shape[1], self.input_dim)):
                    x_dim = x[:, dim:dim+1]
                    fourier_result = torch.zeros_like(x_dim)
                    
                    for i in range(self.n_terms):
                        fourier_result += (self.a_coeffs[i] * torch.cos((i+1) * x_dim) +
                                        self.b_coeffs[i] * torch.sin((i+1) * x_dim))
                    
                    result += fourier_result
                    
                return result + self.bias
                
        f_function = FourierSeriesFunction(a_coeffs, b_coeffs, n_terms, bias, in_dim)
        selected_f_name = f"fourier_series_terms_{n_terms}_a_{a_coeffs.tolist()}_b_{b_coeffs.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_piecewise_linear_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建分段线性函数 (不可逆): 使用线性插值"""
        x_points = config.get('x_points', 10)
        y_values = config.get('y_values', None)
        x_range = config.get('x_range', [-1, 1])
        bias = config.get('bias', 0.0)
        
        # 生成x坐标点
        xs = np.linspace(x_range[0], x_range[1], x_points)
        
        # 如果没有指定y值，随机生成
        if y_values is None:
            # 保存当前numpy随机状态
            np_state = np.random.get_state()

            if seed is not None:
                np.random.seed(seed + 22)
            y_values = np.random.randn(x_points).tolist()

            # 恢复numpy随机状态
            np.random.set_state(np_state)
            
        if not isinstance(y_values, torch.Tensor):
            y_values = torch.tensor(y_values, dtype=torch.float32, device=device)
        else:
            y_values = y_values.to(device)
            
        class PiecewiseLinearFunction(nn.Module):
            def __init__(self, xs, y_values, bias, input_dim):
                super().__init__()
                self.register_buffer('xs', torch.tensor(xs, dtype=torch.float32))
                self.register_buffer('y_values', y_values)
                self.bias = bias
                self.input_dim = input_dim
                
            def forward(self, x):
                result = torch.zeros(x.shape[0], 1, device=x.device)
                
                # 对每个输入维度应用分段线性插值
                for dim in range(min(x.shape[1], self.input_dim)):
                    x_dim = x[:, dim].cpu()
                    interpolated = torch.zeros_like(x_dim)
                    
                    for i in range(len(x_dim)):
                        x_val = x_dim[i].item()
                        # 使用numpy的interp函数进行线性插值
                        interp_val = np.interp(x_val, self.xs.cpu().numpy(), self.y_values.cpu().numpy())
                        interpolated[i] = interp_val
                        
                    result += interpolated.to(x.device).unsqueeze(1)
                    
                return result + self.bias
                
        f_function = PiecewiseLinearFunction(xs, y_values, bias, in_dim)
        selected_f_name = f"piecewise_linear_points_{x_points}_range_{x_range}_y_{y_values.tolist()}_bias_{bias}"
        return f_function, selected_f_name
    
    @staticmethod
    def _create_random_neural_network_function(config, in_dim, out_dim, device, init_std, seed=None):
        """创建随机神经网络函数 (不可逆): 使用随机初始化的小型神经网络"""
        hidden_dim = config.get('hidden_dim', 10)
        depth = config.get('depth', 2)
        activation = config.get('activation', 'tanh')  # 'tanh', 'relu', 'sigmoid'
        
        # 设置随机种子以确保可重现性
        # 保存当前torch随机状态
        torch_state = torch.get_rng_state()
        cuda_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None

        if seed is not None:
            torch.manual_seed(seed + 23)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(seed + 23)
                
        class RandomNeuralNetworkFunction(nn.Module):
            def __init__(self, input_dim, hidden_dim, depth, activation):
                super().__init__()
                layers = []
                
                # 输入层
                layers.append(nn.Linear(input_dim, hidden_dim))
                
                # 激活函数选择
                if activation == 'tanh':
                    act_fn = nn.Tanh()
                elif activation == 'sigmoid':
                    act_fn = nn.Sigmoid()
                elif activation == 'relu':
                    act_fn = nn.ReLU()
                elif activation == 'leaky_relu':
                    act_fn = nn.LeakyReLU(0.2)
                elif activation == 'softplus':
                    act_fn = nn.Softplus()
                else:
                    act_fn = nn.Tanh()  # 默认使用tanh
                    
                layers.append(act_fn)
                
                # 隐藏层
                for _ in range(depth - 1):
                    layers.append(nn.Linear(hidden_dim, hidden_dim))
                    layers.append(act_fn)
                    
                # 输出层
                layers.append(nn.Linear(hidden_dim, 1))
                
                self.network = nn.Sequential(*layers)
                
                # 根据激活函数类型选择合适的初始化方法
                for layer in self.network:
                    if isinstance(layer, nn.Linear):
                        if activation in ['relu', 'leaky_relu']:
                            # ReLU/LeakyReLU激活函数使用He初始化
                            nn.init.kaiming_normal_(layer.weight, mode='fan_in', nonlinearity='relu')
                            nn.init.zeros_(layer.bias)
                        else:
                            # 其他激活函数使用Xavier初始化
                            nn.init.xavier_normal_(layer.weight)
                            nn.init.zeros_(layer.bias)
                        
            def forward(self, x):
                return self.network(x)
                
        f_function = RandomNeuralNetworkFunction(in_dim, hidden_dim, depth, activation)

        # 恢复torch随机状态
        torch.set_rng_state(torch_state)
        if torch.cuda.is_available() and cuda_state is not None:
            torch.cuda.set_rng_state(cuda_state)

        selected_f_name = f"random_nn_hidden_{hidden_dim}_depth_{depth}_activation_{activation}"
        return f_function, selected_f_name

    @staticmethod
    def create_g_function(g_function_config=None, device='cpu', init_std=1.0, seed=None):
        """
        创建g函数（后非线性变换函数）

        参数:
            g_function_config: g函数配置字典，包含type和其他参数
                              如果为None或只是字符串，则使用简单的激活函数
            device: 计算设备
            init_std: 初始化标准差
            seed: 随机种子

        返回:
            g_function: 生成的g函数
            selected_g_name: g函数名称
        """
        # 如果传入的是字符串，转换为配置字典
        if isinstance(g_function_config, str):
            g_function_type = g_function_config
            g_function_config = {'type': g_function_type}
        elif g_function_config is None:
            g_function_config = {'type': 'identity'}

        g_function_type = g_function_config.get('type', 'identity')

        # 简单的单调激活函数
        simple_activation_choices = {
            "identity": nn.Identity(),  # 恒等函数 (对应ANM模型)
            "sigmoid": nn.Sigmoid(),  # Sigmoid函数 (0,1)范围内单调递增
            "tanh": nn.Tanh(),  # Tanh函数 (-1,1)范围内单调递增
            "softplus": nn.Softplus(),  # Softplus函数 (0,∞)范围内单调递增
            "elu+1": lambda x: torch.nn.functional.elu(x) + 1,  # ELU+1函数(确保正值，单调递增)
            "scaled_tanh": lambda x: 2 * torch.tanh(x / 2)  # 缩放的tanh函数，保持单调性
        }

        # 如果是简单激活函数，直接返回
        if g_function_type in simple_activation_choices:
            g_function = simple_activation_choices[g_function_type]
            selected_g_name = g_function_type
            return g_function, selected_g_name

        # 如果是复杂函数类型，使用create_f_function创建1维函数
        try:
            # 创建1维的g函数（输入维度=1，输出维度=1）
            g_function, selected_g_name = FunctionGenerator.create_f_function(
                g_function_config,
                in_dim=1,
                out_dim=1,
                device=device,
                init_std=init_std,
                seed=seed
            )

            # 为g函数添加前缀以区分
            selected_g_name = f"g_{selected_g_name}"

            return g_function, selected_g_name

        except Exception as e:
            raise ValueError(f"不支持的g函数类型: {g_function_type}. 错误: {e}. "
                           f"支持的简单类型: {list(simple_activation_choices.keys())} "
                           f"或任何function_generator.py中支持的函数类型")

