{"metadata": {"total_datasets": 2, "total_configs": 1, "generation_time": "2025-07-26 23:11:45", "train_test_split": 0.7, "noise_config": {"min_noise_std": 0.1, "max_noise_std": 0.5, "noise_distribution": "gaussian", "noise_mean": 0.0, "use_snr_method": false, "snr_config": {}, "use_monte_carlo_precompute": false}, "root_distribution_config": {"root_distribution": "gaussian", "root_mean": 0.0, "root_std": 1.0, "sample_root_std": false, "min_root": 0.0, "max_root": 1.0, "sample_cause_ranges": false}}, "results": {"parent_snr_2.0_child_snr_2.0_other_snr_2.0": {"config_info": {"name": "parent_snr_2.0_child_snr_2.0_other_snr_2.0", "description": "配置: parent_snr_2.0_child_snr_2.0_other_snr_2.0"}, "datasets": {"dataset_0": {"dataset_info": {"num_samples": 1000, "num_features": 11, "num_nodes": 12, "root_distribution": "gaussian", "root_mean": 0.0, "root_actual_std": 1.0, "root_sample_std": false, "root_config_std": 1.0, "root_min": null, "root_max": null, "sample_cause_ranges": false}, "nodes": {"X0": {"node_type": "spouse", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -2.9472272396087646, "q25": -0.640460193157196, "median": 0.003552935551851988, "q75": 0.6695248484611511, "max": 3.150064468383789, "mean": 0.007220188155770302, "std": 0.9910575151443481}, "correlation_info": {}, "mutual_info": {}}, "X1": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.220081329345703, "q25": -0.6531154811382294, "median": -0.04713773354887962, "q75": 0.7039025127887726, "max": 3.7655088901519775, "mean": -0.009959234856069088, "std": 1.0253124237060547}, "correlation_info": {}, "mutual_info": {}}, "X2": {"node_type": "spouse", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.027423143386841, "q25": -0.7001160383224487, "median": -0.053168730810284615, "q75": 0.6165940165519714, "max": 2.690513849258423, "mean": -0.044497743248939514, "std": 1.0068353414535522}, "correlation_info": {}, "mutual_info": {}}, "X4": {"node_type": "spouse", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.0330166816711426, "q25": -0.7224423587322235, "median": -0.08536652103066444, "q75": 0.6616537868976593, "max": 3.0965616703033447, "mean": -0.01306991744786501, "std": 1.0071367025375366}, "correlation_info": {}, "mutual_info": {}}, "X5": {"node_type": "spouse", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -2.8445327281951904, "q25": -0.6951638013124466, "median": -0.028322049416601658, "q75": 0.6614540815353394, "max": 4.076303958892822, "mean": -0.007830151356756687, "std": 0.9998818635940552}, "correlation_info": {}, "mutual_info": {}}, "X7": {"node_type": "parent", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -4.161297798156738, "q25": -0.6392237395048141, "median": -0.0007888543186709285, "q75": 0.6516739428043365, "max": 3.2961862087249756, "mean": 0.0026921615935862064, "std": 1.0039894580841064}, "correlation_info": {}, "mutual_info": {}}, "X9": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X0"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[0.6931923627853394], [0.35519441962242126]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.933638870716095, 0.3137735426425934], [-1.1643801927566528, 0.8299421072006226]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.14722949266433716, 1.2456068992614746], [-0.5750388503074646, 1.015712857246399]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[0.706687331199646, 0.25570714473724365]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[0.6931923627853394], [0.35519441962242126]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.933638870716095, 0.3137735426425934], [-1.1643801927566528, 0.8299421072006226]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[0.14722949266433716, 1.2456068992614746], [-0.5750388503074646, 1.015712857246399]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[0.706687331199646, 0.25570714473724365]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.24591930200898413, "actual_noise_std": 0.2485682122631903, "actual_snr": 1.9576004357998613, "signal_var": 0.12095260620117188, "signal_mean": -0.004052561707794666, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": -0.0008844566182233393}, "data_statistics": {"min": -1.049393892288208, "q25": -0.33073824644088745, "median": -0.008229438215494156, "q75": 0.3074774593114853, "max": 1.2023762464523315, "mean": -0.00493701733648777, "std": 0.42269790172576904}, "correlation_info": {}, "mutual_info": {}}, "X11": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X0", "X1"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-1.13004469871521, -0.6105726361274719], [-0.7025976777076721, 0.5773240923881531]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.5864290595054626, -1.1155169010162354], [-0.7649444937705994, -0.024639060720801353]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.46265938878059387, 0.8250048160552979], [-1.0768214464187622, -0.4583653509616852]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[-0.9908116459846497, 0.4290201961994171]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-1.13004469871521, -0.6105726361274719], [-0.7025976777076721, 0.5773240923881531]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[-0.5864290595054626, -1.1155169010162354], [-0.7649444937705994, -0.024639060720801353]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[-0.46265938878059387, 0.8250048160552979], [-1.0768214464187622, -0.4583653509616852]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[-0.9908116459846497, 0.4290201961994171]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.281014927006041, "actual_noise_std": 0.2764306282044916, "actual_snr": 2.066885664812713, "signal_var": 0.15793877840042114, "signal_mean": 0.0047696675173938274, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": 0.0006206936668604612}, "data_statistics": {"min": -1.3869855403900146, "q25": -0.3702714145183563, "median": 0.011493280529975891, "q75": 0.3694686144590378, "max": 1.3164112567901611, "mean": 0.005390360485762358, "std": 0.49358686804771423}, "correlation_info": {}, "mutual_info": {}}, "X8": {"node_type": "target", "function_config": {"type": "random_neural_network", "parents": ["X7"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[0.3084336221218109], [0.028392748907208443]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.5670085549354553, -0.5229790210723877], [1.2692124843597412, 0.2506999969482422]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.0683913603425026, 1.0589938163757324], [-1.1089633703231812, -0.6702011227607727]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[0.20613537728786469, 0.4068562686443329]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[0.3084336221218109], [0.028392748907208443]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[-0.5670085549354553, -0.5229790210723877], [1.2692124843597412, 0.2506999969482422]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[0.0683913603425026, 1.0589938163757324], [-1.1089633703231812, -0.6702011227607727]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[0.20613537728786469, 0.4068562686443329]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.036174508471641054, "actual_noise_std": 0.036450840649829376, "actual_snr": 1.9697911222464293, "signal_var": 0.0026171901263296604, "signal_mean": 0.00018571113469079137, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": 0.002130778506398201}, "data_statistics": {"min": -0.1769372820854187, "q25": -0.04231668822467327, "median": 0.003117580898106098, "q75": 0.0476838918402791, "max": 0.18649211525917053, "mean": 0.002316489350050688, "std": 0.06367724388837814}, "correlation_info": {}, "mutual_info": {}}, "X10": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X7"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[0.37442636489868164], [0.32748016715049744]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.15240205824375153, -0.46262678503990173], [-0.09215927124023438, -0.15019460022449493]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.6705991625785828, -0.7615662813186646], [0.4807446599006653, 0.48551952838897705]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[-0.8894643187522888, 2.610429286956787]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[0.37442636489868164], [0.32748016715049744]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[-0.15240205824375153, -0.46262678503990173], [-0.09215927124023438, -0.15019460022449493]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[0.6705991625785828, -0.7615662813186646], [0.4807446599006653, 0.48551952838897705]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[-0.8894643187522888, 2.610429286956787]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.1849127524429957, "actual_noise_std": 0.18446726672496927, "actual_snr": 2.009671605085322, "signal_var": 0.06838545203208923, "signal_mean": -0.0009721691603772342, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": -0.0008315002778545022}, "data_statistics": {"min": -0.8268560767173767, "q25": -0.2295091710984707, "median": -0.0075486041605472565, "q75": 0.21524059772491455, "max": 0.9113072156906128, "mean": -0.0018036699621006846, "std": 0.3126097619533539}, "correlation_info": {}, "mutual_info": {}}, "X3": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X9"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[0.2694470286369324], [0.8971376419067383]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.9437286257743835, -0.9398272633552551], [-0.36303311586380005, 0.3694781959056854]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.29438379406929016, -0.283933162689209], [0.009661471471190453, -1.0317363739013672]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[0.8844928741455078, 0.40324151515960693]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[0.2694470286369324], [0.8971376419067383]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.9437286257743835, -0.9398272633552551], [-0.36303311586380005, 0.3694781959056854]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[-0.29438379406929016, -0.283933162689209], [0.009661471471190453, -1.0317363739013672]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[0.8844928741455078, 0.40324151515960693]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.0021892468965292434, "actual_noise_std": 0.002108355756297986, "actual_snr": 2.1564117675883803, "signal_var": 9.585603947925847e-06, "signal_mean": 3.472717071417719e-05, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": 5.850559318787418e-05}, "data_statistics": {"min": -0.011936621740460396, "q25": -0.0022735834354534745, "median": 0.0001689107230049558, "q75": 0.0024678586050868034, "max": 0.011198786087334156, "mean": 9.323276754003018e-05, "std": 0.0037643746472895145}, "correlation_info": {}, "mutual_info": {}}, "X6": {"node_type": "child", "function_config": {"type": "random_neural_network", "parents": ["X0", "X2", "X4", "X5", "X8"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 5, "output_size": 2, "weights_shape": [2, 5], "has_bias": true, "weights": [[-0.30672577023506165, 0.34846046566963196, -0.03741852566599846, -0.18550443649291992, -0.2041933834552765], [0.03485288843512535, -0.2659726142883301, 0.15461011230945587, -0.5887560844421387, 0.18650783598423004]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-1.0622299909591675, 0.39295533299446106], [-0.1810728758573532, 0.6502306461334229]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.3508720099925995, 0.18586182594299316], [-0.7651395201683044, 0.5282752513885498]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[-0.2725215256214142, -0.7516961097717285]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-0.30672577023506165, 0.34846046566963196, -0.03741852566599846, -0.18550443649291992, -0.2041933834552765], [0.03485288843512535, -0.2659726142883301, 0.15461011230945587, -0.5887560844421387, 0.18650783598423004]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[-1.0622299909591675, 0.39295533299446106], [-0.1810728758573532, 0.6502306461334229]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[0.3508720099925995, 0.18586182594299316], [-0.7651395201683044, 0.5282752513885498]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[-0.2725215256214142, -0.7516961097717285]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.11212060187611693, "actual_noise_std": 0.10833262688226096, "actual_snr": 2.1423098675868193, "signal_var": 0.025142058730125427, "signal_mean": 0.002802908420562744, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": -0.0007584447739645839}, "data_statistics": {"min": -0.5928860902786255, "q25": -0.13984011113643646, "median": -0.0063990019261837006, "q75": 0.1437692902982235, "max": 0.5353990793228149, "mean": 0.0020444639958441257, "std": 0.19196632504463196}, "correlation_info": {}, "mutual_info": {}}}, "model_results": {"ols": {"performance": {"r2_train": 0.6645325960251084, "r2_test": 0.6538478653775897, "r2_intervention": 0.6592497748411339, "rmse_train": 0.03643709793686867, "rmse_test": 0.03832615539431572, "rmse_intervention": 0.038025930523872375, "mape_train": 292.3774480819702, "mape_test": 231.10713958740234, "mape_intervention": 197.16511964797974}, "feature_importance": {"permutation": {"parents": {"X7": 1.1244067766835226}, "children": {"X6": 0.0003833130184957752}, "spouses": {"X0": 4.3635778338361554e-05, "X2": 8.508689788537065e-05, "X4": 5.371447446263685e-05, "X5": -0.0011245873038390568}, "others": {"X1": -0.0039848604283307205, "X3": -0.0007119539203260065, "X9": 0.003958221069035761, "X10": 0.0007209318453040477, "X11": 0.008117334980056587}}, "builtin": {"parents": {"X7": 0.04676859453320503}, "children": {"X6": 0.000927072367630899}, "spouses": {"X0": 1.6005686120479368e-05, "X2": 0.0001635380176594481, "X4": 5.321591015672311e-05, "X5": 0.0013273149961605668}, "others": {"X1": 0.00311029190197587, "X3": 0.0013851437252014875, "X9": 0.002508289646357298, "X10": 0.005523992236703634, "X11": 0.005154705140739679}}}}, "xgboost": {"performance": {"r2_train": 0.9997703139846086, "r2_test": 0.6032192441663999, "r2_intervention": 0.5314509820259407, "rmse_train": 0.0009534236160106957, "rmse_test": 0.04103335738182068, "rmse_intervention": 0.04459018260240555, "mape_train": 7.400257885456085, "mape_test": 257.308554649353, "mape_intervention": 229.3161153793335}, "feature_importance": {"permutation": {"parents": {"X7": 1.174493804905028}, "children": {"X6": 0.014223872916147537}, "spouses": {"X0": -0.006774739297900401, "X2": -0.0018698236528388312, "X4": 0.0016112575687812214, "X5": -0.0033557606451192212}, "others": {"X1": 0.007284776798943275, "X3": -0.0006208398391777855, "X9": -4.3296085201964076e-05, "X10": 0.026600132819861694, "X11": 0.006901849609555348}}, "builtin": {"parents": {"X7": 0.7445172667503357}, "children": {"X6": 0.02715502865612507}, "spouses": {"X0": 0.017472537234425545, "X2": 0.01983410120010376, "X4": 0.04199356213212013, "X5": 0.021540366113185883}, "others": {"X1": 0.022673727944493294, "X3": 0.02144606038928032, "X9": 0.022078711539506912, "X10": 0.03801257163286209, "X11": 0.0232760701328516}}}}, "lightgbm": {"performance": {"r2_train": 0.9777879215947001, "r2_test": 0.6376899723258809, "r2_intervention": 0.5557580961491064, "rmse_train": 0.009375905479755498, "rmse_test": 0.039210460515365746, "rmse_intervention": 0.0434181711985822, "mape_train": 94.27965134457685, "mape_test": 237.58457353113994, "mape_intervention": 211.14385478690446}, "feature_importance": {"permutation": {"parents": {"X7": 1.175671594759298}, "children": {"X6": 0.006192811091776829}, "spouses": {"X0": -0.005243434463743128, "X2": -0.000889034483932997, "X4": 0.005654635621406075, "X5": 0.0018053171563428005}, "others": {"X1": 0.0036254656901560933, "X3": 0.01411999461119732, "X9": -0.004371502028945762, "X10": -0.0008848063999027378, "X11": 0.007844287420460061}}, "builtin": {"parents": {"X7": 246}, "children": {"X6": 212}, "spouses": {"X0": 242, "X2": 240, "X4": 278, "X5": 235}, "others": {"X1": 246, "X3": 232, "X9": 211, "X10": 240, "X11": 222}}}}}}, "dataset_1": {"dataset_info": {"num_samples": 1000, "num_features": 19, "num_nodes": 20, "root_distribution": "gaussian", "root_mean": 0.0, "root_actual_std": 1.0, "root_sample_std": false, "root_config_std": 1.0, "root_min": null, "root_max": null, "sample_cause_ranges": false}, "nodes": {"X1": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.252068281173706, "q25": -0.6060889065265656, "median": -0.027763145975768566, "q75": 0.6351120918989182, "max": 2.6144402027130127, "mean": -0.026345856487751007, "std": 0.9593968987464905}, "correlation_info": {}, "mutual_info": {}}, "X2": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.3911473751068115, "q25": -0.701249286532402, "median": -0.04701759293675423, "q75": 0.6615431904792786, "max": 3.286031484603882, "mean": -0.03046182356774807, "std": 1.0194488763809204}, "correlation_info": {}, "mutual_info": {}}, "X3": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.687379837036133, "q25": -0.6740703582763672, "median": 0.01894826628267765, "q75": 0.6731301844120026, "max": 3.2074856758117676, "mean": 0.021797044202685356, "std": 0.9994226694107056}, "correlation_info": {}, "mutual_info": {}}, "X4": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.534158706665039, "q25": -0.6376128196716309, "median": 0.011523867957293987, "q75": 0.6813396066427231, "max": 3.4859912395477295, "mean": 0.015631720423698425, "std": 0.9814038872718811}, "correlation_info": {}, "mutual_info": {}}, "X10": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.0527901649475098, "q25": -0.6744289845228195, "median": -0.03986120969057083, "q75": 0.6772820800542831, "max": 3.3620567321777344, "mean": -0.025432931259274483, "std": 0.9853286147117615}, "correlation_info": {}, "mutual_info": {}}, "X11": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.3661112785339355, "q25": -0.6392757892608643, "median": 0.00917318882420659, "q75": 0.6744588315486908, "max": 3.118537187576294, "mean": 0.027286294847726822, "std": 0.9905861616134644}, "correlation_info": {}, "mutual_info": {}}, "X13": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.3821980953216553, "q25": -0.795311227440834, "median": -0.08366009965538979, "q75": 0.6695856302976608, "max": 3.8129231929779053, "mean": -0.08196558803319931, "std": 1.0519307851791382}, "correlation_info": {}, "mutual_info": {}}, "X14": {"node_type": "spouse", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.185346841812134, "q25": -0.6996342092752457, "median": -0.04844798520207405, "q75": 0.6415145695209503, "max": 3.0367770195007324, "mean": -0.026883747428655624, "std": 0.9887366890907288}, "correlation_info": {}, "mutual_info": {}}, "X16": {"node_type": "spouse", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.411933183670044, "q25": -0.6341602057218552, "median": 0.04464315436780453, "q75": 0.6776522547006607, "max": 3.1942529678344727, "mean": 0.027809327468276024, "std": 0.996261715888977}, "correlation_info": {}, "mutual_info": {}}, "X17": {"node_type": "other", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.2770204544067383, "q25": -0.6962658166885376, "median": -0.03778115473687649, "q75": 0.6076787710189819, "max": 3.0183706283569336, "mean": -0.011542926542460918, "std": 0.9879218935966492}, "correlation_info": {}, "mutual_info": {}}, "X18": {"node_type": "parent", "function_config": null, "snr_info": {"target_snr": null, "configured_noise_std": null, "actual_noise_std": null, "actual_snr": null, "signal_var": null, "signal_mean": null, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": null}, "data_statistics": {"min": -3.0429983139038086, "q25": -0.7147723585367203, "median": 7.571902824565768e-05, "q75": 0.6097939908504486, "max": 3.1350831985473633, "mean": -0.009688746184110641, "std": 0.9653391242027283}, "correlation_info": {}, "mutual_info": {}}, "X6": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X1"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[-0.5533286333084106], [-0.4744862914085388]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.030193250626325607, -0.6631762981414795], [0.16143357753753662, -0.3827514946460724]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.16787265241146088, -0.565064013004303], [-1.8638076782226562, -0.5785309076309204]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[1.1053221225738525, -1.1102248430252075]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-0.5533286333084106], [-0.4744862914085388]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.030193250626325607, -0.6631762981414795], [0.16143357753753662, -0.3827514946460724]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[-0.16787265241146088, -0.565064013004303], [-1.8638076782226562, -0.5785309076309204]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[1.1053221225738525, -1.1102248430252075]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.2641977787549642, "actual_noise_std": 0.2708385800195774, "actual_snr": 1.9031247762267345, "signal_var": 0.139600932598114, "signal_mean": -0.007670116610825062, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": 0.000523025868460536}, "data_statistics": {"min": -1.468806266784668, "q25": -0.34520354121923447, "median": 0.010039936751127243, "q75": 0.32923057675361633, "max": 1.2656183242797852, "mean": -0.007147092837840319, "std": 0.45820409059524536}, "correlation_info": {}, "mutual_info": {}}, "X12": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X1"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[-1.1235566139221191], [0.590927004814148]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.542889416217804, -0.6960123181343079], [0.712909996509552, -0.23799698054790497]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[1.6188212633132935, -1.257881999015808], [0.7161151766777039, 0.7478328347206116]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[1.5085088014602661, 0.45416560769081116]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-1.1235566139221191], [0.590927004814148]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.542889416217804, -0.6960123181343079], [0.712909996509552, -0.23799698054790497]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[1.6188212633132935, -1.257881999015808], [0.7161151766777039, 0.7478328347206116]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[1.5085088014602661, 0.45416560769081116]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.4588750668491615, "actual_noise_std": 0.4544277701240604, "actual_snr": 2.039337900948667, "signal_var": 0.4211326539516449, "signal_mean": 0.014960283413529396, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": 0.01782730408012867}, "data_statistics": {"min": -1.8886151313781738, "q25": -0.5769775062799454, "median": 0.03490246832370758, "q75": 0.6517689526081085, "max": 2.5945169925689697, "mean": 0.032787587493658066, "std": 0.7932139039039612}, "correlation_info": {}, "mutual_info": {}}, "X0": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X1", "X3", "X10"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 3, "output_size": 2, "weights_shape": [2, 3], "has_bias": true, "weights": [[-0.49521318078041077, 0.20900730788707733, -0.7702761888504028], [-1.177626132965088, -0.15211908519268036, -1.1970769166946411]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.026766790077090263, -0.009737718850374222], [1.9266246557235718, 0.3976272940635681]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.6129510998725891, 0.580021858215332], [0.12779153883457184, -1.2490814924240112]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[0.5655810832977295, 0.6586061716079712]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-0.49521318078041077, 0.20900730788707733, -0.7702761888504028], [-1.177626132965088, -0.15211908519268036, -1.1970769166946411]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.026766790077090263, -0.009737718850374222], [1.9266246557235718, 0.3976272940635681]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[-0.6129510998725891, 0.580021858215332], [0.12779153883457184, -1.2490814924240112]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[0.5655810832977295, 0.6586061716079712]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.169110052460046, "actual_noise_std": 0.16984728568221275, "actual_snr": 1.982675420104178, "signal_var": 0.057196419686079025, "signal_mean": -0.0025642223190516233, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": 0.0129778403788805}, "data_statistics": {"min": -0.7743833661079407, "q25": -0.22988465055823326, "median": 0.005990378558635712, "q75": 0.2504454404115677, "max": 0.8384044170379639, "mean": 0.010413615964353085, "std": 0.2943542003631592}, "correlation_info": {}, "mutual_info": {}}, "X5": {"node_type": "target", "function_config": {"type": "random_neural_network", "parents": ["X18"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[-0.5670380592346191], [-0.21268492937088013]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.22462813556194305, 0.3311436176300049], [-0.4801875650882721, -0.6951608657836914]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.12252796441316605, -1.3093104362487793], [-0.43946370482444763, -0.2757583260536194]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[-0.09584053605794907, 0.8872470259666443]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-0.5670380592346191], [-0.21268492937088013]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[-0.22462813556194305, 0.3311436176300049], [-0.4801875650882721, -0.6951608657836914]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[0.12252796441316605, -1.3093104362487793], [-0.43946370482444763, -0.2757583260536194]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[-0.09584053605794907, 0.8872470259666443]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.039617589462281964, "actual_noise_std": 0.04118420945446951, "actual_snr": 1.8507366371524951, "signal_var": 0.0031391067896038294, "signal_mean": 0.000582234060857445, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": 0.001146446680650115}, "data_statistics": {"min": -0.18465322256088257, "q25": -0.04533464089035988, "median": 0.0024492768570780754, "q75": 0.05037431698292494, "max": 0.18032759428024292, "mean": 0.0017286805668845773, "std": 0.06813587248325348}, "correlation_info": {}, "mutual_info": {}}, "X7": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X18"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[1.650688886642456], [0.05254143849015236]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.3986241817474365, -0.5082407593727112], [0.620316743850708, 1.191197156906128]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.6283868551254272, -1.3564122915267944], [-0.3289402425289154, 0.15711082518100739]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[1.3202488422393799, -0.47639840841293335]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[1.650688886642456], [0.05254143849015236]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.3986241817474365, -0.5082407593727112], [0.620316743850708, 1.191197156906128]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[-0.6283868551254272, -1.3564122915267944], [-0.3289402425289154, 0.15711082518100739]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[1.3202488422393799, -0.47639840841293335]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.5817211784498586, "actual_noise_std": 0.5945483412021643, "actual_snr": 1.9146323943519477, "signal_var": 0.6767990589141846, "signal_mean": 0.001970267156139016, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": -0.022756092250347137}, "data_statistics": {"min": -2.712402820587158, "q25": -0.838523805141449, "median": -0.0216389000415802, "q75": 0.7897848784923553, "max": 2.4889724254608154, "mean": -0.020785827189683914, "std": 1.0058516263961792}, "correlation_info": {}, "mutual_info": {}}, "X9": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X1", "X2", "X4", "X11", "X13", "X17", "X18"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 7, "output_size": 2, "weights_shape": [2, 7], "has_bias": true, "weights": [[0.699317991733551, 0.12875936925411224, 0.16745831072330475, -0.33988869190216064, -0.013648545369505882, 0.43907430768013, 0.46288248896598816], [-0.03038267232477665, -0.18346509337425232, 0.892461359500885, -0.4555629789829254, -0.4791841506958008, -0.4929700195789337, -0.4539833664894104]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.839461088180542, 0.7856225967407227], [-0.5894966721534729, 1.2605456113815308]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.2795756161212921, 0.8735355138778687], [0.6612469553947449, 0.9221025109291077]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[2.082747459411621, -0.5672332048416138]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[0.699317991733551, 0.12875936925411224, 0.16745831072330475, -0.33988869190216064, -0.013648545369505882, 0.43907430768013, 0.46288248896598816], [-0.03038267232477665, -0.18346509337425232, 0.892461359500885, -0.4555629789829254, -0.4791841506958008, -0.4929700195789337, -0.4539833664894104]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.839461088180542, 0.7856225967407227], [-0.5894966721534729, 1.2605456113815308]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[-0.2795756161212921, 0.8735355138778687], [0.6612469553947449, 0.9221025109291077]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[2.082747459411621, -0.5672332048416138]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.5017577210304115, "actual_noise_std": 0.5160628396091219, "actual_snr": 1.8906578769941962, "signal_var": 0.5035216212272644, "signal_mean": 0.030362984165549278, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": -0.00033619499299675226}, "data_statistics": {"min": -2.1449899673461914, "q25": -0.6422855705022812, "median": 0.03795758634805679, "q75": 0.7063728272914886, "max": 2.225433349609375, "mean": 0.030026786029338837, "std": 0.8851791620254517}, "correlation_info": {}, "mutual_info": {}}, "X15": {"node_type": "other", "function_config": {"type": "random_neural_network", "parents": ["X12"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[-0.1223701536655426], [-0.7794690728187561]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.01818467117846012, 0.8308601975440979], [0.2843910753726959, 0.09277395904064178]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.47988346219062805, 0.17436248064041138], [0.03415045514702797, 0.10738971084356308]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[-0.31637781858444214, 0.5870367884635925]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-0.1223701536655426], [-0.7794690728187561]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.01818467117846012, 0.8308601975440979], [0.2843910753726959, 0.09277395904064178]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[-0.47988346219062805, 0.17436248064041138], [0.03415045514702797, 0.10738971084356308]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[-0.31637781858444214, 0.5870367884635925]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.046166494519129936, "actual_noise_std": 0.044965023589203325, "actual_snr": 2.1083084191080537, "signal_var": 0.004262690432369709, "signal_mean": -0.0025873498525470495, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": -0.0021744195837527514}, "data_statistics": {"min": -0.20752960443496704, "q25": -0.06490259058773518, "median": -0.005908825434744358, "q75": 0.05587897263467312, "max": 0.21248473227024078, "mean": -0.0047617703676223755, "std": 0.07916633039712906}, "correlation_info": {}, "mutual_info": {}}, "X8": {"node_type": "child", "function_config": {"type": "random_neural_network", "parents": ["X5", "X14", "X16"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 3, "output_size": 2, "weights_shape": [2, 3], "has_bias": true, "weights": [[-1.281104564666748, -0.08084024488925934, 0.09748270362615585], [-0.11440475285053253, 0.9015447497367859, -0.20562425255775452]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.8453316688537598, -0.9762266874313354], [-0.21785637736320496, 0.006424759980291128]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[1.290978193283081, 0.9719976782798767], [0.5323559641838074, -0.25685396790504456]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[-0.5551656484603882, -0.24446727335453033]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-1.281104564666748, -0.08084024488925934, 0.09748270362615585], [-0.11440475285053253, 0.9015447497367859, -0.20562425255775452]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.8453316688537598, -0.9762266874313354], [-0.21785637736320496, 0.006424759980291128]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[1.290978193283081, 0.9719976782798767], [0.5323559641838074, -0.25685396790504456]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[-0.5551656484603882, -0.24446727335453033]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.267105157890848, "actual_noise_std": 0.26198780407074584, "actual_snr": 2.078894236280207, "signal_var": 0.14269033074378967, "signal_mean": -0.01313625369220972, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": 0.00036324906977824867}, "data_statistics": {"min": -1.0700687170028687, "q25": -0.3707178235054016, "median": -0.040349796414375305, "q75": 0.33765680342912674, "max": 1.3196706771850586, "mean": -0.012772997841238976, "std": 0.4489719271659851}, "correlation_info": {}, "mutual_info": {}}, "X19": {"node_type": "child", "function_config": {"type": "random_neural_network", "parents": ["X5"], "hidden_dim": 2, "depth": 3, "activation": "tanh", "network_layers": 7, "layer_details": [{"type": "linear", "input_size": 1, "output_size": 2, "weights_shape": [2, 1], "has_bias": true, "weights": [[-0.6567242741584778], [-1.4106159210205078]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[0.9668601751327515, -0.5556180477142334], [0.049755632877349854, 0.6289545297622681]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 2, "weights_shape": [2, 2], "has_bias": true, "weights": [[-0.7983081340789795, 0.45053109526634216], [0.12157095968723297, -0.572995662689209]], "bias": [0.0, 0.0]}, {"type": "tanh"}, {"type": "linear", "input_size": 2, "output_size": 1, "weights_shape": [1, 2], "has_bias": true, "weights": [[1.2547857761383057, -0.22593949735164642]], "bias": [0.0]}], "f_function_activation": "tanh", "g_function": "identity", "detailed_weights": {"layer_0_weights": [[-0.6567242741584778], [-1.4106159210205078]], "layer_0_bias": [0.0, 0.0], "layer_2_weights": [[0.9668601751327515, -0.5556180477142334], [0.049755632877349854, 0.6289545297622681]], "layer_2_bias": [0.0, 0.0], "layer_4_weights": [[-0.7983081340789795, 0.45053109526634216], [0.12157095968723297, -0.572995662689209]], "layer_4_bias": [0.0, 0.0], "layer_6_weights": [[1.2547857761383057, -0.22593949735164642]], "layer_6_bias": [0.0]}}, "snr_info": {"target_snr": 2.0, "configured_noise_std": 0.03757087571175488, "actual_noise_std": 0.037698364690142795, "actual_snr": 1.9864956045469226, "signal_var": 0.0028231414034962654, "signal_mean": -0.001367515535093844, "config_noise_mean": null, "calculated_noise_mean_target": null, "actual_noise_mean": -0.000327177403960377}, "data_statistics": {"min": -0.21398109197616577, "q25": -0.04753952194005251, "median": -0.00036525633186101913, "q75": 0.04368939530104399, "max": 0.1922789216041565, "mean": -0.0016946931136772037, "std": 0.06381691992282867}, "correlation_info": {}, "mutual_info": {}}}, "model_results": {"ols": {"performance": {"r2_train": 0.7744996615273907, "r2_test": 0.7521408705302366, "r2_intervention": 0.729313320955847, "rmse_train": 0.03219219297170639, "rmse_test": 0.03405072167515755, "rmse_intervention": 0.035584207624197006, "mape_train": 127.8110146522522, "mape_test": 180.35272359848022, "mape_intervention": 176.6784906387329}, "feature_importance": {"permutation": {"parents": {"X18": 0.3730880280997882}, "children": {"X8": 0.001377950709089119, "X19": 0.4858477977315914}, "spouses": {"X14": 1.4383259399101492e-05, "X16": -0.00029521357979508017}, "others": {"X0": 1.2850377812956282e-05, "X1": -1.6280362610402815e-05, "X2": -0.0007140239134767098, "X3": 0.00030378200590130966, "X4": 7.453268637154971e-05, "X6": 0.0022762817127224277, "X7": 0.002507388713528217, "X9": -8.036874028631107e-05, "X10": 0.00044606495310980004, "X11": 0.0018944537887171837, "X12": 0.001208419111592085, "X13": 0.00019709888248102084, "X15": 0.007213429551939081, "X17": -0.0008907938730484677}}, "builtin": {"parents": {"X18": 0.0290945116430521}, "children": {"X8": 0.0018503878964111209, "X19": 0.03477958217263222}, "spouses": {"X14": 6.022118395776488e-05, "X16": 0.0005328729748725891}, "others": {"X0": 2.505545853637159e-05, "X1": 0.00010398188169347122, "X2": 0.0031941875349730253, "X3": 0.001378907822072506, "X4": 0.00022937980247661471, "X6": 0.0016800278099253774, "X7": 0.0015639406628906727, "X9": 0.0007103499956429005, "X10": 0.0003866181941702962, "X11": 0.0008759476477280259, "X12": 0.0032266099005937576, "X13": 0.000400938413804397, "X15": 0.002598110120743513, "X17": 0.00045169430086389184}}}}, "xgboost": {"performance": {"r2_train": 0.9998858696968703, "r2_test": 0.7191311733618421, "r2_intervention": 0.6318268932221756, "rmse_train": 0.0007242315332405269, "rmse_test": 0.03624729812145233, "rmse_intervention": 0.04150017350912094, "mape_train": 3.410893678665161, "mape_test": 165.9024953842163, "mape_intervention": 177.03465223312378}, "feature_importance": {"permutation": {"parents": {"X18": 0.5884425135397354}, "children": {"X8": 0.0016312734810118117, "X19": 0.35576548545996883}, "spouses": {"X14": -0.0010703320842276136, "X16": 0.0005794616802915487}, "others": {"X0": -0.005342879535184826, "X1": 0.0032320539179095573, "X2": -0.0045348015615389965, "X3": 0.0018263379754083209, "X4": 0.0028840554458583836, "X6": 0.00254758669950809, "X7": 0.010676471030928761, "X9": 0.0038466596791492114, "X10": 0.004277377731645086, "X11": 0.002018545404275337, "X12": 0.013640683930802497, "X13": 7.711392068417608e-06, "X15": 0.003494576865888092, "X17": -0.00022428627447150054}}, "builtin": {"parents": {"X18": 0.5605449676513672}, "children": {"X8": 0.013186724856495857, "X19": 0.26999107003211975}, "spouses": {"X14": 0.013386890292167664, "X16": 0.011468610726296902}, "others": {"X0": 0.006112838163971901, "X1": 0.005959481466561556, "X2": 0.01143526192754507, "X3": 0.008797510527074337, "X4": 0.010297444649040699, "X6": 0.008735784329473972, "X7": 0.015206963755190372, "X9": 0.011844615451991558, "X10": 0.010046462528407574, "X11": 0.009046235121786594, "X12": 0.00821773987263441, "X13": 0.005872637964785099, "X15": 0.010004560463130474, "X17": 0.009844187647104263}}}}, "lightgbm": {"performance": {"r2_train": 0.9912508595886301, "r2_test": 0.73002726441184, "r2_intervention": 0.7236212009731864, "rmse_train": 0.006341027794955216, "rmse_test": 0.035537250540793756, "rmse_intervention": 0.03595640248783852, "mape_train": 22.87464254869065, "mape_test": 192.85227366863032, "mape_intervention": 154.6941011233285}, "feature_importance": {"permutation": {"parents": {"X18": 0.6206280237396627}, "children": {"X8": 0.00014868562268917826, "X19": 0.37019173822879536}, "spouses": {"X14": -0.0009170425640563362, "X16": 0.008065014135187084}, "others": {"X0": 0.00046758474591342597, "X1": 0.0018870558154708468, "X2": -0.003216119126528231, "X3": 0.0034939231669891546, "X4": -0.0024430276689758243, "X6": 0.0008802427606547555, "X7": -0.0012924795892858738, "X9": -0.003293388882215235, "X10": 0.00653248431388187, "X11": 0.002603842059924958, "X12": 0.000856090334961442, "X13": -0.003117586217710263, "X15": 0.006621093681478933, "X17": -0.0013707949946962044}}, "builtin": {"parents": {"X18": 200}, "children": {"X8": 101, "X19": 201}, "spouses": {"X14": 125, "X16": 160}, "others": {"X0": 130, "X1": 109, "X2": 135, "X3": 151, "X4": 153, "X6": 102, "X7": 139, "X9": 95, "X10": 175, "X11": 140, "X12": 123, "X13": 118, "X15": 128, "X17": 129}}}}}}}}}}