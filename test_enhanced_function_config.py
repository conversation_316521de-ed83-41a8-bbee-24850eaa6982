#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的function_config功能
验证function_config是否包含父节点、具体参数等完整信息
"""

import json
import os
import numpy as np
import torch
from multi_data_generator import generate_datasets
from pall_pre_test import extract_serializable_scm_info, process_and_save_comprehensive_data

def test_enhanced_function_config():
    """测试增强的function_config"""
    print("=" * 60)
    print("测试增强的function_config")
    print("=" * 60)
    
    # 基础配置
    h_config = {
        'device': 'cpu',
        'min_num_node': 4,
        'max_num_node': 6,
        'num_layers': 2,
        'max_num_children': 3,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 1,
        'min_noise_std': 0.1,
        'max_noise_std': 0.2,
        'min_init_std': 0.5,
        'max_init_std': 1.0,
        'min_output_multiclass_ordered_p': 0.5,
        'max_output_multiclass_ordered_p': 0.8,
        'min_num_samples': 100,
        'max_num_samples': 100,
        'min_drop_node_ratio': 0.1,
        'max_drop_node_ratio': 0.2,
        'task': 'regression',
        'num_classes': 2,
        'model_type': 'ANM',
        'use_snr_method': True,
        'train_test_split_ratio': 0.7,
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'sample_root_std': False,
        'sample_cause_ranges': False,
        'use_monte_carlo_precompute': False
    }
    
    # 测试不同函数类型的配置
    custom_functions_configs = {
        'linear_test': {
            'name': 'Linear Function Test',
            'custom_functions': {
                'Y': {
                    'type': 'linear',
                    'target_snr': 8.0
                },
                'Ychild': {
                    'type': 'linear',
                    'target_snr': 6.0
                },
                'Other': {
                    'type': 'linear',
                    'target_snr': 4.0
                }
            }
        },
        'neural_test': {
            'name': 'Neural Network Test',
            'custom_functions': {
                'Y': {
                    'type': 'random_neural_network',
                    'hidden_dim': 3,
                    'depth': 2,
                    'activation': 'tanh',
                    'target_snr': 8.0
                },
                'Ychild': {
                    'type': 'random_neural_network',
                    'hidden_dim': 2,
                    'depth': 1,
                    'activation': 'relu',
                    'target_snr': 6.0
                },
                'Other': {
                    'type': 'random_neural_network',
                    'hidden_dim': 2,
                    'depth': 1,
                    'activation': 'sigmoid',
                    'target_snr': 4.0
                }
            }
        }
    }
    
    # 生成数据集并提取信息
    all_scm_objects = {}
    
    for config_key, config_info in custom_functions_configs.items():
        print(f"\n--- 测试配置: {config_key} ---")
        
        try:
            datasets = generate_datasets(
                num_dataset=1,
                h_config=h_config,
                perturbation_type='counterfactual',
                perturbation_node_type='single_child',
                perturbation_value_method='sample',
                custom_dag_type='random_SF',
                custom_functions=config_info['custom_functions'],
                custom_dag_size='small',
                node_unobserved=False,
                seed=42 + hash(config_key) % 1000,
                allow_skip=True
            )
            
            if not datasets:
                print(f"❌ 无法生成数据集（配置: {config_key}）")
                continue
            
            print(f"✅ 成功生成 {len(datasets)} 个数据集")
            
            # 提取SCM对象
            for i, dataset in enumerate(datasets):
                scm = dataset[5]  # SCM对象在索引5
                dataset_idx = f"{config_key}_{i}"
                
                if dataset_idx not in all_scm_objects:
                    all_scm_objects[dataset_idx] = {}
                
                all_scm_objects[dataset_idx][config_key] = scm
                
        except Exception as e:
            print(f"❌ 生成数据集失败（配置: {config_key}）: {e}")
            import traceback
            traceback.print_exc()
    
    if not all_scm_objects:
        print("❌ 没有成功生成任何数据集")
        return
    
    # 提取可序列化信息
    print("\n提取可序列化信息...")
    serializable_scm_objects = {}
    
    for dataset_idx, configs in all_scm_objects.items():
        serializable_scm_objects[dataset_idx] = {}
        for config_key, scm in configs.items():
            # 提取单个SCM的信息
            single_scm_dict = {0: scm}
            serializable_info = extract_serializable_scm_info(single_scm_dict)
            serializable_scm_objects[dataset_idx][config_key] = serializable_info[0]
    
    # 生成JSON输出
    print("生成JSON输出...")
    output_dir = "./test_enhanced_config_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        result = process_and_save_comprehensive_data(
            serializable_scm_objects, 
            [], [], [],  # 空的results, snr_results, importance_results
            output_dir,
            custom_functions_configs,
            save_format='json',
            generate_plots=False
        )
        
        # 读取生成的JSON文件
        json_path = os.path.join(output_dir, 'comprehensive_results.json')
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print("✅ JSON文件生成成功")
            
            # 检查增强的function_config
            print("\n=== 增强的function_config检查 ===")
            results = json_data.get('results', {})
            
            for config_name, config_data in results.items():
                print(f"\n配置: {config_name}")
                datasets = config_data.get('datasets', {})
                
                for dataset_name, dataset_data in datasets.items():
                    print(f"  数据集: {dataset_name}")
                    nodes = dataset_data.get('nodes', {})
                    
                    for node_name, node_data in nodes.items():
                        function_config = node_data.get('function_config', {})
                        if function_config and function_config.get('type') != None:  # 跳过根节点
                            print(f"    节点 {node_name}:")
                            print(f"      类型: {function_config.get('type', 'N/A')}")
                            print(f"      父节点: {function_config.get('parents', 'N/A')}")
                            
                            # 检查线性函数的具体参数
                            if function_config.get('type') == 'linear':
                                w = function_config.get('w', 'N/A')
                                b = function_config.get('b', 'N/A')
                                g_func = function_config.get('g_function', 'N/A')
                                print(f"      权重 w: {w}")
                                print(f"      偏置 b: {b}")
                                print(f"      激活函数 g: {g_func}")
                            
                            # 检查神经网络的具体参数
                            elif function_config.get('type') == 'random_neural_network':
                                layers = function_config.get('network_layers', 'N/A')
                                g_func = function_config.get('g_function', 'N/A')
                                print(f"      网络层数: {layers}")
                                print(f"      激活函数 g: {g_func}")
                                
                                # 显示层详情
                                layer_details = function_config.get('layer_details', [])
                                if layer_details:
                                    print(f"      层详情:")
                                    for i, layer in enumerate(layer_details[:2]):  # 只显示前2层
                                        print(f"        层{i}: {layer}")
                                
                                # 检查是否有详细权重
                                detailed_weights = function_config.get('detailed_weights', {})
                                if detailed_weights:
                                    print(f"      详细权重键: {list(detailed_weights.keys())}")
                            
                            # 检查其他函数类型的参数
                            else:
                                other_params = {k: v for k, v in function_config.items() 
                                              if k not in ['type', 'parents']}
                                if other_params:
                                    print(f"      其他参数: {other_params}")
            
            print(f"\n✅ 测试完成，详细结果保存在: {json_path}")
            
        else:
            print("❌ JSON文件未生成")
            
    except Exception as e:
        print(f"❌ 生成JSON输出失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试增强的function_config功能...")
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    test_enhanced_function_config()
    
    print("\n测试完成")

if __name__ == "__main__":
    main()
