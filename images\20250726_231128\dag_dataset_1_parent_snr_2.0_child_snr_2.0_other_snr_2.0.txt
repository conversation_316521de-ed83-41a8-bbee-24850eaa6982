parents: ['X1', 'X3', 'X10'] -> child: X0
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.169110052460046

parents: ['X18'] -> child: X5
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.039617589462281964

parents: ['X1'] -> child: X6
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.2641977787549642

parents: ['X18'] -> child: X7
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.5817211784498586

parents: ['X5', 'X14', 'X16'] -> child: X8
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.267105157890848

parents: ['X1', 'X2', 'X4', 'X11', 'X13', 'X17', 'X18'] -> child: X9
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.5017577210304115

parents: ['X1'] -> child: X12
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.4588750668491615

parents: ['X12'] -> child: X15
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.046166494519129936

parents: ['X5'] -> child: X19
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.03757087571175488