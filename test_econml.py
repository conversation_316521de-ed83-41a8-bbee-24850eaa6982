import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression, LogisticRegression # 导入 LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
# 导入所有需要验证的元学习器
from econml.metalearners import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, XLearner

from econml.dr import DRLearner
from econml.dml import LinearDML, CausalForestDML

# 尝试导入其他模型，如果未安装，请使用 pip install
try:
    from catboost import CatBoostRegressor, CatBoostClassifier # 导入 CatBoostClassifier
except ImportError:
    CatBoostRegressor = None
    CatBoostClassifier = None
    print("CatBoostRegressor/Classifier 未安装。请运行 'pip install catboost' 进行安装。")

try:
    from xgboost import XGBRegressor, XGBClassifier # 导入 XGBClassifier
except ImportError:
    XGBRegressor = None
    XGBClassifier = None
    print("XGBRegressor/Classifier 未安装。请运行 'pip install xgboost' 进行安装。")

try:
    from lightgbm import LGBMRegressor, LGBMClassifier # 导入 LGBMClassifier
except ImportError:
    LGBMRegressor = None
    LGBMClassifier = None
    print("LGBMRegressor/Classifier 未安装。请运行 'pip install lightgbm' 进行安装。")

try:
    from tabpfn import TabPFNRegressor, TabPFNClassifier
except ImportError:
    TabPFNRegressor = None
    TabPFNClassifier = None
    print("TabPFN未安装。请运行 'pip install tabpfn' 进行安装。")
    print("注意：TabPFN可能需要额外的配置或包装才能与 econml 完全兼容。")


def generate_synthetic_data(n_samples=1000, n_features=10):
    """
    生成用于因果推断的合成数据。
    X: 特征
    T: 治疗变量 (二元)
    Y: 结果变量
    """
    X = np.random.normal(0, 1, size=(n_samples, n_features))
    # 治疗变量 T 依赖于 X 的某些特征
    T = (X[:, 0] + X[:, 1] + np.random.normal(0, 0.5, n_samples) > 0).astype(int)
    # 结果变量 Y 依赖于 X、T 和因果效应
    # 真实因果效应 (CATE) 依赖于 X 的某些特征
    true_cate = X[:, 2] + np.sin(X[:, 3])
    Y = 2 * X[:, 0] + 3 * X[:, 1] + 5 * T + true_cate * T + np.random.normal(0, 1, n_samples)
    return X, T, Y, true_cate

# 生成数据
X, T, Y, true_cate = generate_synthetic_data(n_samples=1000, n_features=10)


feature_names = [f'X{i}' for i in range(X.shape[1])]
X_df = pd.DataFrame(X, columns=feature_names)
T_df = pd.Series(T, name='T')
Y_df = pd.Series(Y, name='Y')

print("--- 开始验证 EconML 基学习器兼容性 ---")

# 定义要测试的回归模型及其名称
models_to_test = {
    "OLS (LinearRegression)": {
        "regressor": LinearRegression(),
        "classifier": LogisticRegression(solver='liblinear')
    },
    "CatBoost": {
        "regressor": CatBoostRegressor(verbose=0) if CatBoostRegressor else None,
        "classifier": CatBoostClassifier(verbose=0) if CatBoostClassifier else None
    },
    "XGBoost": {
        "regressor": XGBRegressor(eval_metric='rmse') if XGBRegressor else None,
        "classifier": XGBClassifier(eval_metric='logloss') if XGBClassifier else None
    },
    "LightGBM": {
        "regressor": LGBMRegressor(verbose=0) if LGBMRegressor else None,
        "classifier": LGBMClassifier(verbose=0) if LGBMClassifier else None
    },
    "TabPFN": {
        "regressor": TabPFNRegressor(device='cuda', model_path = '/root/LDM_test/tabpfn-v2-regressor.ckpt') if TabPFNRegressor else None,
        "classifier": TabPFNClassifier(device='cuda', model_path = '/root/LDM_test/tabpfn-v2-classifier.ckpt') if TabPFNClassifier else None
    }
}

# 遍历每个模型并尝试将其作为 TLearner 的基础学习器
for model_name, model_dict in models_to_test.items():
    regressor = model_dict["regressor"]
    if regressor is None:
        print(f"\n跳过 {model_name}，因为回归器未安装或无法实例化。")
        continue

    print(f"\n--- 正在测试 {model_name} 作为 TLearner 的基学习器 ---")
    try:
        # TLearner 需要两个模型：一个用于控制组，一个用于治疗组
        # 这里使用相同的模型实例作为两个估计器,实际可以使用不一样的模型
        learner = TLearner(
            models=[regressor, regressor]
        )

        # 拟合元学习器
        # econml 通常要求 X, T, Y 是 numpy 数组或 pandas DataFrame/Series
        learner.fit(Y_df, T_df, X=X_df)

        # 估计因果效应
        # effect 方法返回每个样本的异质治疗效应 (HTE)
        ate = learner.effect(X_df) # 对于 TLearner, effect 返回 CATE

        print(f"{model_name} 成功用作 TLearner 的基学习器。")
        print(f"估计的平均治疗效应 (ATE) 示例 (前5个): {ate[:5]}")

    except Exception as e:
        print(f"使用 {model_name} 作为 TLearner 的基学习器时发生错误: {e}")
        print(f"这可能意味着 {model_name} 不完全兼容 EconML 的预期接口，或者需要额外的包装。")

print("\n--- 正在测试 SLearner 的兼容性 ---")
# SLearner 需要一个模型来估计 E[Y|X,T]
for model_name, model_dict in models_to_test.items():
    regressor = model_dict["regressor"]
    if regressor is None:
        print(f"\n跳过 {model_name}，因为回归器未安装或无法实例化。")
        continue

    print(f"\n--- 正在测试 {model_name} 作为 SLearner 的基学习器 ---")
    try:
        # SLearner 只需要一个模型
        s_learner = SLearner(
            overall_model=regressor
        )

        s_learner.fit(Y_df, T_df, X=X_df)
        ate_s = s_learner.effect(X_df)

        print(f"{model_name} 成功用作 SLearner 的基学习器。")
        print(f"估计的平均治疗效应 (ATE) 示例 (前5个): {ate_s[:5]}")

    except Exception as e:
        print(f"使用 {model_name} 作为 SLearner 的基学习器时发生错误: {e}")
        print(f"这可能意味着 {model_name} 不完全兼容 EconML 的预期接口，或者需要额外的包装。")

print("\n--- 正在测试 XLearner 的兼容性 ---")
# XLearner 需要多个模型：回归模型用于结果估计，分类模型用于倾向性得分
for model_name, model_dict in models_to_test.items():
    regressor = model_dict["regressor"]
    classifier = model_dict["classifier"]
    
    if regressor is None or classifier is None:
        print(f"\n跳过 {model_name}，因为回归器或分类器未安装或无法实例化。")
        continue

    print(f"\n--- 正在测试 {model_name} 作为 XLearner 的基学习器 ---")
    try:
        # XLearner 需要回归模型来估计 E[Y|X, T] 和 CATE，以及一个分类模型来估计倾向性得分
        x_learner = XLearner(
            models=[regressor, regressor], # 估计 E[Y|X, T=0] 和 E[Y|X, T=1]
            cate_models=[regressor, regressor], # 估计 CATE
            propensity_model=classifier # 使用对应的分类器作为倾向性模型
        )

        x_learner.fit(Y_df, T_df, X=X_df)
        ate_x = x_learner.effect(X_df)

        print(f"{model_name} 成功用作 XLearner 的基学习器。")
        print(f"估计的平均治疗效应 (ATE) 示例 (前5个): {ate_x[:5]}")

    except Exception as e:
        print(f"使用 {model_name} 作为 XLearner 的基学习器时发生错误: {e}")
        print(f"这可能意味着 {model_name} 不完全兼容 EconML 的预期接口，或者需要额外的包装。")


print("\n--- 正在测试 DRLearner 的兼容性 ---")
# DRLearner 需要两个模型：一个用于回归，一个用于倾向性得分
for model_name, model_dict in models_to_test.items():
    regressor = model_dict["regressor"]
    classifier = model_dict["classifier"]
    
    if regressor is None or classifier is None:
        print(f"\n跳过 {model_name}，因为回归器或分类器未安装或无法实例化。")
        continue

    print(f"\n--- 正在测试 {model_name} 作为 DRLearner 的基学习器 ---")
    try:
        # DRLearner 需要一个回归模型来估计 E[Y|X,T] 和一个分类模型来估计 P[T|X]
        dr_learner = DRLearner(
            model_regression=regressor, # 估计 E[Y|X,T]
            model_propensity=classifier, # 使用对应的分类器作为倾向性模型
            model_final=regressor
        )

        dr_learner.fit(Y_df, T_df, X=X_df)
        ate_dr = dr_learner.effect(X_df)

        print(f"{model_name} 成功用作 DRLearner 的基学习器。")
        print(f"估计的平均治疗效应 (ATE) 示例 (前5个): {ate_dr[:5]}")

    except Exception as e:
        print(f"使用 {model_name} 作为 DRLearner 的基学习器时发生错误: {e}")
        print(f"这可能意味着 {model_name} 不完全兼容 EconML 的预期接口，或者需要额外的包装。")


print("\n--- 正在测试 LinearDML 的兼容性 ---")
# LinearDML 需要两个模型：一个用于 Y，一个用于 T
# 注意：对于二元治疗变量，T的预测可以使用回归器或分类器，取决于具体实现
for model_name, model_dict in models_to_test.items():
    regressor = model_dict["regressor"]
    classifier = model_dict["classifier"]
    
    if regressor is None:
        print(f"\n跳过 {model_name}，因为回归器未安装或无法实例化。")
        continue

    print(f"\n--- 正在测试 {model_name} 作为 LinearDML 的基学习器 ---")
    try:
        # 首先尝试使用回归器作为 model_t（适用于连续治疗变量的情况）
        linear_dml = LinearDML(
            model_y=regressor,  # 用回归器预测连续结果变量Y
            model_t=regressor   # 对于二元治疗变量，也可以用回归器预测概率
        )

        linear_dml.fit(Y_df, T_df, X=X_df)
        ate_ldml = linear_dml.effect(X_df)

        print(f"{model_name} 成功用作 LinearDML 的基学习器（使用回归器预测T）。")
        print(f"估计的平均治疗效应 (ATE) 示例 (前5个): {ate_ldml[:5]}")

    except Exception as e1:
        print(f"使用回归器预测T时发生错误: {e1}")
        
        # 如果回归器失败，尝试使用分类器（但根据错误信息，这可能不适用于连续目标）
        if classifier is not None:
            try:
                print(f"尝试使用分类器作为 model_t...")
                linear_dml_clf = LinearDML(
                    model_y=regressor,   # 用回归器预测连续结果变量Y
                    model_t=classifier   # 用分类器预测二元治疗变量T
                )

                linear_dml_clf.fit(Y_df, T_df, X=X_df)
                ate_ldml_clf = linear_dml_clf.effect(X_df)

                print(f"{model_name} 成功用作 LinearDML 的基学习器（使用分类器预测T）。")
                print(f"估计的平均治疗效应 (ATE) 示例 (前5个): {ate_ldml_clf[:5]}")

            except Exception as e2:
                print(f"使用分类器预测T时也发生错误: {e2}")
                print(f"这可能意味着 {model_name} 不完全兼容 EconML 的 LinearDML 接口。")
        else:
            print(f"没有可用的分类器进行进一步测试。")

print("\n--- 正在测试 CausalForestDML 的兼容性 ---")
# CausalForestDML 需要两个模型：一个用于 Y，一个用于 T
for model_name, model_dict in models_to_test.items():
    regressor = model_dict["regressor"]
    
    if regressor is None:
        print(f"\n跳过 {model_name}，因为回归器未安装或无法实例化。")
        continue

    print(f"\n--- 正在测试 {model_name} 作为 CausalForestDML 的基学习器 ---")
    try:
        # CausalForestDML 使用随机森林作为最终阶段，但需要第一阶段模型
        cf_dml = CausalForestDML(
            model_y=regressor,  # 第一阶段：预测Y
            model_t=regressor,  # 第一阶段：预测T
            random_state=42
        )

        cf_dml.fit(Y_df, T_df, X=X_df)
        ate_cf = cf_dml.effect(X_df)

        print(f"{model_name} 成功用作 CausalForestDML 的基学习器。")
        print(f"估计的平均治疗效应 (ATE) 示例 (前5个): {ate_cf[:5]}")

    except Exception as e:
        print(f"使用 {model_name} 作为 CausalForestDML 的基学习器时发生错误: {e}")
        print(f"这可能意味着 {model_name} 不完全兼容 EconML 的预期接口，或者需要额外的包装。")

print("\n--- 验证完成 ---")
