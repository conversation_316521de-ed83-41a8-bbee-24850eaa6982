#!/usr/bin/env python3
"""
测试保存数据集时包含节点关系数据的功能
验证 JSON 文件的正确保存
"""

import os
import sys
import json
import traceback

def test_save_with_relationships():
    """测试保存数据集时包含节点关系数据"""
    try:
        print("=" * 60)
        print("测试保存数据集时包含节点关系数据")
        print("=" * 60)
        
        # 导入必要的模块
        from scm_data_provider import DataGenerationConfig, DataProvider, SNRConfig, NodeFunctionConfigs, FunctionConfig
        import torch
        
        print("✓ 成功导入所有模块")
        
        # 创建基础配置
        print("\n1. 创建基础配置...")
        h_config = {
            'device': 'cpu',
            'min_noise_std': 0.01,
            'max_noise_std': 0.1,
            'min_init_std': 1,
            'max_init_std': 5,
            'root_distribution': 'gaussian',
            'root_mean': 0.0,
            'root_std': 1.0,
            'sample_root_std': False,
            'min_root': 0.0,
            'max_root': 1.0,
            'max_range': 0.5,
            'sample_cause_ranges': False,
            'sample_std': False,
            'min_num_samples': 50,  # 减少样本数量以加快测试
            'max_num_samples': 50,
            'train_test_split_ratio': 0.7,
            'task': 'regression',
            'min_output_multiclass_ordered_p': 0.0,
            'max_output_multiclass_ordered_p': 0.5,
            'categorical_feature_p': 0,
        }
        
        # 创建简单的SNR配置
        snr_config = SNRConfig(
            parent_snr_values=[2.0],  # 只使用一个SNR值以加快测试
            child_snr_multipliers=[1.0],
            other_snr_multipliers=[1.0]
        )
        
        # 创建简单的函数配置
        node_function_configs = NodeFunctionConfigs(
            target_config=FunctionConfig(function_type='linear'),
            target_child_config=FunctionConfig(function_type='polynomial'),
            other_config=FunctionConfig(function_type='mlp')
        )
        
        # 创建数据配置
        data_config = DataGenerationConfig(
            n_datasets=2,  # 生成2个数据集以测试
            dataset_type='standard',  # 使用标准数据集类型
            node_unobserved=False,
            seed=42,
            allow_skip=True,
            custom_dag_type='random_SF',
            custom_dag_size='small',
            avg_in_degree=(1.2, 1.7),
            device='cpu',
            snr_config=snr_config,
            node_function_configs=node_function_configs
        )
        print("✓ 配置创建成功")
        
        # 创建数据提供器并生成数据集
        print("\n2. 生成数据集...")
        provider = DataProvider(h_config, data_config)
        datasets = provider.generate_datasets()
        print(f"✓ 成功生成 {len(datasets)} 个配置的数据集")
        
        # 清理之前的输出目录
        import shutil
        if os.path.exists('data_output'):
            shutil.rmtree('data_output')
        
        # 获取数据分割和节点关系数据
        print("\n3. 获取数据分割和节点关系数据...")
        data_splits = provider.get_dataset_splits()
        all_relationships = provider.get_node_relationships_data()
        print(f"  获取了 {len(data_splits)} 个配置的数据分割")
        print(f"  获取了 {len(all_relationships)} 个配置的节点关系数据")
        
        # 测试不包含节点关系数据的保存
        print("\n4. 测试不包含节点关系数据的保存...")
        saved_paths_without_rel = provider.save_dataset_to_csv(data_splits=data_splits)
        print(f"  ✓ 成功保存 {len(saved_paths_without_rel)} 个配置的数据集（不含节点关系）")
        
        # 验证不包含节点关系文件
        has_relationship_files = False
        for config_key, config_paths in saved_paths_without_rel.items():
            for dataset_paths in config_paths:
                if 'relationships_path' in dataset_paths:
                    has_relationship_files = True
                    break
        
        if not has_relationship_files:
            print("  ✓ 确认不包含节点关系文件")
        else:
            print("  ✗ 意外包含了节点关系文件")
            return False
        
        # 清理输出目录
        if os.path.exists('data_output'):
            shutil.rmtree('data_output')
        
        # 测试包含节点关系数据的保存
        print("\n5. 测试包含节点关系数据的保存...")
        saved_paths_with_rel = provider.save_dataset_to_csv(
            data_splits=data_splits, 
            node_relationships=all_relationships
        )
        print(f"  ✓ 成功保存 {len(saved_paths_with_rel)} 个配置的数据集（含节点关系）")
        
        # 验证包含节点关系文件
        relationship_files_count = 0
        for config_key, config_paths in saved_paths_with_rel.items():
            for dataset_paths in config_paths:
                if 'relationships_path' in dataset_paths:
                    relationship_files_count += 1
                    rel_path = dataset_paths['relationships_path']
                    
                    # 验证文件存在
                    if not os.path.exists(rel_path):
                        print(f"  ✗ 节点关系文件不存在: {rel_path}")
                        return False
                    
                    # 验证JSON文件内容
                    try:
                        with open(rel_path, 'r', encoding='utf-8') as f:
                            rel_data = json.load(f)
                        
                        # 验证JSON结构
                        required_keys = ['target_node', 'relationships', 'markov_blanket', 'dag_info']
                        for key in required_keys:
                            if key not in rel_data:
                                print(f"  ✗ 节点关系JSON缺少键: {key}")
                                return False
                        
                        # 验证relationships结构
                        rel_keys = ['parents', 'children', 'spouses', 'others']
                        for key in rel_keys:
                            if key not in rel_data['relationships']:
                                print(f"  ✗ relationships缺少键: {key}")
                                return False
                            if not isinstance(rel_data['relationships'][key], list):
                                print(f"  ✗ relationships[{key}]不是列表类型")
                                return False
                        
                        print(f"    ✓ 节点关系文件验证通过: {os.path.basename(rel_path)}")
                        print(f"      目标节点: {rel_data['target_node']}")
                        print(f"      马尔可夫毯大小: {len(rel_data['markov_blanket'])}")
                        
                    except json.JSONDecodeError as e:
                        print(f"  ✗ JSON文件格式错误: {e}")
                        return False
        
        print(f"  ✓ 总共保存了 {relationship_files_count} 个节点关系JSON文件")
        
        # 验证文件结构
        print("\n6. 验证文件结构...")
        if os.path.exists('data_output'):
            timestamp_dirs = [d for d in os.listdir('data_output') if os.path.isdir(os.path.join('data_output', d))]
            if timestamp_dirs:
                timestamp_dir = timestamp_dirs[0]
                config_dirs = [d for d in os.listdir(os.path.join('data_output', timestamp_dir)) 
                              if os.path.isdir(os.path.join('data_output', timestamp_dir, d))]
                
                print(f"  ✓ 目录结构: data_output/{timestamp_dir}/ 包含 {len(config_dirs)} 个配置目录")
                
                # 检查每个配置目录的文件
                for config_dir in config_dirs:
                    config_path = os.path.join('data_output', timestamp_dir, config_dir)
                    files = os.listdir(config_path)
                    csv_files = [f for f in files if f.endswith('.csv')]
                    json_files = [f for f in files if f.endswith('.json')]
                    png_files = [f for f in files if f.endswith('.png')]
                    
                    print(f"    配置 {config_dir}: {len(csv_files)} CSV, {len(json_files)} JSON, {len(png_files)} PNG")
            else:
                print("  ✗ 未找到时间戳目录")
                return False
        else:
            print("  ✗ 未找到 data_output 目录")
            return False
        
        print("\n✓ 所有节点关系数据保存测试通过！")
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_save_with_relationships()
    if success:
        print(f"\n{'='*60}")
        print("🎉 节点关系数据保存功能测试成功！")
        print("✅ save_dataset_to_csv 现在支持保存节点关系数据")
        print("✅ 节点关系数据保存为 JSON 格式")
        print("✅ JSON 文件包含完整的节点关系信息:")
        print("   - target_node: 目标节点")
        print("   - relationships: 父节点、子节点、配偶节点、其他节点")
        print("   - markov_blanket: 马尔可夫毯")
        print("   - dag_info: DAG图信息")
        print("📁 文件保存在: data_output/时间戳/配置名称/数据集名称_node_relationships.json")
        print(f"{'='*60}")
        sys.exit(0)
    else:
        print(f"\n{'='*60}")
        print("❌ 测试失败，请检查错误信息")
        print(f"{'='*60}")
        sys.exit(1)
