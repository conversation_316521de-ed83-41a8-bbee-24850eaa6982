parents: ['X9'] -> child: X3
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.013167492871638124

parents: ['X0', 'X2', 'X4', 'X5', 'X8'] -> child: X6
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.11212060187611693

parents: ['X7'] -> child: X8
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.036174508471641054

parents: ['X0'] -> child: X9
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.7776651149472114

parents: ['X7'] -> child: X10
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.5847454661307312

parents: ['X0', 'X1'] -> child: X11
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.8886472258450514