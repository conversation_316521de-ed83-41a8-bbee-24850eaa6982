import numpy as np
import pandas as pd
import xgboost as xgb
import concurrent.futures
import torch
import os
import threading
import time
import datetime
from scipy import stats
import numpy as np
import gc
import traceback

from tabpfn import TabPFNRegressor
from data_generator import generate_datasets, save_datasets_to_csv
from sklearn.metrics import mean_squared_error, r2_score, explained_variance_score, mean_absolute_error, mean_absolute_percentage_error
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler


def calculate_zscore_params(X, y):
    """
    计算特征和标签的均值和标准差，用于Z分数计算

    参数:
    X: 特征数据
    y: 标签数据

    返回:
    特征的均值和标准差，以及标签的均值和标准差
    """
    # 计算特征的均值和标准差
    X_mean = np.mean(X, axis=0)
    X_std = np.std(X, axis=0, ddof=1)  # 使用无偏估计

    # 计算标签的均值和标准差
    y_mean = np.mean(y)
    y_std = np.std(y, ddof=1)  # 使用无偏估计

    return X_mean, X_std, y_mean, y_std


def remove_outliers(X, y, X_mean=None, X_std=None, y_mean=None, y_std=None, threshold=3.0):
    """
    移除超出指定标准差范围的异常值

    参数:
    X: 特征数据
    y: 标签数据
    X_mean: 特征均值，如果为None则从X计算
    X_std: 特征标准差，如果为None则从X计算
    y_mean: 标签均值，如果为None则从y计算
    y_std: 标签标准差，如果为None则从y计算
    threshold: 标准差阈值，默认为3.0

    返回:
    清理后的X和y数据，以及被移除的样本数量，以及计算的统计参数（如果之前为None）
    """
    # 初始样本数
    initial_samples = X.shape[0]

    # 如果没有提供统计参数，则从当前数据计算
    if X_mean is None or X_std is None or y_mean is None or y_std is None:
        X_mean, X_std, y_mean, y_std = calculate_zscore_params(X, y)

    # 计算特征的Z分数
    # 避免除以零（如果某个特征的标准差为0）
    X_std_safe = np.where(X_std == 0, 1e-10, X_std)
    z_scores = np.abs((X - X_mean) / X_std_safe)

    # 找出所有特征的Z分数都在阈值范围内的样本
    mask = np.all(z_scores < threshold, axis=1)

    # 计算标签的Z分数
    # 避免除以零
    y_std_safe = y_std if y_std != 0 else 1e-10
    y_z_scores = np.abs((y - y_mean) / y_std_safe)
    y_mask = y_z_scores < threshold

    # 合并两个掩码，只保留特征和标签都不是异常值的样本
    final_mask = mask & y_mask

    # 应用掩码筛选数据
    X_clean = X[final_mask]
    y_clean = y[final_mask]

    # 计算移除的样本数量
    removed_samples = initial_samples - X_clean.shape[0]

    return X_clean, y_clean, removed_samples, X_mean, X_std, y_mean, y_std


def normalize_data(train_xs, train_ys, test_xs_list, test_ys_list):
    """
    使用训练集的均值和方差来标准化训练集和测试集的X和Y

    参数:
    train_xs: 训练集特征
    train_ys: 训练集标签
    test_xs_list: 测试集特征列表（可以包含多个测试集）
    test_ys_list: 测试集标签列表（可以包含多个测试集）

    返回:
    标准化后的训练集和测试集数据，以及用于逆变换的scaler对象
    """
    # 初始化特征和标签的标准化器
    x_scaler = StandardScaler()
    y_scaler = StandardScaler()

    # 使用训练集拟合并转换
    train_xs_scaled = x_scaler.fit_transform(train_xs)

    # 对标签进行标准化（对于回归问题）
    # 注意：需要将一维数组重塑为二维数组以便于StandardScaler处理
    train_ys_reshaped = train_ys.reshape(-1, 1)
    train_ys_scaled = y_scaler.fit_transform(train_ys_reshaped).flatten()

    # 对测试集应用相同的转换
    test_xs_scaled_list = []
    test_ys_scaled_list = []

    for test_xs, test_ys in zip(test_xs_list, test_ys_list):
        test_xs_scaled = x_scaler.transform(test_xs)
        test_ys_scaled = y_scaler.transform(test_ys.reshape(-1, 1)).flatten()

        test_xs_scaled_list.append(test_xs_scaled)
        test_ys_scaled_list.append(test_ys_scaled)

    return train_xs_scaled, train_ys_scaled, test_xs_scaled_list, test_ys_scaled_list, x_scaler, y_scaler


def inverse_transform_predictions(predictions, y_scaler):
    """
    将标准化后的预测结果转换回原始尺度

    参数:
    predictions: 模型的预测结果（标准化尺度）
    y_scaler: 用于标签的标准化器

    返回:
    原始尺度的预测结果
    """
    # 重塑为二维数组以便于StandardScaler处理
    predictions_reshaped = predictions.reshape(-1, 1)
    # 应用逆变换
    predictions_original_scale = y_scaler.inverse_transform(predictions_reshaped).flatten()
    return predictions_original_scale


def train_tabpfn(train_xs, train_ys, test_xs_original, test_xs_intervention, y_scaler=None, gpu_id=0):
    """训练TabPFN模型并返回预测结果，优化GPU资源使用"""
    tabpfn_model = None  # Initialize
    try:
        # 设置当前线程使用的GPU
        if torch.cuda.is_available():
            device = f'cuda:{gpu_id}'
            torch.cuda.set_device(device)  # 确保此线程使用分配的GPU
        else:
            device = 'cpu'

        print(f"TabPFN模型将在设备 {device} 上训练")

        # 确保在创建模型前清理GPU内存
        if torch.cuda.is_available():
            with torch.cuda.device(device):
                torch.cuda.empty_cache()
                gc.collect()

        # 创建TabPFN模型
        tabpfn_model = TabPFNRegressor(device=device)
        tabpfn_model.fit(train_xs, train_ys)

        # 分批次进行预测，避免内存溢出
        def predict_in_batches(model, X, batch_size=1024):
            predictions = []
            for i in range(0, len(X), batch_size):
                batch = X[i:i + batch_size]
                pred = model.predict(batch)
                predictions.append(pred)
                # 每个批次后清理内存
                if torch.cuda.is_available():
                    with torch.cuda.device(device):
                        torch.cuda.empty_cache()
            return np.concatenate(predictions)

        # TabPFN预测
        tabpfn_pred_original = predict_in_batches(tabpfn_model, test_xs_original)
        tabpfn_pred_intervention = predict_in_batches(tabpfn_model, test_xs_intervention)

        # 如果提供了y_scaler，则将预测结果转换回原始尺度
        if y_scaler is not None:
            tabpfn_pred_original = inverse_transform_predictions(tabpfn_pred_original, y_scaler)
            tabpfn_pred_intervention = inverse_transform_predictions(tabpfn_pred_intervention, y_scaler)

        return tabpfn_pred_original, tabpfn_pred_intervention

    except Exception as e:
        print(f"TabPFN训练或预测时出错 on device {device}: {str(e)}")
        traceback.print_exc()
        raise e
    finally:
        if tabpfn_model is not None:
            del tabpfn_model
            print(f"已删除TabPFN模型 on device {device}")
        if torch.cuda.is_available():
            # 确保在正确的设备上下文中清理
            with torch.cuda.device(device):
                torch.cuda.empty_cache()
        gc.collect()


def train_xgboost(train_xs, train_ys, test_xs_original, test_xs_intervention, y_scaler=None):
    """训练XGBoost模型并返回预测结果"""
    try:
        # 设置XGBoost参数，避免使用过多内存
        xgb_model = xgb.XGBRegressor(
            objective='reg:squarederror',
            random_state=42,
            tree_method='hist', # 使用直方图方法，更节省内存
            max_depth=6,  # 限制树的深度
            n_estimators=100  # 限制树的数量
        )
        xgb_model.fit(train_xs, train_ys)

        # XGBoost预测
        xgb_pred_original = xgb_model.predict(test_xs_original)
        xgb_pred_intervention = xgb_model.predict(test_xs_intervention)

        # 如果提供了y_scaler，则将预测结果转换回原始尺度
        if y_scaler is not None:
            xgb_pred_original = inverse_transform_predictions(xgb_pred_original, y_scaler)
            xgb_pred_intervention = inverse_transform_predictions(xgb_pred_intervention, y_scaler)

        return xgb_pred_original, xgb_pred_intervention

    except Exception as e:
        print(f"XGBoost训练或预测时出错: {str(e)}")
        traceback.print_exc()
        raise e


def evaluate_dataset(dataset, normalize=False, remove_outliers_flag=False, outlier_threshold=3.0, gpu_id=0):
    """评估单个数据集，TabPFN和XGBoost并行训练"""
    try:
        # 确保在开始前清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

        # 提取单个数据集的信息
        dataset_name = dataset[0]
        print(f"\n开始处理数据集: {dataset_name}")

        # 将数据移动到CPU并转换为numpy数组
        xs_original, ys_original = dataset[1].clone().cpu(), dataset[2].clone().cpu()
        xs_intervention, ys_intervention = dataset[3].clone().cpu(), dataset[4].clone().cpu()

        # 释放原始数据
        dataset = None
        gc.collect()

        # 转换为numpy数组以便于处理
        xs_original_np = xs_original.numpy()
        ys_original_np = ys_original.numpy()
        xs_intervention_np = xs_intervention.numpy()
        ys_intervention_np = ys_intervention.numpy()

        # 释放PyTorch张量
        xs_original = ys_original = xs_intervention = ys_intervention = None
        gc.collect()

        # 分割数据
        eval_position = int(xs_original_np.shape[0] * 0.5)
        train_xs, train_ys = xs_original_np[0:eval_position], ys_original_np[0:eval_position]
        test_xs_original, test_ys_original = xs_original_np[eval_position:], ys_original_np[eval_position:]
        test_xs_intervention, test_ys_intervention = xs_intervention_np[eval_position:], ys_intervention_np[
                                                                                         eval_position:]

        # 释放原始numpy数组
        xs_original_np = ys_original_np = xs_intervention_np = ys_intervention_np = None
        gc.collect()

        # 记录原始样本数量
        original_train_samples = train_xs.shape[0]
        original_test_original_samples = test_xs_original.shape[0]
        original_test_intervention_samples = test_xs_intervention.shape[0]

        # 异常值处理
        removed_train_samples = 0
        removed_test_original_samples = 0
        removed_test_intervention_samples = 0

        if remove_outliers_flag:
            print(f"开始异常值处理，阈值: {outlier_threshold} 标准差")
            # 首先处理训练集并获取Z分数参数
            train_xs, train_ys, removed_train_samples, X_mean, X_std, y_mean, y_std = remove_outliers(
                train_xs, train_ys, threshold=outlier_threshold
            )

            # 使用训练集的Z分数参数处理测试集
            test_xs_original, test_ys_original, removed_test_original_samples, _, _, _, _ = remove_outliers(
                test_xs_original, test_ys_original,
                X_mean, X_std, y_mean, y_std,
                threshold=outlier_threshold
            )

            test_xs_intervention, test_ys_intervention, removed_test_intervention_samples, _, _, _, _ = remove_outliers(
                test_xs_intervention, test_ys_intervention,
                X_mean, X_std, y_mean, y_std,
                threshold=outlier_threshold
            )

            print(f"从训练集中移除了 {removed_train_samples} 个异常值样本")
            print(f"从原始测试集中移除了 {removed_test_original_samples} 个异常值样本")
            print(f"从干预测试集中移除了 {removed_test_intervention_samples} 个异常值样本")

        # 标准化处理
        x_scaler = None
        y_scaler = None

        if normalize:
            print("开始数据标准化")
            test_xs_list = [test_xs_original, test_xs_intervention]
            test_ys_list = [test_ys_original, test_ys_intervention]

            train_xs, train_ys, test_xs_scaled_list, test_ys_scaled_list, x_scaler, y_scaler = normalize_data(
                train_xs, train_ys, test_xs_list, test_ys_list
            )

            test_xs_original, test_xs_intervention = test_xs_scaled_list
            test_ys_original, test_ys_intervention = test_ys_scaled_list

        # 获取数据集信息
        n_features = train_xs.shape[1]
        n_samples = original_test_original_samples

        # 处理后的样本数
        processed_train_samples = train_xs.shape[0]
        processed_test_original_samples = test_xs_original.shape[0]
        processed_test_intervention_samples = test_xs_intervention.shape[0]

        print(f"数据集特征数: {n_features}")
        print(f"处理后训练样本数: {processed_train_samples}")
        print(f"处理后原始测试集样本数: {processed_test_original_samples}")
        print(f"处理后干预测试集样本数: {processed_test_intervention_samples}")

        # 检查是否有足够的样本进行评估
        if processed_train_samples < 10 or processed_test_original_samples < 10 or processed_test_intervention_samples < 10:
            print(f"警告: 数据集 {dataset_name} 在处理后样本数过少，跳过评估")
            return {
                'dataset_name': dataset_name,
                'n_samples': n_samples,
                'n_features': n_features,
                'original_train_samples': original_train_samples,
                'processed_train_samples': processed_train_samples,
                'original_test_original_samples': original_test_original_samples,
                'processed_test_original_samples': processed_test_original_samples,
                'original_test_intervention_samples': original_test_intervention_samples,
                'processed_test_intervention_samples': processed_test_intervention_samples,
                'removed_train_outliers': removed_train_samples,
                'removed_test_original_outliers': removed_test_original_samples,
                'removed_test_intervention_outliers': removed_test_intervention_samples,
                'normalized': normalize,
                'outliers_removed': remove_outliers_flag,
                'outlier_threshold': outlier_threshold if remove_outliers_flag else None,
                'error': '处理后样本数过少',
                'tabpfn_rmse_original': None,
                'tabpfn_r2_original': None,
                'tabpfn_r2ad_original': None,
                'tabpfn_evs_original': None,
                'tabpfn_mae_original': None,
                'tabpfn_mape_original': None,
                'xgb_rmse_original': None,
                'xgb_r2_original': None,
                'xgb_r2ad_original': None,
                'xgb_evs_original': None,
                'xgb_mae_original': None,
                'xgb_mape_original': None,
                'tabpfn_rmse_intervention': None,
                'tabpfn_r2_intervention': None,
                'tabpfn_r2ad_intervention': None,
                'tabpfn_evs_intervention': None,
                'tabpfn_mae_intervention': None,
                'tabpfn_mape_intervention': None,
                'xgb_rmse_intervention': None,
                'xgb_r2_intervention': None,
                'xgb_r2ad_intervention': None,
                'xgb_evs_intervention': None,
                'xgb_mae_intervention': None,
                'xgb_mape_intervention': None,
            }

        # 顺序训练模型以避免内存问题
        try:
            print("开始训练TabPFN模型")
            tabpfn_pred_original, tabpfn_pred_intervention = train_tabpfn(
                train_xs, train_ys,
                test_xs_original, test_xs_intervention,
                y_scaler if normalize else None,
                gpu_id
            )

            # 计算TabPFN指标
            tabpfn_rmse_original = np.sqrt(mean_squared_error(test_ys_original, tabpfn_pred_original))
            tabpfn_r2_original = r2_score(test_ys_original, tabpfn_pred_original)
            # Correct n_samples for adjusted R2 calculation
            n_eval_original = test_ys_original.shape[0]
            if n_eval_original > n_features + 1:
                tabpfn_r2ad_original = 1 - (1 - tabpfn_r2_original) * (n_eval_original - 1) / (n_eval_original - n_features - 1)
            else:
                tabpfn_r2ad_original = np.nan # Not enough samples or features for adjusted R2

            tabpfn_evs_original = explained_variance_score(test_ys_original, tabpfn_pred_original)
            tabpfn_mae_original = mean_absolute_error(test_ys_original, tabpfn_pred_original)
            tabpfn_mape_original = mean_absolute_percentage_error(test_ys_original, tabpfn_pred_original)

            tabpfn_rmse_intervention = np.sqrt(mean_squared_error(test_ys_intervention, tabpfn_pred_intervention))
            tabpfn_r2_intervention = r2_score(test_ys_intervention, tabpfn_pred_intervention)
            # Correct n_samples for adjusted R2 calculation
            n_eval_intervention = test_ys_intervention.shape[0]
            if n_eval_intervention > n_features + 1:
                tabpfn_r2ad_intervention = 1 - (1 - tabpfn_r2_intervention) * (n_eval_intervention - 1) / (n_eval_intervention - n_features - 1)
            else:
                tabpfn_r2ad_intervention = np.nan # Not enough samples or features for adjusted R2

            tabpfn_evs_intervention = explained_variance_score(test_ys_intervention, tabpfn_pred_intervention)
            tabpfn_mae_intervention = mean_absolute_error(test_ys_intervention, tabpfn_pred_intervention)
            tabpfn_mape_intervention = mean_absolute_percentage_error(test_ys_intervention, tabpfn_pred_intervention)


            tabpfn_success = True
            print(f"TabPFN训练完成，原始测试集 RMSE: {tabpfn_rmse_original:.4f}, R²: {tabpfn_r2_original:.4f}, AdjustR²: {tabpfn_r2ad_original:.4f}, EVS: {tabpfn_evs_original:.4f}, MAE: {tabpfn_mae_original:.4f}, MAPE: {tabpfn_mape_original:.4f}")
            print(f"TabPFN干预测试集 RMSE: {tabpfn_rmse_intervention:.4f}, R²: {tabpfn_r2_intervention:.4f}, AdjustR²: {tabpfn_r2ad_intervention:.4f}, EVS: {tabpfn_evs_intervention:.4f}, MAE: {tabpfn_mae_intervention:.4f}, MAPE: {tabpfn_mape_intervention:.4f}")
        except Exception as e:
            print(f"TabPFN训练失败: {str(e)}")
            tabpfn_success = False
            tabpfn_rmse_original = tabpfn_r2_original = tabpfn_rmse_intervention = tabpfn_r2_intervention = None

        # 清理内存
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        try:
            print("开始训练XGBoost模型")
            xgb_pred_original, xgb_pred_intervention = train_xgboost(
                train_xs, train_ys,
                test_xs_original, test_xs_intervention,
                y_scaler if normalize else None
            )

            # 计算XGBoost指标
            xgb_rmse_original = np.sqrt(mean_squared_error(test_ys_original, xgb_pred_original))
            xgb_r2_original = r2_score(test_ys_original, xgb_pred_original)
            # Correct n_samples for adjusted R2 calculation (using n_eval_original from above)
            if n_eval_original > n_features + 1:
                xgb_r2ad_original = 1 - (1 - xgb_r2_original) * (n_eval_original - 1) / (n_eval_original - n_features - 1)
            else:
                xgb_r2ad_original = np.nan

            xgb_evs_original = explained_variance_score(test_ys_original, xgb_pred_original)
            xgb_mae_original = mean_absolute_error(test_ys_original, xgb_pred_original)
            xgb_mape_original = mean_absolute_percentage_error(test_ys_original, xgb_pred_original)

            xgb_rmse_intervention = np.sqrt(mean_squared_error(test_ys_intervention, xgb_pred_intervention))
            xgb_r2_intervention = r2_score(test_ys_intervention, xgb_pred_intervention)
            # Correct n_samples for adjusted R2 calculation (using n_eval_intervention from above)
            if n_eval_intervention > n_features + 1:
                xgb_r2ad_intervention = 1 - (1 - xgb_r2_intervention) * (n_eval_intervention - 1) / (n_eval_intervention - n_features - 1)
            else:
                xgb_r2ad_intervention = np.nan

            xgb_evs_intervention = explained_variance_score(test_ys_intervention, xgb_pred_intervention)
            xgb_mae_intervention = mean_absolute_error(test_ys_intervention, xgb_pred_intervention)
            xgb_mape_intervention = mean_absolute_percentage_error(test_ys_intervention, xgb_pred_intervention)

            xgb_success = True
            print(f"XGBoost训练完成，原始测试集 RMSE: {xgb_rmse_original:.4f}, R²: {xgb_r2_original:.4f}, AdjustR²: {xgb_r2ad_original:.4f}, EVS: {xgb_evs_original:.4f}, MAE: {xgb_mae_original:.4f}, MAPE: {xgb_mape_original:.4f}")
            print(f"XGBoost干预测试集 RMSE: {xgb_rmse_intervention:.4f}, R²: {xgb_r2_intervention:.4f}, AdjustR²: {xgb_r2ad_intervention:.4f}, EVS: {xgb_evs_intervention:.4f}, MAE: {xgb_mae_intervention:.4f}, MAPE: {xgb_mape_intervention:.4f}")
        except Exception as e:
            print(f"XGBoost训练失败: {str(e)}")
            xgb_success = False
            xgb_rmse_original = xgb_r2_original = xgb_rmse_intervention = xgb_r2_intervention = None

        # 打印结果摘要
        # print(f"\n数据集 {dataset_name} 评估完成")
        # print(f"数据集样本数: {n_samples}")
        # print(f"特征数: {n_features}")
        # print(f"原始训练样本数: {original_train_samples}")
        # print(f"原始测试集样本数: {original_test_original_samples}")
        # print(f"原始干预测试集样本数: {original_test_intervention_samples}")
        # print(f"是否标准化: {'是' if normalize else '否'}")
        # print(f"是否剔除异常值: {'是' if remove_outliers_flag else '否'}")
        if remove_outliers_flag:
            print(f"异常值阈值: {outlier_threshold} 个标准差")
            print(f"剔除的训练集异常值样本数: {removed_train_samples}")
            print(f"剔除的原始测试集异常值样本数: {removed_test_original_samples}")
            print(f"剔除的干预测试集异常值样本数: {removed_test_intervention_samples}")
            print(f"处理后训练样本数: {processed_train_samples}")
            print(f"处理后原始测试集样本数: {processed_test_original_samples}")
            print(f"处理后干预测试集样本数: {processed_test_intervention_samples}")
        if tabpfn_success and xgb_success:
            print(f"------------------------------------")
            # print("\n原始测试集结果:")
            # print(f"TabPFN RMSE: {tabpfn_rmse_original:.4f}, R²: {tabpfn_r2_original:.4f}")
            # print(f"XGBoost RMSE: {xgb_rmse_original:.4f}, R²: {xgb_r2_original:.4f}")
            # print("\n干预测试集结果:")
            # print(f"TabPFN RMSE: {tabpfn_rmse_intervention:.4f}, R²: {tabpfn_r2_intervention:.4f}")
            # print(f"XGBoost RMSE: {xgb_rmse_intervention:.4f}, R²: {xgb_r2_intervention:.4f}")
        else:
            print("\n部分模型训练失败，请查看上方错误信息")

        # 返回结果字典
        return {
            'dataset_name': dataset_name,
            'n_samples': n_samples,
            'n_features': n_features,
            'original_train_samples': original_train_samples,
            'processed_train_samples': processed_train_samples,
            'original_test_original_samples': original_test_original_samples,
            'processed_test_original_samples': processed_test_original_samples,
            'original_test_intervention_samples': original_test_intervention_samples,
            'processed_test_intervention_samples': processed_test_intervention_samples,
            'removed_train_outliers': removed_train_samples,
            'removed_test_original_outliers': removed_test_original_samples,
            'removed_test_intervention_outliers': removed_test_intervention_samples,
            'normalized': normalize,
            'outliers_removed': remove_outliers_flag,
            'outlier_threshold': outlier_threshold if remove_outliers_flag else None,
            'tabpfn_success': tabpfn_success,
            'xgb_success': xgb_success,
            'tabpfn_rmse_original': tabpfn_rmse_original,
            'tabpfn_r2_original': tabpfn_r2_original,
            'tabpfn_r2ad_original': tabpfn_r2ad_original,
            'tabpfn_evs_original': tabpfn_evs_original,
            'tabpfn_mae_original': tabpfn_mae_original,
            'tabpfn_mape_original': tabpfn_mape_original,
            'xgb_rmse_original': xgb_rmse_original,
            'xgb_r2_original': xgb_r2_original,
            'xgb_r2ad_original': xgb_r2ad_original,
            'xgb_evs_original': xgb_evs_original,
            'xgb_mae_original': xgb_mae_original,
            'xgb_mape_original': xgb_mape_original,
            'tabpfn_rmse_intervention': tabpfn_rmse_intervention,
            'tabpfn_r2_intervention': tabpfn_r2_intervention,
            'tabpfn_r2ad_intervention': tabpfn_r2ad_intervention,
            'tabpfn_evs_intervention': tabpfn_evs_intervention,
            'tabpfn_mae_intervention': tabpfn_mae_intervention,
            'tabpfn_mape_intervention': tabpfn_mape_intervention,
            'xgb_rmse_intervention': xgb_rmse_intervention,
            'xgb_r2_intervention': xgb_r2_intervention,
            'xgb_r2ad_intervention': xgb_r2ad_intervention,
            'xgb_evs_intervention': xgb_evs_intervention,
            'xgb_mae_intervention': xgb_mae_intervention,
            'xgb_mape_intervention': xgb_mape_intervention,
        }

    except Exception as e:
        print(f"评估数据集 {dataset[0] if len(dataset) > 0 else '未知'} 时出错: {str(e)}")
        traceback.print_exc()

        # 确保在出错时也清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

        # 返回错误信息
        return {
            'dataset_name': dataset[0] if len(dataset) > 0 else '未知',
            'error': str(e),
            'normalized': normalize,
            'outliers_removed': remove_outliers_flag,
            'outlier_threshold': outlier_threshold if remove_outliers_flag else None
        }


def evaluate_all_datasets_parallel(ds_list, normalize=False, remove_outliers_flag=False, outlier_threshold=3.0,
                                   max_workers=None):
    """使用线程池并行评估所有数据集，优化GPU资源分配"""
    # 用于存储所有数据集的结果
    all_results = []

    # 获取可用的GPU数量
    num_gpus = torch.cuda.device_count() if torch.cuda.is_available() else 0
    if num_gpus == 0:
        print("警告：未检测到可用的GPU，将使用CPU运行TabPFN")
        device_type = 'cpu'
    else:
        print(f"检测到 {num_gpus} 个GPU设备")
        device_type = 'gpu'

    # 设置最大工作线程数
    if max_workers is None:
        if device_type == 'gpu':
            # GPU模式：每个GPU分配一个工作线程，避免GPU内存争用
            max_workers = num_gpus
        else:
            # CPU模式：使用CPU核心数的线程，但不超过数据集数量
            max_workers = min(os.cpu_count(), len(ds_list))

    print(f"数据预处理设置:")
    print(f"- 标准化: {'启用' if normalize else '禁用'}")
    print(f"- 异常值剔除: {'启用' if remove_outliers_flag else '禁用'}")
    if remove_outliers_flag:
        print(f"- 异常值阈值: {outlier_threshold} 个标准差")
    print(f"- 并行工作线程数: {max_workers}")
    print(f"- 计算设备: {'GPU' if device_type == 'gpu' else 'CPU'}")

    # 创建GPU分配锁，确保每个GPU一次只被一个线程使用
    if device_type == 'gpu':
        gpu_locks = [threading.Lock() for _ in range(num_gpus)]

        # 创建GPU分配函数
        def get_available_gpu():
            for i, lock in enumerate(gpu_locks):
                if lock.acquire(blocking=False):
                    return i, lock
            return None, None

        # 创建GPU释放函数
        def release_gpu(lock):
            if lock:
                lock.release()

    # 修改评估函数，加入GPU资源管理
    def evaluate_dataset_with_resource_management(dataset, normalize, remove_outliers_flag, outlier_threshold):
        gpu_id = 0
        gpu_lock = None

        try:
            # 如果是GPU模式，尝试获取可用GPU
            if device_type == 'gpu':
                gpu_id, gpu_lock = get_available_gpu()

                # 如果没有可用GPU，等待并重试
                while gpu_id is None:
                    time.sleep(1)  # 等待1秒
                    gpu_id, gpu_lock = get_available_gpu()

                print(f"数据集 {dataset[0]} 分配到 GPU {gpu_id}")

            # 执行评估
            result = evaluate_dataset(dataset, normalize, remove_outliers_flag, outlier_threshold, gpu_id)
            return result

        except Exception as e:
            print(f"评估数据集 {dataset[0] if len(dataset) > 0 else '未知'} 时出错: {str(e)}")
            traceback.print_exc()
            return {
                'dataset_name': dataset[0] if len(dataset) > 0 else '未知',
                'error': str(e),
                'normalized': normalize,
                'outliers_removed': remove_outliers_flag,
                'outlier_threshold': outlier_threshold if remove_outliers_flag else None
            }
        finally:
            # 释放GPU资源
            if device_type == 'gpu' and gpu_lock is not None:
                release_gpu(gpu_lock)
                print(f"GPU {gpu_id} 已释放")

    # 使用线程池进行并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = {
            executor.submit(
                evaluate_dataset_with_resource_management,
                dataset,
                normalize,
                remove_outliers_flag,
                outlier_threshold
            ): i for i, dataset in enumerate(ds_list)
        }

        # 处理完成的任务
        for future in concurrent.futures.as_completed(futures):
            dataset_idx = futures[future]
            try:
                result = future.result()
                all_results.append(result)
                dataset_name = result.get('dataset_name', f'数据集 {dataset_idx}')
                print(f"完成数据集 {dataset_name} ({dataset_idx + 1}/{len(ds_list)})")

            except Exception as e:
                dataset_name = ds_list[dataset_idx][0] if dataset_idx < len(ds_list) else "未知"
                print(f"处理数据集 {dataset_name} (索引 {dataset_idx}) 的结果时出错: {str(e)}")
                traceback.print_exc()

    # 将所有结果合并为DataFrame
    results_df = pd.DataFrame(all_results)

    # 创建文件名，包含处理信息
    filename = 'evaluation_results'
    if normalize:
        filename += '_normalized'
    if remove_outliers_flag:
        filename += f'_no_outliers_{outlier_threshold}std'
    filename += '.csv'

    print(f"\n已完成所有 {len(all_results)} 个数据集的评估")
    print(f"结果已保存到 '{filename}'")

    return results_df, filename


def plot_distribution(data_dict, metric_name, plot_type='boxplot', dataset_info=None, save_path=None):
    """
    绘制指定指标的箱线图或小提琴图，比较不同模型在不同数据集上的表现

    参数:
    data_dict (dict): 包含各模型数据的字典，格式为 {'模型名称': 数据数组}
    metric_name (str): 指标名称，如'RMSE'或'R²'
    plot_type (str): 图表类型，'boxplot'或'violinplot'
    dataset_info (dict): 数据集信息，包含样本数和特征数的范围
    save_path (str, optional): 保存图像的路径，如果为None则显示图像

    返回:
    None
    """
    # 设置图表样式
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(10, 6))
    # 转换为长格式
    df_long = pd.melt(pd.DataFrame(data_dict), var_name='Model', value_name=metric_name)

    # 添加颜色映射
    colors = {
        'TabPFN (Original)': '#ffe8c9',
        'XGBoost (Original)': '#fdcabf',
        'TabPFN (Intervention)': '#c0d6ea',
        'XGBoost (Intervention)': '#a6c7e2'
    }

    # 绘制分布图
    if plot_type == 'boxplot':
        # sns.boxplot(x='Model', y=metric_name, data=df_long, palette=colors, whis=1.5, ax=ax)
        sns.boxplot(x='Model', y=metric_name, hue='Model', data=df_long, palette=colors, whis=1.5, ax=ax, legend=False)
    else:  # violinplot
        # sns.violinplot(x='Model', y=metric_name, data=df_long, palette=colors, inner='box', ax=ax)
        sns.violinplot(x='Model', y=metric_name, hue='Model', data=df_long, palette=colors, inner='points', ax=ax, legend=False)
        # inner参数可选值:
        # 'box': 显示箱线图(您当前使用的)
        # 'quartile': 显示四分位线
        # 'points': 显示所有数据点
        # 'stick': 显示小竖线表示每个数据点
        # None: 不显示内部结构
    # 添加标题和标签
    better_direction = 'lower is better' if metric_name in ['RMSE', 'MAE', 'MAPE'] else 'higher is better'
    ax.set_title(f'{metric_name} Comparison across Datasets (n={dataset_info["valid_datasets_count"]})', fontsize=16)
    ax.set_ylabel(f'{metric_name} ({better_direction})', fontsize=14)
    ax.tick_params(axis='x', rotation=45)

    # 添加均值点和数值
    for i, model in enumerate(df_long['Model'].unique()):
        mean_val = df_long[df_long['Model'] == model][metric_name].mean()
        median_val = df_long[df_long['Model'] == model][metric_name].median()
        ax.scatter(i, mean_val, color='white', s=80, zorder=3, marker='o', edgecolor='black')
        ax.text(i, mean_val, f'μ={mean_val:.3f}', ha='center', va='bottom', fontsize=10)
        ax.text(i, median_val, f'Med={median_val:.3f}', ha='center', va='top', fontsize=10, color='darkblue')

    # 添加数据集信息标注
    if dataset_info:
        info_text = f"Datasets info:\n" \
                    f"Sample size: {dataset_info['min_samples']} - {dataset_info['max_samples']}\n" \
                    f"Features: {dataset_info['min_features']} - {dataset_info['max_features']}\n" \
                    f"Number of datasets: {dataset_info['valid_datasets_count']}/{dataset_info['total_datasets']}"

        # 将注释放在图表的右上角
        ax.text(0.98, 0.98, info_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8))

    plt.tight_layout()

    # 保存或显示图像
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图像已保存到: {save_path}")

    plt.close()


def plot_statistical_tests(data_dict1, data_dict2, metric1_name, metric2_name, dataset_info=None, save_path=None):
    """
    执行统计检验并绘制结果表格

    参数:
    data_dict1 (dict): 第一个指标的数据字典，格式为 {'模型名称': 数据数组}
    data_dict2 (dict): 第二个指标的数据字典，格式为 {'模型名称': 数据数组}
    metric1_name (str): 第一个指标名称，如'RMSE'
    metric2_name (str): 第二个指标名称，如'R²'
    dataset_info (dict): 数据集信息，包含样本数和特征数的范围
    save_path (str, optional): 保存图像的路径，如果为None则显示图像

    返回:
    None
    """
    # 设置图表样式
    plt.style.use('seaborn-v0_8-whitegrid')
    fig = plt.figure(figsize=(14, 12))

    # 定义要比较的组
    comparisons = [
        ('TabPFN (Original)', 'XGBoost (Original)'),
        ('TabPFN (Original)', 'TabPFN (Intervention)'),
        ('XGBoost (Original)', 'XGBoost (Intervention)'),
        ('TabPFN (Intervention)', 'XGBoost (Intervention)')
    ]

    # 创建结果存储表格
    metric1_results = []
    metric2_results = []

    for group1, group2 in comparisons:
        # 第一个指标的均值配对t检验
        t_stat_1, p_val_1 = stats.ttest_rel(data_dict1[group1], data_dict1[group2])

        # 第一个指标的标准差F检验
        f_stat_1 = np.var(data_dict1[group1]) / np.var(data_dict1[group2])
        if f_stat_1 < 1:
            f_stat_1 = 1 / f_stat_1
            dof1, dof2 = len(data_dict1[group2]) - 1, len(data_dict1[group1]) - 1
        else:
            dof1, dof2 = len(data_dict1[group1]) - 1, len(data_dict1[group2]) - 1
        p_val_f_1 = 2 * (1 - stats.f.cdf(f_stat_1, dof1, dof2))

        # 第二个指标的均值配对t检验
        t_stat_2, p_val_2 = stats.ttest_rel(data_dict2[group1], data_dict2[group2])

        # 第二个指标的标准差F检验
        f_stat_2 = np.var(data_dict2[group1]) / np.var(data_dict2[group2])
        if f_stat_2 < 1:
            f_stat_2 = 1 / f_stat_2
            dof1, dof2 = len(data_dict2[group2]) - 1, len(data_dict2[group1]) - 1
        else:
            dof1, dof2 = len(data_dict2[group1]) - 1, len(data_dict2[group2]) - 1
        p_val_f_2 = 2 * (1 - stats.f.cdf(f_stat_2, dof1, dof2))

        # 格式化结果
        metric1_results.append([
            f"{group1} vs {group2}",
            f"{data_dict1[group1].mean():.4f} vs {data_dict1[group2].mean():.4f}",
            f"{t_stat_1:.4f}",
            f"{p_val_1:.4f}",
            "Yes" if p_val_1 < 0.05 else "No",
            f"{data_dict1[group1].std():.4f} vs {data_dict1[group2].std():.4f}",
            f"{f_stat_1:.4f}",
            f"{p_val_f_1:.4f}",
            "Yes" if p_val_f_1 < 0.05 else "No"
        ])

        metric2_results.append([
            f"{group1} vs {group2}",
            f"{data_dict2[group1].mean():.4f} vs {data_dict2[group2].mean():.4f}",
            f"{t_stat_2:.4f}",
            f"{p_val_2:.4f}",
            "Yes" if p_val_2 < 0.05 else "No",
            f"{data_dict2[group1].std():.4f} vs {data_dict2[group2].std():.4f}",
            f"{f_stat_2:.4f}",
            f"{p_val_f_2:.4f}",
            "Yes" if p_val_f_2 < 0.05 else "No"
        ])

    # 创建结果表格的列名
    columns = [
        'Comparison', 'Mean Values', 't-stat', 'p-value', 'Significant?',
        'Std Values', 'F-stat', 'p-value', 'Significant?'
    ]

    # 创建两个子图，用于放置表格和标题
    gs = fig.add_gridspec(2, 1, height_ratios=[1, 1], hspace=0.3)

    # 第一个指标表格子图
    ax1 = fig.add_subplot(gs[0])
    ax1.axis('off')
    ax1.set_title(f"{metric1_name} Statistical Tests (Paired t-test for means, F-test for variances, α=0.05)",
                  fontsize=14, fontweight='bold', pad=2)

    # 绘制第一个指标结果表格
    metric1_table = ax1.table(
        cellText=metric1_results,
        colLabels=columns,
        loc='center',
        cellLoc='center',
        colWidths=[0.15, 0.12, 0.08, 0.08, 0.09, 0.12, 0.08, 0.08, 0.09]
    )
    metric1_table.auto_set_font_size(False)
    metric1_table.set_fontsize(9)
    metric1_table.scale(1, 1.5)

    # 第二个指标表格子图
    ax2 = fig.add_subplot(gs[1])
    ax2.axis('off')
    ax2.set_title(f"{metric2_name} Statistical Tests (Paired t-test for means, F-test for variances, α=0.05)",
                  fontsize=14, fontweight='bold', pad=2)

    # 绘制第二个指标结果表格
    metric2_table = ax2.table(
        cellText=metric2_results,
        colLabels=columns,
        loc='center',
        cellLoc='center',
        colWidths=[0.15, 0.12, 0.08, 0.08, 0.09, 0.12, 0.08, 0.08, 0.09]
    )
    metric2_table.auto_set_font_size(False)
    metric2_table.set_fontsize(9)
    metric2_table.scale(1, 1.5)

    # 为显著性结果添加颜色
    for table in [metric1_table, metric2_table]:
        for i in range(len(comparisons)):
            # 均值显著性
            if table._cells[(i + 1, 4)].get_text().get_text() == "Yes":
                table._cells[(i + 1, 4)].set_facecolor('#c6efce')
                table._cells[(i + 1, 4)].set_text_props(color='#006100')
            else:
                table._cells[(i + 1, 4)].set_facecolor('#ffc7ce')
                table._cells[(i + 1, 4)].set_text_props(color='#9c0006')

            # 标准差显著性
            if table._cells[(i + 1, 8)].get_text().get_text() == "Yes":
                table._cells[(i + 1, 8)].set_facecolor('#c6efce')
                table._cells[(i + 1, 8)].set_text_props(color='#006100')
            else:
                table._cells[(i + 1, 8)].set_facecolor('#ffc7ce')
                table._cells[(i + 1, 8)].set_text_props(color='#9c0006')

    # 添加表格解释说明
    explanation_text = (
        "Statistical Tests Explanation:\n"
        "• Paired t-test: Used for mean comparison because measurements are paired across datasets\n"
        "• F-test: Used for variance comparison\n"
        "• Green cells indicate statistically significant differences (p < 0.05)\n"
        "• Red cells indicate non-significant differences"
    )

    # 添加解释文本到底部，调整位置避免重叠
    fig.text(0.5, 0.08, explanation_text, ha='center', va='center', fontsize=12,
             bbox=dict(facecolor='#f0f0f0', alpha=0.8, boxstyle='round,pad=0.5'))

    # 添加数据集信息标注，调整位置避免与解释文本重叠
    if dataset_info:
        info_text = f"Datasets info: Sample size: {dataset_info['min_samples']} - {dataset_info['max_samples']}, " \
                    f"Features: {dataset_info['min_features']} - {dataset_info['max_features']}, " \
                    f"Number of datasets: {dataset_info['valid_datasets_count']}/{dataset_info['total_datasets']}"

        fig.text(0.5, 0.02, info_text, ha='center', va='center', fontsize=10,
                 bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    # 调整布局，为底部文本留出更多空间
    plt.tight_layout(rect=[0, 0.15, 1, 0.95])

    # 保存或显示图像
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图像已保存到: {save_path}")

    plt.close()



def get_metric_data(results_df, metric_type):
    """
    根据指定的度量类型从DataFrame中提取数据，并只包含符合条件的数据集：
    1. 原始测试集上TabPFN和XGBoost的R2都大于0
    2. 原始测试集上RMSE结果不是异常值

    参数:
    results_df (pd.DataFrame): 包含模型结果的DataFrame
    metric_type (str): 要提取的度量类型 (例如 'rmse', 'r2', 'mae' 等)

    返回:
    dict: 包含所选度量数据的字典，键是模型和数据类型的组合
    int: 符合条件的数据集数量
    """
    try:
        # 筛选R2都大于0的数据集
        valid_r2_mask = (results_df['tabpfn_r2_original'] > 0.5) & (results_df['xgb_r2_original'] > 0.5)

        # 计算RMSE的Z分数，筛选出非异常值
        tabpfn_rmse_zscore = np.abs((results_df['tabpfn_rmse_original'] - results_df['tabpfn_rmse_original'].mean()) /
                                     results_df['tabpfn_rmse_original'].std())
        xgb_rmse_zscore = np.abs((results_df['xgb_rmse_original'] - results_df['xgb_rmse_original'].mean()) /
                                  results_df['xgb_rmse_original'].std())

        # 定义RMSE异常值阈值（通常使用3个标准差）
        outlier_threshold = 3.0
        valid_rmse_mask = (tabpfn_rmse_zscore < outlier_threshold) & (xgb_rmse_zscore < outlier_threshold)

        # 合并条件
        valid_mask = valid_r2_mask & valid_rmse_mask

        # 筛选符合条件的数据
        filtered_df = results_df[valid_mask]

        # 获取符合条件的数据集数量
        valid_datasets_count = valid_mask.sum()

        # 提取指定指标的数据
        data = {
            'TabPFN (Original)': filtered_df[f'tabpfn_{metric_type}_original'],
            'XGBoost (Original)': filtered_df[f'xgb_{metric_type}_original'],
            'TabPFN (Intervention)': filtered_df[f'tabpfn_{metric_type}_intervention'],
            'XGBoost (Intervention)': filtered_df[f'xgb_{metric_type}_intervention']
        }

        return data, valid_datasets_count

    except KeyError as e:
        print(f"错误: 列名 {e} 在 DataFrame 中未找到。")
        print(f"请确保 results_df 包含名为 'tabpfn_{metric_type}_original', 'xgb_{metric_type}_original' 等格式的列。")
        print(f"当前 metric_type 为: '{metric_type}'")
        return {}, 0


if __name__ == '__main__':
    # 示例配置
    h_config = {
        'device': 'cpu',
        'min_num_node': 5,
        'max_num_node': 20,
        'num_layers': 4,
        'max_num_children': 10,
        'max_num_classes': 5,
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        'min_root': 0.0,
        'max_root': 0.5,
        'max_range': 0.5,
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        'min_num_samples': 500,
        'max_num_samples': 1000,
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        'task': 'regression',  # classification, regression
        'intervention_on_no_root': True,
        'sample_std': True,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,  # 仅当last_layer_fix_num_node为True时使用
        'intervention_on_observed': True,
        'sample_cause_ranges': True
    }

    intervention_node_type= 'single_child'
    intervention_value_method='sample'

    ds = generate_datasets(
        num_dataset=1000,
        h_config=h_config,
        perturbation_type='counterfactual',
        perturbation_node_type= intervention_node_type,
        perturbation_value_method=intervention_value_method,
        node_unobserved=False,
        # custom_dag_type='common_cause'
    )
    # ['single_parent', 'single_child', 'single_sibling', 'single_spouse', 'single_grandparent',
    # 'all_parents', 'all_children', 'all_siblings', 'all_spouses', 'all_grandparents', 'all_family',
    # 'all_non_parents', 'all_non_children', 'all_non_siblings','all_non_spouses', 'all_non_grandparents', 'all_non_family']
    # 可选：保存数据集到CSV文件
    # save_datasets_to_csv(standard_datasets, './data/standard')
    # save_datasets_to_csv(ds, './data/perturbed')
    # save_datasets_to_csv(ds, './data/intervention')
    print("Dataset generation complete!")

    # # 示例：使用预定义的common_cause结构（U -> X1,X2,X3 -> Y）
    # intervention_datasets = generate_datasets(
    #     num_dataset=5,
    #     h_config=h_config,
    #     intervention_type='counterfactual',
    #     intervention_node_type='single_parent',
    #     intervention_value_method='sample',
    #     node_unobserved=False,
    #     custom_dag_type='common_cause'  # 使用预定义的common_cause结构
    # )

    # # 示例：使用自定义边列表定义DAG结构
    # custom_edges = [
    #     ('U', 'X1'), ('U', 'X2'), ('U', 'X3'),
    #     ('X1', 'Y'), ('X2', 'Y'), ('X3', 'Y'),
    #     ('X1', 'Z'), ('Z', 'Y')  # 添加额外的路径 X1 -> Z -> Y
    # ]
    # intervention_datasets = generate_datasets(
    #     num_dataset=5,
    #     h_config=h_config,
    #     intervention_type='counterfactual',
    #     intervention_node_type='single_parent',
    #     intervention_value_method='sample',
    #     node_unobserved=False,
    #     custom_edges=custom_edges  # 使用自定义边列表
    # )

    # 使用线程池并行处理评估所有数据集

    results_df, results_file_name = evaluate_all_datasets_parallel(ds)

    current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    time_folder = f"./results/{intervention_node_type}/perturbation/{intervention_value_method}/unobserved/layer{h_config['num_layers']}/{current_time}"

    os.makedirs(time_folder, exist_ok=True)

    results_df.to_csv(f'{time_folder}/{results_file_name}', index=False)

    # 准备RMSE数据 - 使用修改后的函数
    rmse_data, valid_count = get_metric_data(results_df, 'rmse')

    # 准备其他指标数据
    r2_data, _ = get_metric_data(results_df, 'r2')
    r2ad_data, _ = get_metric_data(results_df, 'r2ad')
    evs_data, _ = get_metric_data(results_df, 'evs')
    mae_data, _ = get_metric_data(results_df, 'mae')
    mape_data, _ = get_metric_data(results_df, 'mape')

    # 准备数据集信息，包含有效数据集数量
    dataset_info = {
        'min_samples': results_df['n_samples'].min(),
        'max_samples': results_df['n_samples'].max(),
        'min_features': results_df['n_features'].min(),
        'max_features': results_df['n_features'].max(),
        'total_datasets': len(results_df),
        'valid_datasets_count': valid_count
    }

    # 绘制箱线图
    plot_distribution(rmse_data, 'RMSE', plot_type='boxplot', dataset_info=dataset_info, save_path=f'{time_folder}/rmse_boxplot.png')
    plot_distribution(mape_data, 'MAPE', plot_type='boxplot', dataset_info=dataset_info, save_path=f'{time_folder}/mape_boxplot.png')

    # 绘制小提琴图
    plot_distribution(rmse_data, 'RMSE', plot_type='violinplot', dataset_info=dataset_info, save_path=f'{time_folder}/rmse_violinplot.png')
    plot_distribution(mape_data, 'MAPE', plot_type='violinplot', dataset_info=dataset_info, save_path=f'{time_folder}/mape_violinplot.png')

    # 绘制统计检验结果表格
    plot_statistical_tests(rmse_data, mape_data, 'RMSE', 'MAPE', dataset_info=dataset_info, save_path=f'{time_folder}/statistical_tests.png')