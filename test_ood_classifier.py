import numpy as np
import pandas as pd
import xgboost as xgb
from tabpfn import TabPFNClassifier
from sklearn.metrics import accuracy_score, f1_score, recall_score, precision_score, roc_auc_score
import os
import torch


def evaluate_dataset(dataset):
    # 提取单个数据集的信息
    dataset_name = dataset[0]
    xs_original, ys_original = dataset[1].clone().cpu(), dataset[2].clone().cpu()
    xs_intervention, ys_intervention = dataset[3].clone().cpu(), dataset[4].clone().cpu()

    # 转换为numpy数组以便于处理
    xs_original_np = xs_original.numpy()
    ys_original_np = ys_original.numpy().astype(int)  # 确保标签是整数类型
    xs_intervention_np = xs_intervention.numpy()
    ys_intervention_np = ys_intervention.numpy().astype(int)

    # 分割数据
    eval_position = int(xs_original.shape[0] * 0.5)
    train_xs, train_ys = xs_original_np[0:eval_position], ys_original_np[0:eval_position]
    test_xs_original, test_ys_original = xs_original_np[eval_position:], ys_original_np[eval_position:]
    test_xs_intervention, test_ys_intervention = xs_intervention_np[eval_position:], ys_intervention_np[eval_position:]

    # 获取数据集信息
    n_samples = xs_original.shape[0]
    n_features = xs_original.shape[1]

    # 检查是否为二分类问题
    is_binary = len(np.unique(train_ys)) == 2

    # 训练TabPFN模型
    tabpfn_model = TabPFNClassifier(device='cpu')
    tabpfn_model.fit(train_xs, train_ys)

    # TabPFN预测
    tabpfn_pred_proba_original = tabpfn_model.predict_proba(test_xs_original)
    tabpfn_pred_original = tabpfn_model.predict(test_xs_original)
    tabpfn_pred_proba_intervention = tabpfn_model.predict_proba(test_xs_intervention)
    tabpfn_pred_intervention = tabpfn_model.predict(test_xs_intervention)

    # 计算TabPFN指标 - 原始测试集
    tabpfn_accuracy_original = accuracy_score(test_ys_original, tabpfn_pred_original)
    tabpfn_f1_original = f1_score(test_ys_original, tabpfn_pred_original, average='weighted')
    tabpfn_recall_original = recall_score(test_ys_original, tabpfn_pred_original, average='weighted')
    tabpfn_precision_original = precision_score(test_ys_original, tabpfn_pred_original, average='weighted')

    # 计算TabPFN指标 - 干预测试集
    tabpfn_accuracy_intervention = accuracy_score(test_ys_intervention, tabpfn_pred_intervention)
    tabpfn_f1_intervention = f1_score(test_ys_intervention, tabpfn_pred_intervention, average='weighted')
    tabpfn_recall_intervention = recall_score(test_ys_intervention, tabpfn_pred_intervention, average='weighted')
    tabpfn_precision_intervention = precision_score(test_ys_intervention, tabpfn_pred_intervention, average='weighted')

    # 对于二分类问题，计算AUC
    tabpfn_auc_original = None
    tabpfn_auc_intervention = None
    if is_binary:
        tabpfn_auc_original = roc_auc_score(test_ys_original, tabpfn_pred_proba_original[:, 1])
        tabpfn_auc_intervention = roc_auc_score(test_ys_intervention, tabpfn_pred_proba_intervention[:, 1])

    # 训练XGBoost模型
    xgb_model = xgb.XGBClassifier(objective='binary:logistic' if is_binary else 'multi:softprob', random_state=42)
    xgb_model.fit(train_xs, train_ys)

    # XGBoost预测
    xgb_pred_proba_original = xgb_model.predict_proba(test_xs_original)
    xgb_pred_original = xgb_model.predict(test_xs_original)
    xgb_pred_proba_intervention = xgb_model.predict_proba(test_xs_intervention)
    xgb_pred_intervention = xgb_model.predict(test_xs_intervention)

    # 计算XGBoost指标 - 原始测试集
    xgb_accuracy_original = accuracy_score(test_ys_original, xgb_pred_original)
    xgb_f1_original = f1_score(test_ys_original, xgb_pred_original, average='weighted')
    xgb_recall_original = recall_score(test_ys_original, xgb_pred_original, average='weighted')
    xgb_precision_original = precision_score(test_ys_original, xgb_pred_original, average='weighted')

    # 计算XGBoost指标 - 干预测试集
    xgb_accuracy_intervention = accuracy_score(test_ys_intervention, xgb_pred_intervention)
    xgb_f1_intervention = f1_score(test_ys_intervention, xgb_pred_intervention, average='weighted')
    xgb_recall_intervention = recall_score(test_ys_intervention, xgb_pred_intervention, average='weighted')
    xgb_precision_intervention = precision_score(test_ys_intervention, xgb_pred_intervention, average='weighted')

    # 对于二分类问题，计算AUC
    xgb_auc_original = None
    xgb_auc_intervention = None
    if is_binary:
        xgb_auc_original = roc_auc_score(test_ys_original, xgb_pred_proba_original[:, 1])
        xgb_auc_intervention = roc_auc_score(test_ys_intervention, xgb_pred_proba_intervention[:, 1])

    # 打印结果
    print(f"\n数据集名称: {dataset_name}")
    print(f"数据集样本数: {n_samples}")
    print(f"特征数: {n_features}")
    print("\n原始测试集结果:")
    print(
        f"TabPFN - Accuracy: {tabpfn_accuracy_original:.4f}, F1: {tabpfn_f1_original:.4f}, Recall: {tabpfn_recall_original:.4f}, Precision: {tabpfn_precision_original:.4f}")
    if is_binary:
        print(f"TabPFN - AUC: {tabpfn_auc_original:.4f}")
    print(
        f"XGBoost - Accuracy: {xgb_accuracy_original:.4f}, F1: {xgb_f1_original:.4f}, Recall: {xgb_recall_original:.4f}, Precision: {xgb_precision_original:.4f}")
    if is_binary:
        print(f"XGBoost - AUC: {xgb_auc_original:.4f}")

    print("\n干预测试集结果:")
    print(
        f"TabPFN - Accuracy: {tabpfn_accuracy_intervention:.4f}, F1: {tabpfn_f1_intervention:.4f}, Recall: {tabpfn_recall_intervention:.4f}, Precision: {tabpfn_precision_intervention:.4f}")
    if is_binary:
        print(f"TabPFN - AUC: {tabpfn_auc_intervention:.4f}")
    print(
        f"XGBoost - Accuracy: {xgb_accuracy_intervention:.4f}, F1: {xgb_f1_intervention:.4f}, Recall: {xgb_recall_intervention:.4f}, Precision: {xgb_precision_intervention:.4f}")
    if is_binary:
        print(f"XGBoost - AUC: {xgb_auc_intervention:.4f}")

    # 返回结果字典
    result = {
        'dataset_name': dataset_name,
        'n_samples': n_samples,
        'n_features': n_features,
        'is_binary': is_binary,
        'tabpfn_accuracy_original': tabpfn_accuracy_original,
        'tabpfn_f1_original': tabpfn_f1_original,
        'tabpfn_recall_original': tabpfn_recall_original,
        'tabpfn_precision_original': tabpfn_precision_original,
        'xgb_accuracy_original': xgb_accuracy_original,
        'xgb_f1_original': xgb_f1_original,
        'xgb_recall_original': xgb_recall_original,
        'xgb_precision_original': xgb_precision_original,
        'tabpfn_accuracy_intervention': tabpfn_accuracy_intervention,
        'tabpfn_f1_intervention': tabpfn_f1_intervention,
        'tabpfn_recall_intervention': tabpfn_recall_intervention,
        'tabpfn_precision_intervention': tabpfn_precision_intervention,
        'xgb_accuracy_intervention': xgb_accuracy_intervention,
        'xgb_f1_intervention': xgb_f1_intervention,
        'xgb_recall_intervention': xgb_recall_intervention,
        'xgb_precision_intervention': xgb_precision_intervention
    }

    # 对于二分类问题，添加AUC指标
    if is_binary:
        result.update({
            'tabpfn_auc_original': tabpfn_auc_original,
            'xgb_auc_original': xgb_auc_original,
            'tabpfn_auc_intervention': tabpfn_auc_intervention,
            'xgb_auc_intervention': xgb_auc_intervention
        })

    return result


def evaluate_all_datasets(ds_list):
    # 用于存储所有数据集的结果
    all_results = []

    # 遍历每个数据集并评估
    for i, dataset in enumerate(ds_list):
        print(f"正在评估数据集 {i + 1}/{len(ds_list)}: {dataset[0]}")
        try:
            result = evaluate_dataset(dataset)
            all_results.append(result)
        except Exception as e:
            print(f"评估数据集 {dataset[0]} 时出错: {str(e)}")

    # 将所有结果合并为DataFrame
    results_df = pd.DataFrame(all_results)

    # 保存结果到CSV文件
    results_df.to_csv('all_datasets_classification_results.csv', index=False)

    print(f"\n已完成所有 {len(all_results)} 个数据集的评估，结果已保存到 'all_datasets_classification_results.csv'")

    return results_df

# 假设ds是包含多个数据集的列表，调用函数评估所有数据集
# results = evaluate_all_datasets(ds)
