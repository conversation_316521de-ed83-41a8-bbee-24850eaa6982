#!/usr/bin/env python3
"""
测试详细输出格式
验证数据分割信息的完整显示（包含 X 和 Y 数据）
"""

import os
import sys
import traceback

def test_detailed_output():
    """测试详细输出格式"""
    try:
        print("=" * 60)
        print("测试详细输出格式")
        print("=" * 60)
        
        # 导入必要的模块
        from scm_data_provider import DataGenerationConfig, DataProvider, SNRConfig, NodeFunctionConfigs, FunctionConfig
        import torch
        
        print("✓ 成功导入所有模块")
        
        # 创建基础配置
        print("\n1. 创建基础配置...")
        h_config = {
            'device': 'cpu',
            'min_noise_std': 0.01,
            'max_noise_std': 0.1,
            'min_init_std': 1,
            'max_init_std': 5,
            'root_distribution': 'gaussian',
            'root_mean': 0.0,
            'root_std': 1.0,
            'sample_root_std': False,
            'min_root': 0.0,
            'max_root': 1.0,
            'max_range': 0.5,
            'sample_cause_ranges': False,
            'sample_std': False,
            'min_num_samples': 100,  # 使用合理的样本数量
            'max_num_samples': 100,
            'train_test_split_ratio': 0.7,
            'task': 'regression',
            'min_output_multiclass_ordered_p': 0.0,
            'max_output_multiclass_ordered_p': 0.5,
            'categorical_feature_p': 0,
        }
        
        # 创建简单的SNR配置
        snr_config = SNRConfig(
            parent_snr_values=[2.0],  # 只使用一个SNR值以加快测试
            child_snr_multipliers=[1.0],
            other_snr_multipliers=[1.0]
        )
        
        # 创建简单的函数配置
        node_function_configs = NodeFunctionConfigs(
            target_config=FunctionConfig(function_type='linear'),
            target_child_config=FunctionConfig(function_type='polynomial'),
            other_config=FunctionConfig(function_type='mlp')
        )
        
        # 创建数据配置
        data_config = DataGenerationConfig(
            n_datasets=2,  # 生成2个数据集以测试
            dataset_type='standard',  # 使用标准数据集类型
            node_unobserved=False,
            seed=42,
            allow_skip=True,
            custom_dag_type='random_SF',
            custom_dag_size='small',
            avg_in_degree=(1.2, 1.7),
            device='cpu',
            snr_config=snr_config,
            node_function_configs=node_function_configs
        )
        print("✓ 配置创建成功")
        
        # 创建数据提供器并生成数据集
        print("\n2. 生成数据集...")
        provider = DataProvider(h_config, data_config)
        datasets = provider.generate_datasets()
        print(f"✓ 成功生成 {len(datasets)} 个配置的数据集")
        
        # 测试详细的数据分割输出
        print("\n3. 测试详细的数据分割输出...")
        data_splits = provider.get_dataset_splits()
        print(f"  获取了 {len(data_splits)} 个配置的数据集分割")
        
        for config_key, config_data in data_splits.items():
            print(f"    配置 {config_key}: {len(config_data)} 个数据集的分割数据")
            for dataset_idx, dataset_splits in config_data.items():
                dataset_name = dataset_splits['dataset_name']
                dataset_type = dataset_splits['dataset_type']
                feature_count = len(dataset_splits['feature_names'])
                train_x_samples = dataset_splits['train_xs'].shape[0]
                train_y_samples = dataset_splits['train_ys'].shape[0]
                
                print(f"      数据集 {dataset_idx}: 名称={dataset_name}, 类型={dataset_type}, 特征数={feature_count}")
                print(f"        训练数据: X={train_x_samples}样本, Y={train_y_samples}样本")
                
                if dataset_type == 'standard':
                    test_x_samples = dataset_splits['test_xs'].shape[0]
                    test_y_samples = dataset_splits['test_ys'].shape[0]
                    print(f"        测试数据: X={test_x_samples}样本, Y={test_y_samples}样本")
                    
                    # 验证数据一致性
                    assert train_x_samples == train_y_samples, "训练数据 X 和 Y 样本数不一致"
                    assert test_x_samples == test_y_samples, "测试数据 X 和 Y 样本数不一致"
                    
                else:
                    test_original_x_samples = dataset_splits['test_xs_original'].shape[0]
                    test_original_y_samples = dataset_splits['test_ys_original'].shape[0]
                    test_modified_x_samples = dataset_splits['test_xs_modified'].shape[0]
                    test_modified_y_samples = dataset_splits['test_ys_modified'].shape[0]
                    print(f"        原始测试数据: X={test_original_x_samples}样本, Y={test_original_y_samples}样本")
                    print(f"        {dataset_type}测试数据: X={test_modified_x_samples}样本, Y={test_modified_y_samples}样本")
                    
                    # 验证数据一致性
                    assert train_x_samples == train_y_samples, "训练数据 X 和 Y 样本数不一致"
                    assert test_original_x_samples == test_original_y_samples, "原始测试数据 X 和 Y 样本数不一致"
                    assert test_modified_x_samples == test_modified_y_samples, "修改测试数据 X 和 Y 样本数不一致"
                
                # 验证特征名称
                feature_names = dataset_splits['feature_names']
                assert len(feature_names) == feature_count, "特征名称数量与特征数不一致"
                assert len(feature_names) == dataset_splits['train_xs'].shape[1], "特征名称数量与训练数据特征维度不一致"
                
                print(f"        特征名称: {feature_names[:3]}{'...' if len(feature_names) > 3 else ''}")
        
        # 测试数据访问示例
        print("\n4. 数据访问示例:")
        first_config_key = list(data_splits.keys())[0]
        first_dataset = data_splits[first_config_key][0]
        
        print(f"  如何访问第一个配置的第一个数据集:")
        print(f"    配置键: {first_config_key}")
        print(f"    数据集名称: {first_dataset['dataset_name']}")
        print(f"    训练 X 数据形状: {first_dataset['train_xs'].shape}")
        print(f"    训练 Y 数据形状: {first_dataset['train_ys'].shape}")
        
        if first_dataset['dataset_type'] == 'standard':
            print(f"    测试 X 数据形状: {first_dataset['test_xs'].shape}")
            print(f"    测试 Y 数据形状: {first_dataset['test_ys'].shape}")
        else:
            print(f"    原始测试 X 数据形状: {first_dataset['test_xs_original'].shape}")
            print(f"    原始测试 Y 数据形状: {first_dataset['test_ys_original'].shape}")
            print(f"    修改测试 X 数据形状: {first_dataset['test_xs_modified'].shape}")
            print(f"    修改测试 Y 数据形状: {first_dataset['test_ys_modified'].shape}")
        
        print(f"    特征名称: {first_dataset['feature_names']}")
        print(f"    配置信息: {first_dataset['config_info']['name']}")
        
        print("\n✓ 所有详细输出格式测试通过！")
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_detailed_output()
    if success:
        print(f"\n{'='*60}")
        print("🎉 详细输出格式测试成功！")
        print("✅ 数据分割信息现在包含完整的 X 和 Y 数据信息")
        print("✅ 显示格式更加清晰，便于理解数据结构")
        print("📊 输出信息包括:")
        print("   - 数据集名称和类型")
        print("   - 特征数量和特征名称")
        print("   - 训练数据的 X 和 Y 样本数")
        print("   - 测试数据的 X 和 Y 样本数")
        print("   - 对于干预/扰动数据集，还包括原始和修改后的测试数据")
        print("💡 这样的输出格式便于用户了解如何提取和使用数据")
        print(f"{'='*60}")
        sys.exit(0)
    else:
        print(f"\n{'='*60}")
        print("❌ 测试失败，请检查错误信息")
        print(f"{'='*60}")
        sys.exit(1)
