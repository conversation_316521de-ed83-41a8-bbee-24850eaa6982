parents: ['X1', 'X3', 'X10'] -> child: X0
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.5347729410043062

parents: ['X18'] -> child: X5
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.039617589462281964

parents: ['X1'] -> child: X6
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.8354667336229313

parents: ['X18'] -> child: X7
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 1.8395638870588114

parents: ['X5', 'X14', 'X16'] -> child: X8
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.267105157890848

parents: ['X1', 'X2', 'X4', 'X11', 'X13', 'X17', 'X18'] -> child: X9
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 1.5866972320314678

parents: ['X1'] -> child: X12
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 1.4510903727053752

parents: ['X12'] -> child: X15
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.191701718157168

parents: ['X5'] -> child: X19
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.03757087571175488