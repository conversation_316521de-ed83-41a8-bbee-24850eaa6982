import torch
import pandas as pd
import random
import os
import numpy as np
import networkx as nx
from datetime import datetime
from multi_scm_model import SCM, SkipDatasetException
from scm_utils import draw_graph, uniform_int_sampler_f, uniform_sampler_f

try:
    import igraph as ig
except ImportError:
    raise ImportError("需要安装igraph库才能生成随机DAG。请使用 pip install python-igraph 安装。")


def get_dataset_seed(dataset_index, seed_type, seed=None, seeds_config=None):
    """
    根据种子系统配置获取指定数据集和种子类型的种子

    种子系统有四种情况:
        1. 如果seed=None且seeds_config=None: 不指定任何种子，所有数据集使用随机种子(None)
        2. 如果seed=None但seeds_config不为None:
           - 对于提供了种子列表的种子类型，每个数据集使用列表中对应的种子(各自独享)
           - 对于未提供种子列表的种子类型，所有数据集使用随机种子(None)
           - 如果种子列表长度不足，剩余的数据集基于固定种子生成递增种子
        3. 如果seed不为None且seeds_config=None: 每个数据集有独特的种子(seed+类型偏移量+数据集索引)
        4. 如果seed不为None且seeds_config不为None:
           - 对于提供了种子列表的种子类型，每个数据集使用列表中对应的种子(各自独享)
           - 对于未提供种子列表的种子类型，所有数据集共享同一个种子(seed+类型偏移量)
           - 如果种子列表长度不足，为剩余数据集基于全局种子生成递增种子

    参数:
        dataset_index: 数据集索引(从0开始)
        seed_type: 种子类型，支持 'dag_generation', 'scm_model', 'config_generation', 'data_sampling'
        seed: 全局种子
        seeds_config: 种子配置字典

    返回:
        该数据集该种子类型应使用的种子值，如果为None则表示使用随机种子
    """
    # 定义种子类型偏移量，使用足够大的间隔确保不同类型的种子不会冲突
    seed_type_offsets = {
        'dag_generation': 100000,      # 10万
        'scm_model': 200000,           # 20万
        'config_generation': 300000,   # 30万
        'data_sampling': 400000        # 40万
    }

    if seed_type not in seed_type_offsets:
        raise ValueError(f"不支持的种子类型: {seed_type}")

    type_offset = seed_type_offsets[seed_type]

    # 情况1: seed=None且seeds_config=None
    if seed is None and seeds_config is None:
        return None

    # 情况2: seed=None但seeds_config不为None
    if seed is None and seeds_config is not None:
        if seed_type in seeds_config and seeds_config[seed_type] is not None:
            seed_list = seeds_config[seed_type]
            if dataset_index < len(seed_list):
                # 使用列表中对应的种子
                return seed_list[dataset_index]
            else:
                # 种子列表长度不足，基于固定种子生成递增种子
                base_seed = 1000000 + type_offset  # 使用固定基础种子
                extra_index = dataset_index - len(seed_list)
                return base_seed + extra_index
        else:
            # 未提供种子列表，使用随机种子
            return None

    # 情况3: seed不为None且seeds_config=None
    if seed is not None and seeds_config is None:
        return seed + type_offset + dataset_index

    # 情况4: seed不为None且seeds_config不为None
    if seed is not None and seeds_config is not None:
        if seed_type in seeds_config and seeds_config[seed_type] is not None:
            seed_list = seeds_config[seed_type]
            if dataset_index < len(seed_list):
                # 使用列表中对应的种子
                return seed_list[dataset_index]
            else:
                # 种子列表长度不足，基于全局种子生成递增种子
                extra_index = dataset_index - len(seed_list)
                return seed + type_offset + extra_index
        else:
            # 未提供种子列表，使用全局种子+偏移量
            return seed + type_offset

    return None


def generate_custom_dag(structure_type='common_cause', custom_edges=None,
                        size='small', seed=None, remove_isolated_nodes=False,
                        predefined_intervention_nodes=None, predefined_perturbation_nodes=None,
                        predefined_unobserved_nodes=None, avg_in_degree=(1.2, 1.7)):
    """
    生成预定义结构的DAG图或根据自定义边列表生成DAG图

    参数:
        structure_type: 预定义结构类型，如'common_cause', 'chain', 'fork', 'collider'等
                       或者'random_ER'(Erdős-Rényi随机图), 'random_SF'(Scale-free无标度网络)
        custom_edges: 自定义边列表，格式为[(parent, child), ...]，优先级高于structure_type
        size: 随机图的规模，可选'small', 'medium', 'large', 'vlarge'，仅当使用随机图时有效
        seed: 随机种子，用于重现结果
        remove_isolated_nodes: 是否移除孤立节点（没有任何连接的节点），默认False
        predefined_intervention_nodes: 预定义的干预节点列表，如果指定则直接使用，不进行搜索或采样
        predefined_perturbation_nodes: 预定义的扰动节点列表，如果指定则直接使用，不进行搜索或采样
        predefined_unobserved_nodes: 预定义的不可观测节点列表，如果指定则直接使用，不按原有逻辑计算
        avg_in_degree: 随机图的平均入度范围，格式为(min_degree, max_degree)，默认(1.2, 1.7)，仅当使用随机图时有效

    返回:
        G: NetworkX DiGraph对象
        root_nodes: 根节点列表
        layer_nodes: 每层节点列表
        target_node: 目标节点 (对于随机图类型返回None)
        feature_nodes: 特征节点列表 (对于随机图类型返回None)
        dag_info: DAG信息字典，包含预定义配置
    """
    # 设置随机种子
    if seed is not None:
        random.seed(seed)
        np.random.seed(seed)

    G = nx.DiGraph()

    # 如果提供了自定义边列表，则使用它构建图
    if custom_edges is not None:
        # 从边列表中提取所有唯一节点
        all_nodes = set()
        for parent, child in custom_edges:
            all_nodes.add(parent)
            all_nodes.add(child)

        # 添加所有节点到图中
        G.add_nodes_from(all_nodes)

        # 添加所有边
        G.add_edges_from(custom_edges)

        # 找出根节点（入度为0的节点）
        root_nodes = [node for node in G.nodes if G.in_degree(node) == 0]

        # 尝试根据图的结构推断层次
        layer_nodes = []
        remaining_nodes = set(G.nodes)

        # 第一层是根节点
        layer_nodes.append(list(root_nodes))
        remaining_nodes -= set(root_nodes)

        # 逐层添加节点，直到所有节点都被添加
        while remaining_nodes:
            current_layer = []
            for node in remaining_nodes:
                # 检查节点的所有父节点是否都已经在前面的层中
                parents = set(G.predecessors(node))
                if all(parent in [n for layer in layer_nodes for n in layer] for parent in parents):
                    current_layer.append(node)

            # 如果无法添加更多节点，则剩余节点可能存在循环依赖
            if not current_layer:
                print("警告: 图中可能存在循环依赖，无法完全分层。将剩余节点放入最后一层。")
                layer_nodes.append(list(remaining_nodes))
                break

            layer_nodes.append(current_layer)
            remaining_nodes -= set(current_layer)

        # 默认将最后一层的第一个节点作为目标节点
        target_node = layer_nodes[-1][0] if layer_nodes[-1] else None

        # 默认将除目标节点外的所有节点作为特征节点
        feature_nodes = [node for node in G.nodes if node != target_node]

        # 创建DAG信息字典，包含预定义配置
        dag_info = {
            'type': 'custom_edges',
            'predefined_intervention_nodes': predefined_intervention_nodes,
            'predefined_perturbation_nodes': predefined_perturbation_nodes,
            'predefined_unobserved_nodes': predefined_unobserved_nodes
        }

        return G, root_nodes, layer_nodes, target_node, feature_nodes, dag_info

    # 检查是否为随机图类型
    if structure_type.startswith('random'):
        # 节点数范围定义
        nodenumclass = {'small': (11, 20), 'medium': (21, 50), 'large': (51, 100), 'vlarge': (101, 200)}
        # 平均入度范围 - 使用传入的参数
        avgInDegree = avg_in_degree

        # 确定节点数量
        if size in nodenumclass:
            min_nodes, max_nodes = nodenumclass[size]
            d = random.randint(min_nodes, max_nodes)
        else:
            d = random.randint(11, 20)  # 默认使用small规模

        # 确定边数量
        avg_degree = random.uniform(avgInDegree[0], avgInDegree[1])
        s0 = int(d * avg_degree)

        # 从structure_type中提取图类型
        graph_type = 'ER' if structure_type == 'random_ER' else 'SF'

        # 生成随机DAG
        adj_matrix = simulate_dag(d, s0, graph_type, remove_isolated_nodes)

        # 确保adj_matrix是有效的
        actual_size = adj_matrix.shape[0]  # 获取实际的矩阵大小

        # 创建节点名称
        nodes = [f'X{i}' for i in range(actual_size)]
        G.add_nodes_from(nodes)

        # 添加边
        for i in range(actual_size):
            for j in range(actual_size):
                if adj_matrix[i, j] == 1:
                    G.add_edge(nodes[i], nodes[j])

        # 找出根节点（入度为0的节点）
        root_nodes = [node for node in G.nodes if G.in_degree(node) == 0]

        # 拓扑排序来确定层次
        try:
            topo_sorted = list(nx.topological_sort(G))
            layer_nodes = []

            # 通过拓扑排序确定层次
            remaining_nodes = set(topo_sorted)
            while remaining_nodes:
                # 当前层包括所有入度为0的节点(考虑剩余子图)
                current_layer = []
                # 对remaining_nodes进行排序以确保确定性的遍历顺序
                for node in sorted(list(remaining_nodes)):
                    # 检查在剩余子图中是否入度为0
                    if all(pred not in remaining_nodes for pred in G.predecessors(node)):
                        current_layer.append(node)

                if not current_layer:  # 如果无法找到入度为0的节点，可能存在循环
                    break

                # 对当前层的节点进行排序以确保确定性的顺序
                current_layer.sort()
                layer_nodes.append(current_layer)
                remaining_nodes -= set(current_layer)

            # 如果还有剩余节点，将它们放入最后一层
            if remaining_nodes:
                layer_nodes.append(list(remaining_nodes))

            # 对于随机图类型，返回None作为target_node和feature_nodes
            # 这些将在SCM中通过select_valid_target_node和select_features动态选择
            # 添加图类型信息，用于重试时保持一致性
            dag_info = {
                'type': 'random',
                'graph_type': graph_type,
                'size': size,
                'structure_type': structure_type,
                'node_count': actual_size,
                'predefined_intervention_nodes': predefined_intervention_nodes,
                'predefined_perturbation_nodes': predefined_perturbation_nodes,
                'predefined_unobserved_nodes': predefined_unobserved_nodes
            }
            return (G, root_nodes, layer_nodes, None, None, dag_info)

        except nx.NetworkXUnfeasible:
            print("警告: 生成的图不是DAG，可能存在循环。")
            # 创建一个简单的备选DAG
            G = nx.DiGraph()
            G.add_nodes_from(['X0', 'X1', 'X2'])
            G.add_edges_from([('X0', 'X1'), ('X1', 'X2')])
            return G, ['X0'], [['X0'], ['X1'], ['X2']], 'X2', ['X0', 'X1']

    # 预定义结构
    if structure_type == 'common_cause':
        # U -> X1, X2, X3 -> Y 结构
        G.add_nodes_from(['U', 'X1', 'X2', 'X3', 'Y'])
        G.add_edges_from([
            ('U', 'X1'), ('U', 'X2'), ('U', 'X3'),
            ('X1', 'Y'), ('X2', 'Y'), ('X3', 'Y')
        ])
        root_nodes = ['U']
        layer_nodes = [['U'], ['X1', 'X2', 'X3'], ['Y']]
        target_node = 'Y'
        feature_nodes = ['U', 'X1', 'X2', 'X3']
        # 预定义结构中的默认节点配置（默认为None，表示使用原有逻辑）
        structure_intervention_nodes = None  # 可设置为具体节点，如 ['X1', 'X2']
        structure_perturbation_nodes = None  # 可设置为具体节点，如 ['X1', 'X3']
        structure_unobserved_nodes = None    # 可设置为具体节点，如 ['U']

    elif structure_type == 'chain':
        # X1 -> X2 -> X3 -> Y 结构
        G.add_nodes_from(['X1', 'X2', 'X3', 'Y'])
        G.add_edges_from([('X1', 'X2'), ('X2', 'X3'), ('X3', 'Y')])
        root_nodes = ['X1']
        layer_nodes = [['X1'], ['X2'], ['X3'], ['Y']]
        target_node = 'Y'
        feature_nodes = ['X1', 'X2', 'X3']
        # 预定义结构中的默认节点配置（默认为None，表示使用原有逻辑）
        structure_intervention_nodes = None  # 可设置为具体节点，如 ['X1']
        structure_perturbation_nodes = None  # 可设置为具体节点，如 ['X2']
        structure_unobserved_nodes = None    # 可设置为具体节点，如 ['X1']

    elif structure_type == 'fork':
        # X1 <- Z -> X2, Y 结构
        G.add_nodes_from(['Z', 'X1', 'X2', 'Y'])
        G.add_edges_from([('Z', 'X1'), ('Z', 'X2'), ('Z', 'Y')])
        root_nodes = ['Z']
        layer_nodes = [['Z'], ['X1', 'X2', 'Y']]
        target_node = 'Y'
        feature_nodes = ['X1', 'X2']
        # 预定义结构中的默认节点配置（默认为None，表示使用原有逻辑）
        structure_intervention_nodes = None  # 可设置为具体节点，如 ['Z']
        structure_perturbation_nodes = None  # 可设置为具体节点，如 ['Z']
        structure_unobserved_nodes = None    # 可设置为具体节点，如 ['Z']

    elif structure_type == 'collider':
        # X1 -> Y <- X2 结构
        G.add_nodes_from(['X1', 'X2', 'Y'])
        G.add_edges_from([('X1', 'Y'), ('X2', 'Y')])
        root_nodes = ['X1', 'X2']
        layer_nodes = [['X1', 'X2'], ['Y']]
        target_node = 'Y'
        feature_nodes = ['X1', 'X2']
        # 预定义结构中的默认节点配置（默认为None，表示使用原有逻辑）
        structure_intervention_nodes = None  # 可设置为具体节点，如 ['X1']
        structure_perturbation_nodes = None  # 可设置为具体节点，如 ['X2']
        structure_unobserved_nodes = None    # 可设置为具体节点，如 ['X1']

    elif structure_type == 'diamond':
        # X -> Z1, Z2 -> Y 结构
        G.add_nodes_from(['X', 'Z1', 'Z2', 'Y'])
        G.add_edges_from([('X', 'Z1'), ('X', 'Z2'), ('Z1', 'Y'), ('Z2', 'Y')])
        root_nodes = ['X']
        layer_nodes = [['X'], ['Z1', 'Z2'], ['Y']]
        target_node = 'Y'
        feature_nodes = ['X', 'Z1', 'Z2']
        # 预定义结构中的默认节点配置（默认为None，表示使用原有逻辑）
        structure_intervention_nodes = None  # 可设置为具体节点，如 ['X']
        structure_perturbation_nodes = None  # 可设置为具体节点，如 ['Z1']
        structure_unobserved_nodes = None    # 可设置为具体节点，如 ['X']

    elif structure_type == 'simple':
        # X -> Y  Z1 -> Z2结构
        G.add_nodes_from(['U', 'X', 'Y'])
        G.add_edges_from([
            ('U', 'Y'), ('U', 'X')
        ])
        root_nodes = ['U']
        layer_nodes = [['U'], ['X', 'Y']]
        target_node = 'Y'
        feature_nodes = ['U', 'X']
        # 预定义结构中的默认节点配置（默认为None，表示使用原有逻辑）
        # 示例：可以设置为具体节点来覆盖原有逻辑
        structure_intervention_nodes = None  # 设置为具体节点，如 ['U1']
        structure_perturbation_nodes = None  # 设置为具体节点，如 ['U1']
        structure_unobserved_nodes = None    # 设置为具体节点，如 ['U2']

    elif structure_type == 'complex':
        # 一个更复杂的结构，包含多个路径和共同原因
        G.add_nodes_from(['A', 'B', 'C', 'D', 'E', 'F', 'Y'])
        G.add_edges_from([
            ('A', 'B'), ('A', 'C'),
            ('B', 'D'), ('B', 'E'),
            ('C', 'E'), ('C', 'F'),
            ('D', 'Y'), ('E', 'Y'), ('F', 'Y')
        ])
        root_nodes = ['A']
        layer_nodes = [['A'], ['B', 'C'], ['D', 'E', 'F'], ['Y']]
        target_node = 'Y'
        feature_nodes = ['A', 'B', 'C', 'D', 'E', 'F']
        # 预定义结构中的默认节点配置（默认为None，表示使用原有逻辑）
        structure_intervention_nodes = None  # 可设置为具体节点，如 ['A', 'B']
        structure_perturbation_nodes = None  # 可设置为具体节点，如 ['B', 'C']
        structure_unobserved_nodes = None    # 可设置为具体节点，如 ['A']

    else:
        raise ValueError(f"未知的结构类型: {structure_type}")

    # 为预定义结构创建DAG信息字典，包含预定义配置
    # 实现优先级：全局设置 > 结构级设置 > None（使用原有逻辑）
    final_intervention_nodes = predefined_intervention_nodes if predefined_intervention_nodes is not None else structure_intervention_nodes
    final_perturbation_nodes = predefined_perturbation_nodes if predefined_perturbation_nodes is not None else structure_perturbation_nodes
    final_unobserved_nodes = predefined_unobserved_nodes if predefined_unobserved_nodes is not None else structure_unobserved_nodes

    dag_info = {
        'type': 'predefined',
        'structure_type': structure_type,
        'predefined_intervention_nodes': final_intervention_nodes,
        'predefined_perturbation_nodes': final_perturbation_nodes,
        'predefined_unobserved_nodes': final_unobserved_nodes,
        # 保存原始的结构级配置，用于调试和信息显示
        'structure_intervention_nodes': structure_intervention_nodes,
        'structure_perturbation_nodes': structure_perturbation_nodes,
        'structure_unobserved_nodes': structure_unobserved_nodes
    }

    return G, root_nodes, layer_nodes, target_node, feature_nodes, dag_info


def test_seed_reproducibility(h_config, test_seed=42, test_seeds_config=None):
    """
    测试种子系统的可重现性

    参数:
        h_config: 高级配置字典
        test_seed: 测试用的全局种子
        test_seeds_config: 测试用的种子配置
    """
    print(f"\n测试种子可重现性: seed={test_seed}, seeds_config={test_seeds_config}")

    # 第一次运行
    print("第一次运行...")
    datasets_run1 = generate_datasets(
        num_dataset=1,
        h_config=h_config,
        custom_dag_type='common_cause',
        seed=test_seed,
        seeds_config=test_seeds_config,
        allow_skip=True
    )

    # 第二次运行
    print("第二次运行...")
    datasets_run2 = generate_datasets(
        num_dataset=1,
        h_config=h_config,
        custom_dag_type='common_cause',
        seed=test_seed,
        seeds_config=test_seeds_config,
        allow_skip=True
    )

    # 比较结果
    print("比较结果...")
    if len(datasets_run1) > 0 and len(datasets_run2) > 0:
        x1, y1 = datasets_run1[0][1], datasets_run1[0][2]
        x2, y2 = datasets_run2[0][1], datasets_run2[0][2]

        x_equal = torch.allclose(x1, x2, atol=1e-6)
        y_equal = torch.allclose(y1, y2, atol=1e-6)

        print(f"特征数据是否相同: {x_equal}")
        print(f"目标数据是否相同: {y_equal}")

        if x_equal and y_equal:
            print("✅ 可重现性测试通过!")
        else:
            print("❌ 可重现性测试失败!")
            print(f"第一次运行前3行特征数据:\n{x1[:3]}")
            print(f"第二次运行前3行特征数据:\n{x2[:3]}")
    else:
        print("❌ 数据集生成失败，无法进行比较")


def simulate_dag(d, s0, graph_type, remove_isolated_nodes=False):
    """
    生成随机DAG
    Args:
        d (int): 节点数量
        s0 (int): 期望的边数量
        graph_type (str): 图类型，'ER'或'SF'
        remove_isolated_nodes (bool): 是否移除孤立节点，默认False
    Returns:
        np.ndarray: DAG的二值邻接矩阵
    """


    def _random_permutation(M):
        """随机置换矩阵的行和列"""
        P = np.random.permutation(np.eye(M.shape[0]))
        return P.T @ M @ P

    def _acyclic_orientation(B_und):
        """将无向图转换为有向无环图"""
        return np.tril(B_und, k=-1)

    def _remove_isolating_node(B):
        """移除孤立节点"""
        non_iso_index = np.logical_or(B.any(axis=0), B.any(axis=1))
        if non_iso_index.any():  # 确保至少有一个非孤立节点
            return B[non_iso_index][:, non_iso_index]
        return B  # 如果所有节点都是孤立的，返回原矩阵

    def _graph_to_adjmat(G):
        """将图对象转换为邻接矩阵"""
        return np.array(G.get_adjacency().data)

    # 确保节点数量和边数量是合理的
    d = max(2, d)  # 至少有2个节点
    s0 = max(1, min(s0, d * (d - 1) // 2))  # 边数不超过完全图的边数

    # 根据指定类型生成无向图
    try:
        if graph_type == 'ER':  # Erdős-Rényi随机图
            G_und = ig.Graph.Erdos_Renyi(n=d, m=s0)
        elif graph_type == 'SF':  # Scale-free无标度网络
            m = max(1, min(int(round(s0 / d)), d - 1))  # 确保m合理
            G_und = ig.Graph.Barabasi(n=d, m=m, directed=False)
        else:
            raise ValueError('未知的图类型')
    except Exception as e:
        print(f"生成图时出错: {e}")
        # 创建一个简单的备选图
        G_und = ig.Graph.Erdos_Renyi(n=min(d, 5), m=min(s0, 5))

    try:
        # 将无向图转换为DAG
        B_und = _graph_to_adjmat(G_und)
        B_und = _random_permutation(B_und)
        B = _acyclic_orientation(B_und)

        # 根据参数决定是否移除孤立节点
        if B.size > 0 and remove_isolated_nodes:
            B = _remove_isolating_node(B)

        # 再次随机置换以增加多样性
        if B.size > 0 and B.shape[0] > 1:
            B_perm = _random_permutation(B).astype(int)
            # 验证结果是否为DAG
            if ig.Graph.Adjacency(B_perm.tolist()).is_dag():
                return B_perm
            else:
                print("警告: 置换后的图不是DAG，返回原始矩阵")
                return B.astype(int)
        else:
            return B.astype(int)
    except Exception as e:
        print(f"处理邻接矩阵时出错: {e}")
        # 返回一个简单的有向无环图
        simple_dag = np.zeros((min(d, 3), min(d, 3)), dtype=int)
        if simple_dag.shape[0] > 1:
            simple_dag[0, 1] = 1
        if simple_dag.shape[0] > 2:
            simple_dag[1, 2] = 1
        return simple_dag

def config_generation(h_config, seed=None):
    """生成SCM的配置

    参数:
        h_config: 高级配置字典
        seed: 随机种子，用于确保配置生成的可重现性
    """
    # 初始化一个空列表，用于存储每层的节点数
    n_nodes_list = []
    # 创建一个节点数量采样器，从 h_config 中指定的最小和最大节点数之间均匀采样整数
    # 为节点数量采样器使用种子
    num_node_sampler = uniform_int_sampler_f(h_config['min_num_node'], h_config['max_num_node'], seed=seed)
    # 循环生成除最后一层外的每层节点数
    for layer_idx in range(h_config['num_layers'] - 1):
        n_nodes_list.append(num_node_sampler())
    # 根据 h_config 中的配置决定最后一层的节点数
    if h_config['single_effect_last_layer']:
        # 如果最后一层是单一效应节点，则节点数为 1
        n_nodes_list.append(1)
    elif h_config['last_layer_fix_num_node']:
        # 如果最后一层节点数固定，则使用 h_config 中指定的数量
        n_nodes_list.append(h_config['num_node_last_layer'])
    else:
        # 否则，使用节点数量采样器生成最后一层的节点数
        n_nodes_list.append(num_node_sampler())

    # 为每层（除最后一层外）创建一个子节点数量采样器列表
    # 每个采样器从 1 到 h_config['max_num_children'] 和下一层节点数中的较小值之间均匀采样整数
    # 为每个子节点数量采样器使用不同的种子偏移
    num_children_samplers = [uniform_int_sampler_f(1, min(h_config['max_num_children'], n_nodes_list[i + 1]),
                                                  seed=None if seed is None else seed + 100 + i) for i in
                             range(len(n_nodes_list) - 1)]

    # 从 h_config 中指定的最小和最大噪声标准差之间均匀采样一个噪声标准差
    if h_config['min_noise_std'] == h_config['max_noise_std']:
        noise_std = h_config['min_noise_std']
    else:
        noise_std = uniform_sampler_f(h_config['min_noise_std'], h_config['max_noise_std'],
                                    seed=None if seed is None else seed + 200)()

    # 从 h_config 中指定的最小和最大 MLP 初始化标准差之间均匀采样一个初始化标准差
    init_std = uniform_sampler_f(h_config['min_init_std'], h_config['max_init_std'],
                                seed=None if seed is None else seed + 300)()

    # 从 h_config 中指定的最小和最大输出多类有序概率之间均匀采样一个概率值
    output_multiclass_ordered_p = uniform_sampler_f(h_config['min_output_multiclass_ordered_p'],
                                                    h_config['max_output_multiclass_ordered_p'],
                                                    seed=None if seed is None else seed + 400)()

    # 从 h_config 中指定的最小和最大样本数量之间均匀采样一个整数作为样本数量
    num_samples = uniform_int_sampler_f(h_config['min_num_samples'], h_config['max_num_samples'],
                                       seed=None if seed is None else seed + 500)()

    # 从 h_config 中指定的最小和最大节点丢弃比例之间均匀采样一个丢弃比例
    drop_node_ratio = uniform_sampler_f(h_config['min_drop_node_ratio'], h_config['max_drop_node_ratio'],
                                       seed=None if seed is None else seed + 600)()

    # 从 2 到 h_config 中指定的最大类别数之间均匀采样一个整数作为分类任务的目标类别数
    num_classes = uniform_int_sampler_f(2, h_config['max_num_classes'],
                                       seed=None if seed is None else seed + 700)()

    # 创建配置字典，存储所有采样得到的参数和 h_config 中的一些固定参数
    config = {
        'device': h_config['device'],  # 设备（如 'cpu' 或 'cuda'）
        'n_nodes_list': n_nodes_list,  # 每层的节点数列表
        'noise_std': noise_std,  # 噪声标准差
        'init_std': init_std,  # MLP 初始化标准差
        'num_children_samplers': num_children_samplers,  # 子节点数量采样器列表
        'min_root': h_config['min_root'],  # 根节点初始化范围的最小值
        'max_root': h_config['max_root'],  # 根节点初始化范围的最大值
        'max_range': h_config['max_range'],  # 根节点初始化范围的最大长度
        'num_classes': num_classes,  # 分类任务的目标类别数
        'output_multiclass_ordered_p': output_multiclass_ordered_p,  # 输出多类有序的概率
        'num_samples': num_samples,  # 样本数量
        'task': h_config['task'],  # 任务类型（如 'classification' 或 'regression'）
        'sample_std': h_config['sample_std'],  # 是否对标准差进行采样
        'categorical_feature_p': h_config['categorical_feature_p'],  # 生成类别特征的概率
        'drop_node_ratio': drop_node_ratio,  # 节点丢弃比例
        'sample_cause_ranges': h_config['sample_cause_ranges'],  # 是否采样原因节点的范围
        # 新增根节点分布配置
        'root_distribution': h_config.get('root_distribution', 'uniform'),  # 根节点分布类型
        'root_mean': h_config.get('root_mean', 0.0),  # 高斯分布均值
        'root_std': h_config.get('root_std', 1.0),  # 高斯分布标准差
        'sample_root_std': h_config.get('sample_root_std', False),  # 是否采样标准差
        # 新增训练/测试分割比例配置
        'train_test_split_ratio': h_config.get('train_test_split_ratio', 0.5),  # 训练/测试分割比例
        # 新增SNR相关配置
        'use_snr_method': h_config.get('use_snr_method', False),  # 是否使用SNR方法
        'snr_config': h_config.get('snr_config', None),  # SNR配置参数

        # 新增蒙特卡洛计算参数配置
        'use_monte_carlo_precompute': h_config.get('use_monte_carlo_precompute', False),  # 是否进行蒙特卡洛计算
        'monte_carlo_samples': h_config.get('monte_carlo_samples', 10000),  # 蒙特卡洛样本数
        'monte_carlo_root_seed': h_config.get('monte_carlo_root_seed', 42),  # 采样根节点的蒙特卡洛种子
        'monte_carlo_noise_seed': h_config.get('monte_carlo_noise_seed', 123),  # 采样噪声的蒙特卡洛种子
        'monte_carlo_resample': h_config.get('monte_carlo_resample', True),  # 是否对父节点的蒙特卡洛样本重采样
        'monte_carlo_resample_size': h_config.get('monte_carlo_resample_size', 1000),  # 重采样数量

    }

    # 返回生成的配置字典
    return config


def generate_datasets(num_dataset, h_config,
                      perturbation_type=None, perturbation_node_type=None, perturbation_value_method='sample',
                     intervention_type=None, intervention_node_type=None, intervention_value_method='sample',
                      node_unobserved=False, unobserved_nodes=None, custom_dag_type=None, custom_edges=None,
                      custom_dag_size='small', remove_isolated_nodes=False, seed=None, seeds_config=None, allow_skip=True,
                      custom_functions=None, predefined_intervention_nodes=None, predefined_perturbation_nodes=None,
                      predefined_unobserved_nodes=None, avg_in_degree=(1.2, 1.7)):
    """
    生成多个数据集
    参数:
        num_dataset: 要生成的数据集数量
        h_config: 高级配置字典
        perturbation_type: 扰动类型，如果不为None，则生成扰动数据
        perturbation_node_type: 扰动节点类型
        intervention_type: 干预类型，如果不为None，则生成干预数据
        intervention_node_type: 干预节点类型
        intervention_value_method: 干预值的生成方法，'mean', 'sample', 或 'random'
        node_unobserved: 控制不可观测节点的选择方式，可以是以下值：
            - False: 所有节点都可被观测
            - True: 干预/扰动节点不可被观测（向后兼容）
            - 'intervention_nodes': 干预/扰动节点不可被观测（与True相同）
            - 'intervention_n_X': 随机选择X个干预/扰动节点不可被观测，X为整数
            - 'intervention_ratio_Y': 随机选择一定比例的干预/扰动节点不可被观测，Y为0到1之间的小数
            - 'random_n_X': 随机选择X个节点不可被观测，X为整数
            - 'random_ratio_Y': 随机选择一定比例的节点不可被观测，Y为0到1之间的小数
            - 'parents', 'children', 'siblings', 'spouses', 'non_parents', 'non_children', 'non_siblings', 'non_spouses', 'markov_blanket', 'non_markov_blanket': 选择与目标节点有特定关系的节点不可被观测
            - 'parents_X', 'children_X', 等: 选择X个与目标节点有特定关系的节点不可被观测，X为整数
            - 'parents_ratio_Y', 'children_ratio_Y', 等: 选择一定比例的与目标节点有特定关系的节点不可被观测，Y为0到1之间的小数
        unobserved_nodes: 直接指定不可被观测的节点列表或集合，优先级高于node_unobserved
        custom_dag_type: 自定义DAG类型，如'common_cause', 'chain', 'random_ER', 'random_SF'等
        custom_edges: 自定义边列表，格式为[(parent, child), ...]
        custom_dag_size: 随机图的规模，可选'small', 'medium', 'large'，仅当使用随机图时有效
        remove_isolated_nodes: 是否移除孤立节点（没有任何连接的节点），默认False
        seed: 全局随机种子，用于重现结果
        seeds_config: 种子配置字典，用于为不同种子类型指定具体的种子列表
                     格式: {'dag_generation': [seed1, seed2, ...], 'scm_model': [seed1, seed2, ...], ...}
                     支持的种子类型: 'dag_generation', 'scm_model', 'config_generation', 'data_sampling'
        allow_skip: 是否允许跳过失败的数据集生成
        custom_functions: 自定义函数配置字典，格式为 {node: function_config}
                         例如: {'Y': {'type': 'linear', 'coefficients': [2.0, 3.0], 'bias': 1.0}}
        predefined_intervention_nodes: 预定义的干预节点列表，如果指定则直接使用，不进行搜索或采样
        predefined_perturbation_nodes: 预定义的扰动节点列表，如果指定则直接使用，不进行搜索或采样
        predefined_unobserved_nodes: 预定义的不可观测节点列表，如果指定则直接使用，不按原有逻辑计算
        avg_in_degree: 随机图的平均入度范围，格式为(min_degree, max_degree)，默认(1.2, 1.7)，仅当使用随机图时有效

    返回:
        包含数据集信息的列表
    """
    datasets = []

    

    # 设置随机种子以确保可重现性
    if seed is not None:
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)

    # 统计信息
    total_attempts = 0
    successful_datasets = 0
    skipped_datasets = 0

    print(f"开始生成 {num_dataset} 个数据集，跳过策略: {'允许跳过' if allow_skip else '不允许跳过'}")
    print(f"种子配置: seed={seed}, seeds_config={seeds_config}")

    # 循环生成直到达到目标数量
    while successful_datasets < num_dataset:
        total_attempts += 1
        current_attempt = total_attempts - 1  # 从0开始的索引

        # 使用新的种子管理系统为每个数据集获取不同类型的种子
        # 重要修复：使用 current_attempt 而不是 successful_datasets 来计算种子
        # 这样即使生成失败，下次重试也会使用不同的种子
        dag_seed = get_dataset_seed(current_attempt, 'dag_generation', seed, seeds_config)
        scm_seed = get_dataset_seed(current_attempt, 'scm_model', seed, seeds_config)
        config_seed = get_dataset_seed(current_attempt, 'config_generation', seed, seeds_config)
        data_seed = get_dataset_seed(current_attempt, 'data_sampling', seed, seeds_config)

        dataset_name = 'dataset_{}'.format(successful_datasets)  # 使用成功的数据集计数命名

        print(f"\n尝试生成第 {successful_datasets + 1} 个数据集 (总尝试次数: {total_attempts})...")
        print(f"种子分配: DAG={dag_seed}, SCM={scm_seed}, Config={config_seed}, Data={data_seed}")

        try:
            config = config_generation(h_config, seed=config_seed)

            # 如果指定了自定义DAG类型或边列表，则生成自定义DAG
            custom_dag = None
            if custom_dag_type is not None or custom_edges is not None:
                # 只有随机图类型才需要size参数
                if custom_dag_type in ['random_ER', 'random_SF']:
                    custom_dag = generate_custom_dag(structure_type=custom_dag_type, custom_edges=custom_edges,
                                                    size=custom_dag_size, seed=dag_seed,
                                                    remove_isolated_nodes=remove_isolated_nodes,
                                                    predefined_intervention_nodes=predefined_intervention_nodes,
                                                    predefined_perturbation_nodes=predefined_perturbation_nodes,
                                                    predefined_unobserved_nodes=predefined_unobserved_nodes,
                                                    avg_in_degree=avg_in_degree)
                else:
                    # 预定义结构不需要size参数，但为了保持一致性也传递avg_in_degree参数
                    custom_dag = generate_custom_dag(structure_type=custom_dag_type, custom_edges=custom_edges,
                                                    seed=dag_seed,
                                                    remove_isolated_nodes=remove_isolated_nodes,
                                                    predefined_intervention_nodes=predefined_intervention_nodes,
                                                    predefined_perturbation_nodes=predefined_perturbation_nodes,
                                                    predefined_unobserved_nodes=predefined_unobserved_nodes,
                                                    avg_in_degree=avg_in_degree)



            # 创建SCM模型，传入自定义DAG（如果有）和种子，以及跳过配置
            # 同时传递干预和扰动节点类型，用于目标节点选择
            scm = SCM(config, custom_dag=custom_dag, seed=scm_seed,
                     allow_skip=allow_skip, custom_functions=custom_functions,
                     intervention_node_type=intervention_node_type,
                     perturbation_node_type=perturbation_node_type)

            # 根据请求生成数据
            if perturbation_type is not None:
                # 生成扰动数据
                (x_original, y_original), (x_perturbation, y_perturbation) = scm.perturbation(
                    perturbation_type=perturbation_type,
                    perturbation_node_type=perturbation_node_type,
                    perturbation_value_method = perturbation_value_method,
                    node_unobserved = node_unobserved,
                    unobserved_nodes = unobserved_nodes,
                    sort_perturbation_nodes=True  # 添加参数确保扰动节点排序
                )

                # 创建数据集信息字典
                data_info = {
                    'dataset_name': dataset_name,
                    'config': config,
                    'dag_type': custom_dag_type,
                    'dag_size': custom_dag_size,
                    'num_nodes': len(scm.dag.nodes()),
                    'num_edges': len(scm.dag.edges()),
                    'task_type': 'perturbation'
                }

                # 添加SNR信息（如果可用）
                if hasattr(scm, 'snr_info') and scm.snr_info is not None:
                    data_info['snr_info'] = scm.snr_info
                if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
                    data_info['snr_validation'] = scm.snr_validation_results

                # 保存数据集，包括SCM对象和数据信息
                dataset = [dataset_name, x_original, y_original, x_perturbation, y_perturbation, scm, data_info]
                datasets.append(dataset)
                
                if custom_dag_size != None:                
                    # 检查特征数量是否与指定规模的节点数范围匹配
                    node_count = len(scm.dag.nodes())
                    feature_count = x_original.shape[1]

                    # 使用与generate_custom_dag中相同的节点数范围定义
                    nodenumclass = {'small': (11, 20), 'medium': (21, 50), 'large': (51, 100), 'vlarge': (101, 200)}

                    # 只检查指定的custom_dag_size对应的范围
                    if custom_dag_size in nodenumclass:
                        min_nodes, max_nodes = nodenumclass[custom_dag_size]
                        expected_features = node_count - 1  # 特征数应该等于节点数-1（目标节点）

                        # 检查节点数是否在指定规模范围内
                        if min_nodes <= node_count <= max_nodes:
                            # 检查特征数是否匹配预期
                            if feature_count != expected_features:
                                print(f"特征数量是{feature_count}，节点数是{node_count}，不匹配{custom_dag_size}类别的预期特征数{expected_features}")

                                # 提供详细的调试信息
                                print(f"调试信息:")
                                print(f"  目标节点: {scm.selected_target}")
                                print(f"  特征节点数量: {len(scm.selected_features) if scm.selected_features else 0}")
                                print(f"  drop_node_ratio: {scm.drop_node_ratio}")

                                # 检查哪些节点缺失
                                all_nodes = set(scm.dag.nodes())
                                target_node = scm.selected_target
                                expected_feature_nodes = all_nodes - {target_node}
                                actual_feature_nodes = set(scm.selected_features) if scm.selected_features else set()

                                missing_nodes = expected_feature_nodes - actual_feature_nodes
                                extra_nodes = actual_feature_nodes - expected_feature_nodes

                                if missing_nodes:
                                    print(f"  缺失的特征节点: {missing_nodes}")
                                if extra_nodes:
                                    print(f"  多余的特征节点: {extra_nodes}")


                                # 创建保存目录
                                debug_dir = "./debug_graphs"
                                os.makedirs(debug_dir, exist_ok=True)

                                # 生成文件名
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
                                graph_filename = f"mismatch_{custom_dag_size}_nodes{node_count}_features{feature_count}_{timestamp}.png"
                                graph_path = os.path.join(debug_dir, graph_filename)

                                # 保存图形，标记目标节点和特征节点
                                draw_graph(scm.dag, graph_path,
                                        target_node=scm.selected_target,
                                        selected_features=scm.selected_features,
                                        assignment=getattr(scm, 'assignment', None),
                                        scm=scm,
                                        model_results=None)

                                print(f"已保存不匹配的DAG图到: {graph_path}")
                                input("Press Enter to continue...")
                        else:
                            print(f"节点数{node_count}不在指定的{custom_dag_size}规模范围[{min_nodes}, {max_nodes}]内，特征数量是{feature_count}")

                            # 提供详细的调试信息
                            print(f"调试信息:")
                            print(f"  目标节点: {scm.selected_target}")
                            print(f"  特征节点数量: {len(scm.selected_features) if scm.selected_features else 0}")
                            print(f"  drop_node_ratio: {scm.drop_node_ratio}")


                            # 创建保存目录
                            debug_dir = "./debug_graphs"
                            os.makedirs(debug_dir, exist_ok=True)

                            # 生成文件名
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
                            graph_filename = f"out_of_range_{custom_dag_size}_nodes{node_count}_features{feature_count}_{timestamp}.png"
                            graph_path = os.path.join(debug_dir, graph_filename)

                            # 保存图形，标记目标节点和特征节点
                            draw_graph(scm.dag, graph_path,
                                    target_node=scm.selected_target,
                                    selected_features=scm.selected_features,
                                    assignment=getattr(scm, 'assignment', None),
                                    scm=scm,
                                    model_results=None)

                            print(f"已保存超出{custom_dag_size}范围的DAG图到: {graph_path}")
                            input("Press Enter to continue...")

            elif intervention_type is not None:
                # 生成干预数据
                (x_original, y_original), (x_intervened, y_intervened) = scm.intervention(
                    intervention_type=intervention_type,
                    intervention_node_type=intervention_node_type,
                    intervention_value_method=intervention_value_method,
                    node_unobserved=node_unobserved,
                    unobserved_nodes=unobserved_nodes,
                    sort_intervention_nodes=True  # 添加参数确保干预节点排序
                )

                # 创建数据集信息字典
                data_info = {
                    'dataset_name': dataset_name,
                    'config': config,
                    'dag_type': custom_dag_type,
                    'dag_size': custom_dag_size,
                    'num_nodes': len(scm.dag.nodes()),
                    'num_edges': len(scm.dag.edges()),
                    'task_type': 'intervention'
                }

                # 添加SNR信息（如果可用）
                if hasattr(scm, 'snr_info') and scm.snr_info is not None:
                    data_info['snr_info'] = scm.snr_info
                if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
                    data_info['snr_validation'] = scm.snr_validation_results

                # 保存数据集，包括SCM对象和数据信息
                dataset = [dataset_name, x_original, y_original, x_intervened, y_intervened, scm, data_info]
                datasets.append(dataset)

            else:
                # 生成普通数据
                x, y, _ = scm.forward()

                # 创建数据集信息字典
                data_info = {
                    'dataset_name': dataset_name,
                    'config': config,
                    'dag_type': custom_dag_type,
                    'dag_size': custom_dag_size,
                    'num_nodes': len(scm.dag.nodes()),
                    'num_edges': len(scm.dag.edges()),
                    'task_type': 'standard'
                }

                # 添加SNR信息（如果可用）
                if hasattr(scm, 'snr_info') and scm.snr_info is not None:
                    data_info['snr_info'] = scm.snr_info
                if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
                    data_info['snr_validation'] = scm.snr_validation_results

                # 保存数据集，包括SCM对象和数据信息
                dataset = [dataset_name, x, y, scm, data_info]
                datasets.append(dataset)

            # 成功生成一个数据集
            successful_datasets += 1
            print(f"✅ 成功生成第 {successful_datasets} 个数据集")

        except SkipDatasetException as e:
            # 捕获跳过异常
            skipped_datasets += 1
            print(f"⚠️  跳过第 {successful_datasets + 1} 个数据集的生成: {str(e)}")
            print(f"   原因: {str(e)}")
            continue

        except Exception as e:
            # 捕获其他异常
            print(f"❌ 生成第 {successful_datasets + 1} 个数据集时发生错误: {str(e)}")
            if allow_skip:
                skipped_datasets += 1
                print(f"   允许跳过，继续生成下一个数据集")
                continue
            else:
                print(f"   不允许跳过，停止生成")
                raise

    # 打印统计信息
    print(f"\n" + "="*60)
    print(f"数据集生成完成!")
    print(f"目标数量: {num_dataset}")
    print(f"成功生成: {successful_datasets}")
    print(f"跳过数量: {skipped_datasets}")
    print(f"总尝试次数: {total_attempts}")
    print(f"成功率: {successful_datasets/total_attempts*100:.1f}%")
    print(f"="*60)

    return datasets


def save_datasets_to_csv(datasets, output_dir='./data', use_timestamp=True):
    """将生成的数据集保存为CSV文件，并使列名与DAG中的节点名称一致，并标示干预/扰动/不可观测节点

    参数:
        datasets: 数据集列表
        output_dir: 基础输出目录
        use_timestamp: 是否使用时间戳创建子目录
    """
    if use_timestamp:
        # 创建以当前时间命名的子目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(output_dir, f"run_{timestamp}")
        print(f"数据将保存到: {output_dir}")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    for dataset in datasets:
        dataset_name = dataset[0]
        print(f"处理数据集: {dataset_name}")

        # 保存特征数据
        x = dataset[1]
        print(f"特征数据形状: {x.shape}")

        # 获取SCM对象
        scm = dataset[-2]

        # 获取目标节点
        target_node = None
        if hasattr(scm, 'selected_target'):
            target_node = scm.selected_target

        print(f"目标节点: {target_node}")

        # 获取特征节点
        feature_nodes = []
        if hasattr(scm, 'selected_features'):
            feature_nodes = scm.selected_features
            print(f"特征节点列表(selected_features): {feature_nodes}")
            print(f"特征节点数量: {len(feature_nodes)}")

        # 获取干预节点
        intervention_nodes = set()
        if hasattr(scm, 'intervention_nodes'):
            intervention_nodes = scm.intervention_nodes
            print(f"干预节点: {intervention_nodes}")

        # 获取扰动节点
        perturbation_nodes = set()
        if hasattr(scm, 'perturbation_nodes'):
            perturbation_nodes = scm.perturbation_nodes
            print(f"扰动节点: {perturbation_nodes}")

        # 获取不可观测节点
        unobserved_nodes = set()
        if hasattr(scm, 'unobserved_nodes'):
            unobserved_nodes = scm.unobserved_nodes
            print(f"不可观测节点: {unobserved_nodes}")

        # 检查特征节点数量是否与数据列数匹配
        mismatch = len(feature_nodes) != x.shape[1]
        if mismatch:
            print(f"警告: 特征节点数量({len(feature_nodes)})与数据列数({x.shape[1]})不匹配!")
            print(f"特征节点列表: {feature_nodes}")
            print(f"这可能是因为存在不可观测节点: {unobserved_nodes}")

            # 如果存在不可观测节点，我们将创建一个包含所有特征的DataFrame，对于不可观测节点填充NaN
            if unobserved_nodes and len(feature_nodes) > x.shape[1]:
                print("处理不可观测节点: 将为不可观测节点创建空值列")

        # 构建干预/扰动信息字符串（用于文件名）
        intervention_info = ""
        if intervention_nodes:
            if isinstance(intervention_nodes, set):
                intervention_info = "_intv" + "_".join(map(str, intervention_nodes))
            else:
                intervention_info = f"_intv{intervention_nodes}"
        elif perturbation_nodes:
            if isinstance(perturbation_nodes, set):
                intervention_info = "_ptb" + "_".join(map(str, perturbation_nodes))
            else:
                intervention_info = f"_ptb{perturbation_nodes}"

        print(f"干预/扰动信息: {intervention_info}")

        # 创建特征列名，标示出干预/扰动/不可观测节点
        feature_cols = []
        for node in feature_nodes:
            col_name = f"feature{node}"
            # 添加干预/扰动标记
            if node in intervention_nodes:
                col_name = f"feature{node}_intv"
            elif node in perturbation_nodes:
                col_name = f"feature{node}_ptb"

            # 添加不可观测标记
            if node in unobserved_nodes:
                col_name = f"{col_name}_unobserved"

            feature_cols.append(col_name)

        print(f"特征列名: {feature_cols}")

        # 将特征数据保存为CSV，使用自定义列名
        if mismatch and unobserved_nodes and len(feature_nodes) > x.shape[1]:
            # 创建一个新的DataFrame，包含所有特征（包括不可观测节点）
            # 首先确定哪些节点是可观测的（数据中实际存在的列）
            observed_features = [feat for feat in feature_nodes if feat not in unobserved_nodes]

            # 如果观测特征数量仍然与数据列数不匹配，可能有其他问题
            if len(observed_features) != x.shape[1]:
                print(f"警告: 即使排除不可观测节点后，特征数量({len(observed_features)})仍与数据列数({x.shape[1]})不匹配")
                print(f"将尝试按顺序匹配可用列")

            # 创建一个全NaN的DataFrame
            df_full = pd.DataFrame(np.nan, index=range(len(x)), columns=feature_cols)

            # 填充实际数据
            x_np = x.cpu().numpy()
            for i, feat in enumerate(observed_features):
                if i < x.shape[1]:  # 确保不超出实际数据列数
                    # 找到该特征在feature_nodes中的索引
                    feat_idx = feature_nodes.index(feat)
                    # 将实际数据填入对应列
                    df_full.iloc[:, feat_idx] = x_np[:, i]

            # 保存完整DataFrame
            df_full.to_csv(f"{output_dir}/{dataset_name}_x{intervention_info}.csv", index=False)
            print(f"已创建包含不可观测节点(NaN值)的数据文件")
        else:
            # 正常情况：特征数量与数据列数匹配
            pd.DataFrame(x.cpu().numpy(), columns=feature_cols).to_csv(
                f"{output_dir}/{dataset_name}_x{intervention_info}.csv", index=False)

        # 保存目标数据，列名包含目标节点信息
        y = dataset[2]
        target_col = [f"target{target_node}"]
        # 如果目标节点是干预节点，也标示出来
        if target_node in intervention_nodes:
            target_col = [f"target{target_node}_intv"]
        elif target_node in perturbation_nodes:
            target_col = [f"target{target_node}_ptb"]

        # 如果目标节点是不可观测节点，也标示出来（虽然通常目标节点不应该是不可观测的）
        if target_node in unobserved_nodes:
            target_col = [f"{target_col[0]}_unobserved"]

        pd.DataFrame(y.cpu().numpy(), columns=target_col).to_csv(
            f"{output_dir}/{dataset_name}_y{intervention_info}.csv", index=False)

        # 如果有干预/扰动数据，也保存
        if len(dataset) > 4:
            x_modified = dataset[3]
            y_modified = dataset[4]

            # 保存修改后的特征数据
            if mismatch and unobserved_nodes and len(feature_nodes) > x_modified.shape[1]:
                # 创建一个新的DataFrame，包含所有特征（包括不可观测节点）
                observed_features = [feat for feat in feature_nodes if feat not in unobserved_nodes]

                # 创建一个全NaN的DataFrame
                df_full_modified = pd.DataFrame(np.nan, index=range(len(x_modified)), columns=feature_cols)

                # 填充实际数据
                x_modified_np = x_modified.cpu().numpy()
                for i, feat in enumerate(observed_features):
                    if i < x_modified.shape[1]:  # 确保不超出实际数据列数
                        # 找到该特征在feature_nodes中的索引
                        feat_idx = feature_nodes.index(feat)
                        # 将实际数据填入对应列
                        df_full_modified.iloc[:, feat_idx] = x_modified_np[:, i]

                # 保存完整DataFrame
                df_full_modified.to_csv(f"{output_dir}/{dataset_name}_x_modified{intervention_info}.csv", index=False)
            else:
                # 正常情况：特征数量与数据列数匹配
                pd.DataFrame(x_modified.cpu().numpy(), columns=feature_cols).to_csv(
                    f"{output_dir}/{dataset_name}_x_modified{intervention_info}.csv", index=False)

            # 保存修改后的目标数据
            pd.DataFrame(y_modified.cpu().numpy(), columns=target_col).to_csv(
                f"{output_dir}/{dataset_name}_y_modified{intervention_info}.csv", index=False)

        print(f"数据集 {dataset_name} 保存完成\n")


if __name__ == '__main__':
    # 示例配置
    h_config = {
        'device': 'cpu',
        'min_num_node': 5,
        'max_num_node': 20,
        'num_layers': 3,
        'max_num_children': 10,
        'max_num_classes': 5,
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        'root_distribution': 'uniform',
        'min_root': 0.0,
        'max_root': 1.0,
        'max_range': 0.5,
        'sample_cause_ranges': False,
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        'min_num_samples': 1000,
        'max_num_samples': 1000,
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        'task': 'regression',
        'sample_std': False,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,
        'train_test_split_ratio': 0.5,
        # 启用SNR方法
        'use_snr_method': False,
        'snr_config': {
            'base_snr': 8.0,
            'depth_decay': 0.85,
            'min_snr': [11.0, 9.0, 7.0, 5.0, 3.0],
            'max_snr': [12.0, 10.0, 8.0, 6.0, 4.0],
            'complexity_weight': 0.3
        },
        'use_monte_carlo_precompute': False
    }

    h_config.update({
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'sample_root_std': False,
        'sample_cause_ranges': False
    })

    intervention_node_type = 'single_child'
    intervention_value_method = 'sample'
    n = 1 # 每种方式生成数据集数量

    custom_dag_type = 'random_SF'
    
    # 示例2：生成扰动数据集，使用不同的不可观测节点设置
    print("Generating perturbed datasets with different unobserved node settings...")

    parent_snr = 5 # Y的父节点到Y的边的SNR
    other_snr = 5 
    
#     custom_functions= {
#         'X': {
#             'type': 'linear',
#             'bias': 0.0,
#             'target_snr': parent_snr
#         },
#         'Y': {  
#             'type': 'gaussian_process',
#             'lengthscale': 1.5,
#             'f_magn': 2.0,
#             'target_snr': other_snr
#         },
#         'Yspouse_Ychild': {  # Y的配偶节点到共同孩子的边
#             'type': 'random_neural_network',
#             'hidden_dim': 2,
#             'depth': 2,
#             'activation': 'tanh',
#             'target_snr': other_snr
#         },
#         'Yparent_Ysibling': {  # 共同父节点到兄弟节点的边
#             'type': 'random_neural_network',
#             'hidden_dim': 2,
#             'depth': 2,
#             'activation': 'tanh',
#             'target_snr': other_snr
#         },
#         'Ygrandparent_Yparent': {  # 祖父节点到父节点的边
#             'type': 'random_neural_network',
#             'hidden_dim': 2,
#             'depth': 2,
#             'activation': 'tanh',
#             'target_snr': other_snr
#         },
#         'Other': {  # 其他所有边
#             'type': 'random_neural_network',
#             'hidden_dim': 2,
#             'depth': 2,
#             'activation': 'tanh',
#             'target_snr': other_snr
#         }
# }
    
    custom_functions= {
    'target': {
        'type': 'linear',
        'bias': 0.0,
        'target_snr': 5
    },
    'target_child': {
        'type': 'random_neural_network',
            'hidden_dim': 2,
            'depth': 2,
            'activation': 'tanh',
            'target_snr': 10
    },
    'Other_type': {
        'type': 'gaussian_process',
        'lengthscale': 1.5,
        'f_magn': 2.0,
        'target_snr': 20
    }
}
                    
    
    # # 扰动节点可观测
    perturbed_datasets_intervention_unobserved = generate_datasets(
        num_dataset=n,
        h_config=h_config,
        perturbation_type='counterfactual',
        perturbation_node_type=intervention_node_type,
        perturbation_value_method=intervention_value_method,
        custom_dag_type=custom_dag_type,
        custom_functions=custom_functions,
        custom_dag_size=None,
        node_unobserved=False,
        seed=42,
        allow_skip=True
    )
    #
    # # 目标节点的所有父节点不可观测
    # perturbed_datasets_all_parents_unobserved = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     perturbation_type='counterfactual',
    #     perturbation_node_type=intervention_node_type,
    #     perturbation_value_method=intervention_value_method,
    #     custom_dag_type='random_SF',
    #     node_unobserved='parents',  # 目标节点的所有父节点不可观测
    #     # seed=42  # 使用相同的种子以便比较
    # )
    #
    # # 目标节点的2个父节点不可观测
    # perturbed_datasets_two_parents_unobserved = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     perturbation_type='counterfactual',
    #     perturbation_node_type=intervention_node_type,
    #     perturbation_value_method=intervention_value_method,
    #     custom_dag_type='random_SF',
    #     node_unobserved='parents_2',  # 目标节点的2个父节点不可观测
    #     # seed=42  # 使用相同的种子以便比较
    # )
    #
    # # 目标节点的50%的父节点不可观测
    # perturbed_datasets_half_parents_unobserved = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     perturbation_type='counterfactual',
    #     perturbation_node_type=intervention_node_type,
    #     perturbation_value_method=intervention_value_method,
    #     custom_dag_type='random_SF',
    #     node_unobserved='parents_ratio_0.5',  # 目标节点的50%的父节点不可观测
    #     # seed=42  # 使用相同的种子以便比较
    # )
    #
    # # 随机选择3个节点不可观测
    # perturbed_datasets_random_unobserved = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     perturbation_type='counterfactual',
    #     perturbation_node_type=intervention_node_type,
    #     perturbation_value_method=intervention_value_method,
    #     custom_dag_type='random_SF',
    #     node_unobserved='random_n_3',  # 随机选择3个节点不可观测
    #     # seed=42  # 使用相同的种子以便比较
    # )

    # 如果测试通过，继续生成实际数据集
    # print("\n" + "=" * 60)
    # print("生成实际数据集")
    # print("=" * 60)

    # 示例3：生成干预数据集，使用不同的不可观测节点设置
    # print("Generating intervention datasets with different unobserved node settings...")

    # # 所有节点都可观测
    # intervention_datasets_all_observed = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     intervention_type='counterfactual',
    #     intervention_node_type=intervention_node_type,
    #     intervention_value_method=intervention_value_method,
    #     custom_dag_type='random_ER',  # 使用预定义结构
    #     custom_dag_size='small',
    #     remove_isolated_nodes=False,
    #     node_unobserved=False,  # 所有节点都可观测
    #     seed=42,  # 设置不同的随机种子
    #     seeds_config=None,  # 使用默认种子配置
    #     allow_skip=True
    # )

    # # 干预节点不可观测
    # intervention_datasets_intervention_unobserved = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     intervention_type='counterfactual',
    #     intervention_node_type=intervention_node_type,
    #     intervention_value_method=intervention_value_method,
    #     custom_dag_type='common_cause',  # 使用预定义结构
    #     node_unobserved=False,  # 干预节点不可观测
    #     # seed=43  # 使用相同的种子以便比较
    # )
    #
    # # 所有马尔科夫毯节点不可观测
    # intervention_datasets_all_markov_blanket_unobserved = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     intervention_type='counterfactual',
    #     intervention_node_type=intervention_node_type,
    #     intervention_value_method=intervention_value_method,
    #     custom_dag_type='common_cause',  # 使用预定义结构
    #     node_unobserved='markov_blanket',  # 所有马尔科夫毯节点不可观测
    #     # seed=43  # 使用相同的种子以便比较
    # )
    #
    # # 3个马尔科夫毯节点不可观测
    # intervention_datasets_three_markov_blanket_unobserved = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     intervention_type='counterfactual',
    #     intervention_node_type=intervention_node_type,
    #     intervention_value_method=intervention_value_method,
    #     custom_dag_type='common_cause',  # 使用预定义结构
    #     node_unobserved='markov_blanket_3',  # 3个马尔科夫毯节点不可观测
    #     # seed=43  # 使用相同的种子以便比较
    # )
    #
    # # 30%的马尔科夫毯节点不可观测
    # intervention_datasets_partial_markov_blanket_unobserved = generate_datasets(
    #     num_dataset=1,
    #     h_config=h_config,
    #     intervention_type='counterfactual',
    #     intervention_node_type=intervention_node_type,
    #     intervention_value_method=intervention_value_method,
    #     custom_dag_type='common_cause',  # 使用预定义结构
    #     node_unobserved='markov_blanket_ratio_0.3',  # 30%的马尔科夫毯节点不可观测
    #     # seed=43  # 使用相同的种子以便比较
    # )
#     # ['single_parent', 'single_child', 'single_sibling', 'single_spouse', 'single_grandparent',
#     # 'all_parents', 'all_children', 'all_siblings', 'all_spouses', 'all_grandparents', 'all_family',
#     # 'all_non_parents', 'all_non_children', 'all_non_siblings','all_non_spouses', 'all_non_grandparents', 'all_non_family']
    # 可选：保存标准数据集到CSV文件
    # save_datasets_to_csv(standard_datasets, './data/standard')
    # 保存所有数据集 - 使用时间戳目录
    # save_datasets_to_csv(perturbed_datasets_all_observed, './data/perturbed_all_observed')
    # save_datasets_to_csv(perturbed_datasets_intervention_unobserved, './data/perturbed_intervention_unobserved')
    # save_datasets_to_csv(perturbed_datasets_all_parents_unobserved, './data/perturbed_all_parents_unobserved')
    # save_datasets_to_csv(perturbed_datasets_two_parents_unobserved, './data/perturbed_two_parents_unobserved')
    # save_datasets_to_csv(perturbed_datasets_half_parents_unobserved, './data/perturbed_half_parents_unobserved')
    # save_datasets_to_csv(perturbed_datasets_random_unobserved, './data/perturbed_random_unobserved')
    #
    # save_datasets_to_csv(intervention_datasets_all_observed, './data/intervention_all_observed', use_timestamp=True)
    # save_datasets_to_csv(intervention_datasets_intervention_unobserved, './data/intervention_intervention_unobserved')
    # save_datasets_to_csv(intervention_datasets_all_markov_blanket_unobserved, './data/intervention_all_markov_blanket_unobserved')
    # save_datasets_to_csv(intervention_datasets_three_markov_blanket_unobserved, './data/intervention_three_markov_blanket_unobserved')
    # save_datasets_to_csv(intervention_datasets_partial_markov_blanket_unobserved, './data/intervention_partial_markov_blanket_unobserved')

    print("Dataset generation complete!")

    # ========================================================================
    # SNR-based Noise Calculation Usage Examples
    # ========================================================================

    # 要使用新的SNR-based噪声计算方法，请按以下步骤操作：

    # 1. 在h_config中启用SNR方法：
    # h_config['use_snr_method'] = True

    # 2. 配置SNR参数（可选，有默认值）：
    # h_config['snr_config'] = {
    #     'base_snr': 8.0,                    # 根节点基准SNR
    #     'depth_decay': 0.85,                # 深度衰减因子
    #     'min_snr': [11.0, 9.0, 7.0, 5.0, 3.0],  # 各深度最小SNR限制
    #     'max_snr': [12.0, 10.0, 8.0, 6.0, 4.0], # 各深度最大SNR限制
    #     'complexity_weight': 0.3          # 复杂度权重
    # }

    # 3. 生成数据集：
    # snr_datasets = generate_datasets(
    #     num_dataset=5,
    #     h_config=h_config,  # 包含SNR配置的h_config
    #     custom_dag_type='random_ER',
    #     seed=42,
    #     allow_skip=True
    # )

    # 4. 分析和验证（可选）：
    # from noise_snr_calculator import SNRValidator
    # from data_quality_analyzer import GraphComplexityAnalyzer, DataQualityEvaluator

    # validator = SNRValidator()
    # complexity_analyzer = GraphComplexityAnalyzer()
    # quality_evaluator = DataQualityEvaluator()

    # 对于每个生成的数据集：
    # dataset_name, x_data, y_data, scm = dataset[0], dataset[1], dataset[2], dataset[-1]
    # complexity_report = complexity_analyzer.generate_complexity_report(scm.dag)
    # quality_report = quality_evaluator.generate_quality_report(x_data, y_data)

    # 详细使用示例请参考 example_snr_noise.py 文件

    # 测试种子系统的可重现性
    # print("\n" + "="*60)
    # print("测试种子系统可重现性")
    # print("="*60)

    # # 测试1: 仅使用全局种子
    # test_seed_reproducibility(h_config, test_seed=42, test_seeds_config=None)

    # # 测试2: 使用种子配置
    # test_seeds_config = {
    #     'dag_generation': [100],
    #     'scm_model': [200],
    #     'config_generation': [300],
    #     'data_sampling': [400]
    # }
    # test_seed_reproducibility(h_config, test_seed=None, test_seeds_config=test_seeds_config)

    # # 测试3: 混合模式
    # mixed_seeds_config = {
    #     'dag_generation': [500],
    #     'scm_model': None  # 使用全局种子
    # }
    # test_seed_reproducibility(h_config, test_seed=1000, test_seeds_config=mixed_seeds_config)

    # # 演示种子配置系统的使用
    # print("\n" + "="*60)
    # print("演示种子配置系统")
    # print("="*60)

    # # 示例1: 使用全局种子，每个数据集自动获得不同的种子
    # print("\n示例1: 使用全局种子 (seed=100, seeds_config=None)")
    # example_datasets_1 = generate_datasets(
    #     num_dataset=2,
    #     h_config=h_config,
    #     custom_dag_type='common_cause',
    #     seed=100,
    #     seeds_config=None,
    #     allow_skip=True
    # )

    # # 示例2: 使用种子配置，为不同类型指定具体种子列表
    # print("\n示例2: 使用种子配置 (seed=None, seeds_config指定)")
    # seeds_config_example = {
    #     'dag_generation': [200, 201],  # 为前两个数据集指定DAG生成种子
    #     'scm_model': [300, 301],       # 为前两个数据集指定SCM模型种子
    #     'config_generation': None,      # 配置生成使用随机种子
    #     'data_sampling': [500, 501]    # 为前两个数据集指定数据采样种子
    # }
    # example_datasets_2 = generate_datasets(
    #     num_dataset=2,
    #     h_config=h_config,
    #     custom_dag_type='common_cause',
    #     seed=None,
    #     seeds_config=seeds_config_example,
    #     allow_skip=True
    # )

    # # 示例3: 混合模式，全局种子+种子配置
    # print("\n示例3: 混合模式 (seed=1000, seeds_config部分指定)")
    # seeds_config_mixed = {
    #     'dag_generation': [2000, 2001],  # 为DAG生成指定具体种子
    #     'scm_model': None,               # SCM模型使用全局种子+偏移
    #     # config_generation和data_sampling未指定，将使用全局种子+偏移
    # }
    # example_datasets_3 = generate_datasets(
    #     num_dataset=2,
    #     h_config=h_config,
    #     custom_dag_type='common_cause',
    #     seed=1000,
    #     seeds_config=seeds_config_mixed,
    #     allow_skip=True
    # )

    # print("\n种子配置系统演示完成!")

    # 种子配置系统说明:
    # ================
    #
    # 种子系统有四种情况:
    # 1. seed=None, seeds_config=None: 所有随机操作使用随机种子
    # 2. seed=None, seeds_config=指定: 使用seeds_config中的种子列表，未指定的类型使用随机种子
    # 3. seed=指定, seeds_config=None: 每个数据集使用全局种子+类型偏移+数据集索引
    # 4. seed=指定, seeds_config=指定: 优先使用seeds_config，未指定的类型使用全局种子+偏移
    #
    # 支持的种子类型:
    # - 'dag_generation': DAG图结构生成
    # - 'scm_model': SCM模型内部随机操作(节点赋值函数、噪声生成等)
    # - 'config_generation': 配置参数采样(节点数、噪声标准差等)
    # - 'data_sampling': 数据采样过程
    #
    # 种子偏移量设计(简化版):
    # - dag_generation: +100000 (10万)
    # - scm_model: +200000 (20万)
    # - config_generation: +300000 (30万)
    # - data_sampling: +400000 (40万)
    #
    # 种子生成策略:
    # - 使用简单递增方式: seed + offset + index
    # - 相邻种子差异为1，但随机序列相关性低(平均0.035)
    # - 完全可重现，跨平台一致
    # - 简单易懂，便于调试和维护


