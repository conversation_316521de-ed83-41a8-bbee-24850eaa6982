# 导入所需的库
import os
import igraph as ig  # 用于图操作的库
import numpy as np
from scipy.stats import truncnorm  # 用于生成截断正态分布
from pgmpy.models.BayesianNetwork import BayesianNetwork as BayesianModel  # 贝叶斯网络模型
from pgmpy.factors.discrete import TabularCPD  # 用于表示条件概率分布
from pgmpy.sampling import BayesianModelSampling  # 用于贝叶斯网络采样
import networkx as nx  # 用于图的可视化
from networkx.drawing.nx_agraph import graphviz_layout
import matplotlib.pyplot as plt
from Tools import Graph  # 自定义的图工具类

# 定义数据集的基本参数
# 每个规模类别的图的数量
numeachclass = {'small': 50, 'medium': 50, 'large': 50, 'vlarge': 50}
# 每个规模类别的节点数范围
nodenumclass = {'small': (11, 20), 'medium': (21, 50), 'large': (51, 100), 'vlarge': (101, 1000)}
# 平均入度(也等于平均出度)的范围
avgInDegree = (1.2, 1.7)  # == avgOutDegree == #Edges/#Nodes, lower/upper bound


def is_dag(W):
    """
    检查邻接矩阵W是否表示一个DAG
    Args:
        W: 邻接矩阵
    Returns:
        bool: 是否为DAG
    """
    G = ig.Graph.Weighted_Adjacency(W.tolist())
    return G.is_dag()


def simulate_dag(d, s0, graph_type):
    """
    生成随机DAG
    Args:
        d (int): 节点数量
        s0 (int): 期望的边数量
        graph_type (str): 图类型，'ER'或'SF'
    Returns:
        np.ndarray: DAG的二值邻接矩阵
    """

    def _random_permutation(M):
        """随机置换矩阵的行和列"""
        P = np.random.permutation(np.eye(M.shape[0]))
        return P.T @ M @ P

    def _acyclic_orientation(B_und):
        """将无向图转换为有向无环图"""
        return np.tril(B_und, k=-1)

    def _remove_isolating_node(B):
        """移除孤立节点"""
        non_iso_index = np.logical_or(B.any(axis=0), B.any(axis=1))
        return B[non_iso_index][:, non_iso_index]

    def _graph_to_adjmat(G):
        """将图对象转换为邻接矩阵"""
        return np.array(G.get_adjacency().data)

    # 根据指定类型生成无向图
    if graph_type == 'ER':  # Erdős-Rényi随机图
        G_und = ig.Graph.Erdos_Renyi(n=d, m=s0)
    elif graph_type == 'SF':  # Scale-free无标度网络
        G_und = ig.Graph.Barabasi(n=d, m=int(round(s0 / d)), directed=False, outpref=True, power=-3)
    else:
        raise ValueError('unknown graph type')

    # 将无向图转换为DAG
    B_und = _graph_to_adjmat(G_und)
    B_und = _random_permutation(B_und)
    B = _acyclic_orientation(B_und)
    B = _remove_isolating_node(B)
    B_perm = _random_permutation(B).astype(int)
    assert ig.Graph.Adjacency(B_perm.tolist()).is_dag()
    return B_perm


def simulate_cards(B, card_param=None):
    """
    为DAG中的每个节点生成基数(即离散变量的可能取值数量)
    Args:
        B: DAG的邻接矩阵
        card_param: 基数生成的参数
    Returns:
        np.ndarray: 每个节点的基数
    """
    if card_param == None:
        # 默认参数设置
        card_param = {'lower': 2, 'upper': 6, 'mu': 2, 'basesigma': 1.5}

    def _max_peers():
        """计算每个节点作为父节点时最大的同级节点数"""
        in_degrees = B.sum(axis=0)
        peers_num = in_degrees[:, None] * B.T
        return peers_num.max(axis=0)

    # 从截断正态分布生成基数
    lower, upper, mu, basesigma = card_param['lower'], card_param['upper'], card_param['mu'], card_param['basesigma']
    sigma = basesigma / np.exp2(_max_peers())
    cards = truncnorm((lower - mu) / sigma, (upper - mu) / sigma, loc=mu, scale=sigma). \
        rvs(size=B.shape[0]).round().astype(int)
    return cards


def simulate_discrete(B, n, card_param=None, alpha_param=None):
    """
    生成离散数据
    Args:
        B: DAG的邻接矩阵
        n: 样本数量
        card_param: 基数参数
        alpha_param: Dirichlet分布的参数
    Returns:
        np.ndarray: 生成的离散数据
    """
    if alpha_param == None:
        alpha_param = {'lower': 0.1, 'upper': 1.0}

    def _random_alpha():
        """生成随机的Dirichlet分布参数"""
        return np.random.uniform(alpha_param['lower'], alpha_param['upper'])

    def _dirichlet(alpha, size):
        """生成Dirichlet分布的概率向量"""
        probs = np.random.dirichlet(np.ones(size) * alpha)
        probs[-1] = 1 - probs[:-1].sum()
        return probs

    # 生成节点基数
    cards = simulate_cards(B, card_param=card_param)
    # 创建贝叶斯网络
    diEdges = list(map(lambda x: (str(x[0]), str(x[1])), np.argwhere(B == 1)))
    bn = BayesianModel(diEdges)

    # 为每个节点生成条件概率表
    for node in range(len(cards)):
        parents = np.where(B[:, node] == 1)[0].tolist()
        parents_card = [cards[prt] for prt in parents]
        rand_ps = np.array([_dirichlet(_random_alpha(), cards[node])
                            for _ in range(int(np.prod(parents_card)))]).T.tolist()
        cpd = TabularCPD(str(node), cards[node], rand_ps,
                         evidence=list(map(str, parents)), evidence_card=parents_card)
        bn.add_cpds(cpd)

    # 使用前向采样生成数据
    inference = BayesianModelSampling(bn)
    df = inference.forward_sample(size=n)
    # 调整列顺序以匹配原始节点顺序
    topo_order = list(map(int, df.columns))
    topo_index = [-1] * len(topo_order)
    for ind, node in enumerate(topo_order):
        topo_index[node] = ind
    return df.to_numpy()[:, topo_index].astype(int)


def simulate_graphs():
    """
    生成并保存图结构
    """
    # 创建保存目录
    os.makedirs('./synthetics/graph/', exist_ok=True)
    os.makedirs('./synthetics/graph/imgs/', exist_ok=True)

    # 为每个规模类别和图类型生成图
    for cname in nodenumclass.keys():
        lower, upper = nodenumclass[cname]
        for gtype in ['ER', 'SF']:
            for id in range(numeachclass[cname]):
                synname = f'{cname}_{gtype}_{id}'
                print('now simulating graph structure for', synname)

                # 生成随机DAG
                d = np.random.randint(lower, upper)
                s0 = np.round(np.random.uniform(avgInDegree[0], avgInDegree[1]) * d).astype(int)
                B = simulate_dag(d, s0, gtype)

                # 保存邻接矩阵
                graphtxtpath = f'./synthetics/graph/{synname}.txt'
                np.savetxt(graphtxtpath, B, fmt='%i')

                # 分析图结构
                dig = Graph.DiGraph(graphtxtpath)
                tforks, vstrucs, allIdentifiableEdges = dig.tforks, dig.vstrucs, dig.IdentifiableEdges

                # 准备可视化
                DirectedEdges = list(map(tuple, np.argwhere(B == 1)))
                NodeIDs = list(range(len(B)))
                vedges = set()
                for vv in vstrucs:
                    vedges.add((vv[1], vv[0]))
                    vedges.add((vv[2], vv[0]))

                # 使用networkx绘制图
                G = nx.DiGraph()
                G.add_edges_from(DirectedEdges)
                G.add_nodes_from(NodeIDs)
                pos = graphviz_layout(G, prog='dot')
                nx.draw_networkx_nodes(G, pos, node_color='lightblue')
                nx.draw_networkx_labels(G, pos, font_size=7)
                nx.draw_networkx_edges(G, pos, edgelist=set(DirectedEdges) - vedges)
                nx.draw_networkx_edges(G, pos, edgelist=vedges, edge_color='green')

                # 保存图像
                plt.title(f'{synname}, {len(NodeIDs)} nodes, {len(DirectedEdges)} edges, '
                          f'{len(vstrucs)} V, {len(tforks) - len(vstrucs)} nonV')
                plt.tight_layout()
                plt.savefig(f'./synthetics/graph/imgs/{synname}.png')
                plt.clf()


def simulate_data_discrt():
    """
    为已生成的图结构生成离散数据
    """
    os.makedirs('./synthetics/data/', exist_ok=True)
    for cname in nodenumclass.keys():
        for gtype in ['ER', 'SF']:
            for id in range(numeachclass[cname]):
                synname = f'{cname}_{gtype}_{id}'
                print('now forward sampling data for', synname)
                # 读取图结构并生成数据
                B = np.loadtxt(f'./synthetics/graph/{synname}.txt')
                np.save(f'./synthetics/data/{synname}.npy', simulate_discrete(B, n=10000))


if __name__ == '__main__':
    # 生成训练用的合成数据
    simulate_graphs()  # 首先生成图结构
    simulate_data_discrt()  # 然后生成对应的离散数据
