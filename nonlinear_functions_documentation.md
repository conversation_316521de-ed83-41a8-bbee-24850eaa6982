# 非线性自定义函数文档

本文档描述了新增的非线性自定义函数类型，包括可逆和不可逆两大类函数。

## 函数分类

### 可逆函数 (Invertible Functions)

这些函数在其定义域内是单调的，因此是可逆的。

#### 1. 多项式函数 (polynomial)
- **类型**: `polynomial`
- **数学形式**: `y = a1*x1^p1 + a2*x2^p2 + ... + b`
- **可逆性**: 使用奇次幂保证可逆性
- **参数**:
  - `coefficients`: 系数列表 [a1, a2, ...]
  - `powers`: 幂次列表 [p1, p2, ...] (自动确保为奇数)
  - `bias`: 偏置项 b
  - `noise_std` 或 `target_snr`: 噪声配置

**示例配置**:
```python
{
    'type': 'polynomial',
    'coefficients': [1.5, 1.0],
    'powers': [3, 5],  # 奇次幂保证可逆性
    'bias': 0.1,
    'noise_std': 0.05
}
```

#### 2. 指数函数 (exponential)
- **类型**: `exponential`
- **数学形式**: `y = a1*exp(b1*x1) + a2*exp(b2*x2) + ... + c`
- **可逆性**: 指数函数单调递增
- **参数**:
  - `coefficients`: 外层系数 [a1, a2, ...]
  - `exp_coefficients`: 指数系数 [b1, b2, ...]
  - `bias`: 偏置项 c
  - `noise_std` 或 `target_snr`: 噪声配置

**示例配置**:
```python
{
    'type': 'exponential',
    'coefficients': [0.5, 0.4],
    'exp_coefficients': [0.3, 0.25],
    'bias': 0.1,
    'noise_std': 0.05
}
```

#### 3. 对数函数 (logarithmic)
- **类型**: `logarithmic`
- **数学形式**: `y = a1*log(b1*|x1|+1) + a2*log(b2*|x2|+1) + ... + c`
- **可逆性**: 对数函数单调递增
- **参数**:
  - `coefficients`: 外层系数 [a1, a2, ...]
  - `log_coefficients`: 对数内部系数 [b1, b2, ...]
  - `bias`: 偏置项 c
  - `noise_std` 或 `target_snr`: 噪声配置

**示例配置**:
```python
{
    'type': 'logarithmic',
    'coefficients': [1.0, 0.8],
    'log_coefficients': [1.5, 1.2],
    'bias': 0.0,
    'target_snr': 15.0
}
```

#### 4. 缩放Sigmoid函数 (sigmoid_scaled)
- **类型**: `sigmoid_scaled`
- **数学形式**: `y = a1*sigmoid(b1*x1) + a2*sigmoid(b2*x2) + ... + c`
- **可逆性**: Sigmoid函数单调递增
- **参数**:
  - `coefficients`: 外层系数 [a1, a2, ...]
  - `scale_coefficients`: 缩放系数 [b1, b2, ...]
  - `bias`: 偏置项 c
  - `noise_std` 或 `target_snr`: 噪声配置

#### 5. 缩放Tanh函数 (tanh_scaled)
- **类型**: `tanh_scaled`
- **数学形式**: `y = a1*tanh(b1*x1) + a2*tanh(b2*x2) + ... + c`
- **可逆性**: Tanh函数单调递增
- **参数**:
  - `coefficients`: 外层系数 [a1, a2, ...]
  - `scale_coefficients`: 缩放系数 [b1, b2, ...]
  - `bias`: 偏置项 c
  - `noise_std` 或 `target_snr`: 噪声配置

### 不可逆函数 (Non-Invertible Functions)

这些函数不是单调的，因此不可逆。

#### 1. 偶次多项式函数 (polynomial_even)
- **类型**: `polynomial_even`
- **数学形式**: `y = a1*x1^p1 + a2*x2^p2 + ... + b`
- **不可逆性**: 使用偶次幂
- **参数**:
  - `coefficients`: 系数列表 [a1, a2, ...]
  - `powers`: 幂次列表 [p1, p2, ...] (自动确保为偶数)
  - `bias`: 偏置项 b
  - `noise_std` 或 `target_snr`: 噪声配置

#### 2. 正弦函数 (sine)
- **类型**: `sine`
- **数学形式**: `y = a1*sin(b1*x1) + a2*sin(b2*x2) + ... + c`
- **不可逆性**: 正弦函数周期性
- **参数**:
  - `coefficients`: 振幅系数 [a1, a2, ...]
  - `frequency_coefficients`: 频率系数 [b1, b2, ...]
  - `bias`: 偏置项 c
  - `noise_std` 或 `target_snr`: 噪声配置

#### 3. 余弦函数 (cosine)
- **类型**: `cosine`
- **数学形式**: `y = a1*cos(b1*x1) + a2*cos(b2*x2) + ... + c`
- **不可逆性**: 余弦函数周期性
- **参数**: 与正弦函数相同

#### 4. ReLU+二次项组合 (relu_quadratic)
- **类型**: `relu_quadratic`
- **数学形式**: `y = a1*ReLU(x1) + b1*x1^2 + a2*ReLU(x2) + b2*x2^2 + ... + c`
- **不可逆性**: ReLU的非线性和二次项的组合
- **参数**:
  - `relu_coefficients`: ReLU项系数 [a1, a2, ...]
  - `quad_coefficients`: 二次项系数 [b1, b2, ...]
  - `bias`: 偏置项 c
  - `noise_std` 或 `target_snr`: 噪声配置

#### 5. 高斯径向基函数 (gaussian_rbf)
- **类型**: `gaussian_rbf`
- **数学形式**: `y = a1*exp(-b1*(x1-c1)^2) + a2*exp(-b2*(x2-c2)^2) + ... + d`
- **不可逆性**: 高斯函数的钟形特性
- **参数**:
  - `coefficients`: 振幅系数 [a1, a2, ...]
  - `width_coefficients`: 宽度系数 [b1, b2, ...]
  - `centers`: 中心点 [c1, c2, ...]
  - `bias`: 偏置项 d
  - `noise_std` 或 `target_snr`: 噪声配置

## 配置示例

### 在test_correct_snr_validation.py中的配置

文件中现在包含以下配置类型：

1. **原始线性配置**: `markov_noise` - 保留原有的线性函数配置
2. **可逆非线性配置**: 
   - `polynomial_invertible` - 多项式可逆函数
   - `exponential_invertible` - 指数可逆函数
   - `sigmoid_tanh_invertible` - Sigmoid/Tanh可逆函数
3. **不可逆非线性配置**:
   - `polynomial_non_invertible` - 多项式不可逆函数
   - `trigonometric_non_invertible` - 三角函数不可逆
   - `complex_non_invertible` - 复杂不可逆函数
4. **混合配置**: `mixed_functions_with_snr` - 不同函数类型+目标SNR
5. **基准配置**: `linear_baseline` - 线性基准函数

### 注释掉的原始配置

所有之前的配置都被保留在注释中，包括：
- `non_markov_noise`
- `fixed_same_noise` 
- `target_snr`
- `mixed_config`

## 使用方法

1. 在`custom_functions_configs`中选择或取消注释想要使用的配置
2. 运行测试脚本查看不同函数类型的效果
3. 可以根据需要修改参数或添加新的配置

## 扩展性

系统设计具有良好的扩展性，可以轻松添加新的函数类型：
1. 在`multi_scm_model.py`的`_setup_custom_function`方法中添加新的函数类型
2. 创建对应的PyTorch模块类
3. 在配置文件中添加相应的配置示例
