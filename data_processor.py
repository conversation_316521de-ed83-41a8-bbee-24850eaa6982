import torch
from data_processor_utils import DataProcessor
from typing import List, Tuple
import numpy as np

def process_datasets(test_datasets: List, 
                    train_size: float = 0.5, 
                    device: str = 'cpu',
                    n_sigma: int = 3,
                    min_lb: int = -5,
                    max_up: int = 5,
                    discretize_features: List[int] = None,
                    num_classes: int = None) -> Tuple:
    """
    处理test_datasets中的每个数据集，返回处理后的训练集和测试集
    
    参数:
        test_datasets: 数据集列表，每个元素为[dataset_name, x_original, y_original, x_intervened, y_intervened, config]
        train_size: 训练集比例
        device: 计算设备(cpu/cuda)
        n_sigma: 异常值处理的sigma倍数
        min_lb: 截断下限
        max_up: 截断上限
        discretize_features: 需要离散化的特征索引列表
        num_classes: 离散化的类别数
    
    返回:
        Tuple: (train_x, train_y, test0_x, test0_y, test1_x, test1_y)
    """
    processor = DataProcessor(device=device, n_sigma=n_sigma, min_lb=min_lb, max_up=max_up)
    
    results = []
    for ds in test_datasets:
        # 数据集划分
        x_orig, y_orig = ds[1], ds[2]
        x_interv, y_interv = ds[3], ds[4]
        
        # 原始数据划分训练集和测试集0
        eval_position = int(x_orig.shape[0] * train_size)
        train_x, train_y = x_orig[:eval_position], y_orig[:eval_position]
        test0_x, test0_y = x_orig[eval_position:], y_orig[eval_position:]
        
        # 干预数据采样测试集1
        sample_size = test0_x.shape[0]
        idx = torch.randperm(x_interv.shape[0])[:sample_size]
        test1_x, test1_y = x_interv[idx], y_interv[idx]
        
        # 标准化
        train_x = processor.standardize(train_x, fit=True)
        test0_x = processor.standardize(test0_x)
        test1_x = processor.standardize(test1_x)
        
        # 截断
        train_x = processor.clip_data(train_x)
        test0_x = processor.clip_data(test0_x)
        test1_x = processor.clip_data(test1_x)
        
        # 异常值处理
        train_x = processor.remove_outliers(train_x, fit=True)
        test0_x = processor.remove_outliers(test0_x)
        test1_x = processor.remove_outliers(test1_x)
        
        # 离散化
        if discretize_features is not None:
            for feat_idx in discretize_features:
                # 训练集离散化
                d_train, class_boundaries = processor.discretize(
                    train_x[:, feat_idx], num_classes=num_classes)
                train_x[:, feat_idx] = d_train.float()
                
                # 测试集使用相同的class_boundaries
                d_test0, _ = processor.discretize(
                    test0_x[:, feat_idx], class_boundaries=class_boundaries)
                d_test1, _ = processor.discretize(
                    test1_x[:, feat_idx], class_boundaries=class_boundaries)
                test0_x[:, feat_idx] = d_test0.float()
                test1_x[:, feat_idx] = d_test1.float()
        
        results.append((train_x, train_y, test0_x, test0_y, test1_x, test1_y))
    
    return results