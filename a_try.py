import os
import glob
import pickle
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from sklearn.model_selection import train_test_split
from sklearn.metrics import root_mean_squared_error, r2_score
import torch
from pathlib import Path
from inference.regressor import TabPFNRegressor
from inference.regressor_mse import TabPFNRegressor_mse
from inference.regressor_muzero import TabPFNRegressor_muzero
import re

# 确保 GPU 可用
if not torch.cuda.is_available():
    raise SystemError('GPU device not found. For fast training, please enable GPU.')

def fuse_model_weights(original_model_path, default_model_path, save_path):
    """
    融合模型权重的函数 - 学习自原代码的model_path_save函数
    Args:
        original_model_path: 原始模型权重路径 (mse或muzero)
        default_model_path: 默认模型权重路径
        save_path: 融合后模型保存路径
    """
    try:
        if not Path(original_model_path).exists():
            print(f"Warning: {original_model_path} does not exist, skipping fusion")
            return None
            
        if not Path(default_model_path).exists():
            print(f"Warning: {default_model_path} does not exist, skipping fusion")
            return None
            
        # 加载原始模型和默认模型 - 参考原代码逻辑
        model_m = torch.load(open(original_model_path, 'rb'))   
        model_d = torch.load(open(default_model_path, 'rb')) 
        
        # 提取原始模型的状态字典 - 参考原代码
        my_stat = model_m[0]    
        new_stat = {k.replace('module.', ''): v for k, v in my_stat.items()}    
        
        # 融合权重 - 参考原代码逻辑
        for k in model_d['state_dict'].keys():
            if k in new_stat:
                model_d['state_dict'][k] = new_stat[k]
            else:
                print(f"[!] Warning: {k} not found in new model params")
        
        # 保存融合后的模型
        torch.save(model_d, open(save_path, 'wb'))
        print(f"Successfully fused and saved model to {save_path}")
        return save_path
        
    except Exception as e:
        print(f"Error fusing model weights for {original_model_path}: {e}")
        return None

def create_simulation_data(n_samples=1000, n_features=10, noise_level=0.1, random_state=42):
    """
    创建模拟数据集
    """
    np.random.seed(random_state)
    
    # 生成特征
    X = np.random.randn(n_samples, n_features)
    
    # 生成目标变量（非线性关系）
    y = (X[:, 0] ** 2 + 
         np.sin(X[:, 1]) * 2 + 
         X[:, 2] * X[:, 3] + 
         np.exp(X[:, 4] * 0.5) +
         np.sum(X[:, 5:], axis=1) * 0.3 +
         np.random.normal(0, noise_level, n_samples))
    
    return X, y

def safe_model_prediction(model_class, model_path, X_train, y_train, X_test, model_name):
    """
    安全地进行模型预测，如果失败则跳过
    """
    try:
        if model_path is None:
            print(f"Skipping {model_name}: model path is None")
            return None, None
            
        # 创建模型实例
        model = model_class(random_state=42, ignore_pretraining_limits=True, model_path=model_path)
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 预测
        y_pred = model.predict(X_test)
        
        print(f"✅ {model_name} prediction successful")
        return y_pred, model
        
    except Exception as e:
        print(f"❌ {model_name} failed: {e}")
        return None, None

def process_simulation_dataset(X, y, dataset_name="Simulation"):
    """
    处理模拟数据集：训练三种 TabPFN 模型，计算 RMSE 和 R²
    """
    print(f"\n=== Processing {dataset_name} Dataset ===")
    
    # 训练测试拆分
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.33, random_state=42)
    
    # 标准化目标变量
    y_mean = y_train.mean()
    y_std = y_train.std()
    y_train_normalized = (y_train - y_mean) / y_std
    y_test_normalized = (y_test - y_mean) / y_std
    
    # 记录数据集大小和特征数
    sample_size, feature_count = X.shape
    print(f"Dataset size: {sample_size} samples, {feature_count} features")
    
    # 定义模型路径
    mse_model_path = '/root/TabPFN-v2/test_regression/mse_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
    muzero_model_path = '/root/TabPFN-v2/test_regression/muzero_prior_diff_real_checkpoint_n_0_epoch_1000.ckpt'
    model_default = "/root/LDM_test/tabpfn-v2-regressor.ckpt"
    
    # 融合模型权重
    print("\n--- Fusing Model Weights ---")
    mse_fused_path = fuse_model_weights(mse_model_path, model_default, './mse_fused_model.ckpt')
    muzero_fused_path = fuse_model_weights(muzero_model_path, model_default, './muzero_fused_model.ckpt')
    
    # 定义模型配置
    models_config = [
        (TabPFNRegressor, model_default, "TabPFN_default"),
        (TabPFNRegressor_mse, mse_fused_path, "TabPFN_mse"),
        (TabPFNRegressor_muzero, muzero_fused_path, "TabPFN_muzero")
    ]
    
    predictions = {}
    
    print("\n--- Training and Predicting ---")
    
    # 对每个模型进行预测
    for model_class, model_path, model_name in models_config:
        y_pred, trained_model = safe_model_prediction(
            model_class, model_path, X_train, y_train_normalized, X_test, model_name
        )
        
        if y_pred is not None:
            # 计算 RMSE 和 R²
            rmse = root_mean_squared_error(y_test_normalized, y_pred)
            r2 = r2_score(y_test_normalized, y_pred)
            
            predictions[model_name] = y_pred
            
            print(f"{model_name} - RMSE: {rmse:.4f}, R²: {r2:.4f}")
        else:
            # 模型失败
            predictions[model_name] = None
            print(f"{model_name} - Failed to train/predict")
    
    return predictions, y_test_normalized

def create_comparison_plot(predictions, y_test, dataset_name="Simulation"):
    """
    创建预测结果对比图
    """
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle(f'{dataset_name} Dataset - Prediction Comparison', fontsize=16)
    
    model_names = ["TabPFN_default", "TabPFN_mse", "TabPFN_muzero"] #
    
    for i, model_name in enumerate(model_names):
        ax = axes[i]
        
        if predictions[model_name] is not None:
            y_pred = predictions[model_name]
            ax.scatter(y_test, y_pred, alpha=0.6, s=20)
            ax.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
            ax.set_xlabel('True Values')
            ax.set_ylabel('Predicted Values')
            ax.set_title(f'{model_name}')
            
            # 计算并显示 R²
            r2 = r2_score(y_test, y_pred)
            ax.text(0.05, 0.95, f'R² = {r2:.4f}', transform=ax.transAxes, 
                   bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.8))
        else:
            ax.text(0.5, 0.5, 'Model Failed', transform=ax.transAxes, 
                   ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f'{model_name} (Failed)')
    
    plt.tight_layout()
    return fig

# 主程序
if __name__ == "__main__":
    # 创建结果目录
    results_dir = "./simulation_results"
    os.makedirs(results_dir, exist_ok=True)
    
    # 创建多个不同的模拟数据集进行测试
    simulation_configs = [
        {"n_samples": 500, "n_features": 5, "noise_level": 0.1, "name": "Small_LowNoise"},
        {"n_samples": 1000, "n_features": 10, "noise_level": 0.1, "name": "Medium_LowNoise"},
        {"n_samples": 1500, "n_features": 15, "noise_level": 0.2, "name": "Large_HighNoise"},
        {"n_samples": 800, "n_features": 8, "noise_level": 0.05, "name": "Medium_VeryLowNoise"}
    ]
    
    print("🚀 Starting simulation experiments with three TabPFN variants...")
    
    for config in simulation_configs:
        print(f"\n{'='*60}")
        print(f"Creating simulation dataset: {config['name']}")
        
        # 创建模拟数据
        X, y = create_simulation_data(
            n_samples=config['n_samples'],
            n_features=config['n_features'],
            noise_level=config['noise_level'],
            random_state=42
        )
        
        # 处理数据集
        predictions, y_test = process_simulation_dataset(X, y, config['name'])
        
        # 创建对比图
        fig = create_comparison_plot(predictions, y_test, config['name'])
        plot_path = os.path.join(results_dir, f"{config['name']}_comparison.png")
        fig.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"Comparison plot saved to {plot_path}")
    
    print(f"\n🎉 All simulation experiments completed!")
    print(f"📈 Comparison plots saved to: {results_dir}")
