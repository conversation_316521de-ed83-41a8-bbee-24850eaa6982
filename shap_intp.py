"""
TabPFN Regression SHAP Visualization for Original vs Intervened Data
This script demonstrates how to visualize SHAP values for TabPFN regression models
on both original and intervened test sets.
"""

import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score
import shap
import os
from multi_data_generator import generate_datasets
from tabpfn_extensions import TabPFNRegressor, interpretability

# 检查CUDA是否可用并设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# 全局超参数配置
CONFIG = {
    # 数据集生成参数
    'num_datasets': 1,                # 要生成的数据集数量
    'min_samples': 2000,              # 数据集最小样本数
    'max_samples': 10000,              # 数据集最大样本数

    # 数据集生成的SCM参数
    'node_unobserved': False,         # 不可观测节点设置: False, True, 'parents', 'parents_2', 'parents_ratio_0.5', 'random_n_3'等
    'intervention_type': 'counterfactual',  # 干预类型: 'counterfactual', 'noise'等
    'intervention_node_type': 'single_parent', # 干预节点类型
    'intervention_value_method': 'sample',  # 干预值的生成方法
    'custom_dag_type': 'common_cause',   # DAG类型

    # SHAP分析参数
    'max_shap_samples': 2,           # SHAP分析的最大样本数
    'r2_threshold': 0,              # 执行SHAP分析的R²阈值

    # 其他参数
    'seed': None,                       # 随机种子
    'output_dir': 'results',          # 结果保存目录

    # SCM配置参数
    'h_config': {
        'device': 'cpu',  # 先设置为CPU，稍后再转移到GPU
        'min_num_node': 5,
        'max_num_node': 20,
        'num_layers': 3,
        'max_num_children': 10,
        'max_num_classes': 5,
        'min_noise_std': 0.01,
        'max_noise_std': 0.1,
        'min_init_std': 1,
        'max_init_std': 5,
        'min_root': 0.0,
        'max_root': 0.5,
        'max_range': 0.5,
        'min_output_multiclass_ordered_p': 0.0,
        'max_output_multiclass_ordered_p': 0.5,
        'categorical_feature_p': 0,
        'min_drop_node_ratio': 0,
        'max_drop_node_ratio': 0,
        'task': 'regression',
        'intervention_on_no_root': True,
        'sample_std': True,
        'single_effect_last_layer': False,
        'last_layer_fix_num_node': False,
        'num_node_last_layer': 5,
        'intervention_on_observed': True,
        'sample_cause_ranges': True
    }
}

def generate_and_process_datasets():
    """
    生成数据集并进行处理
    """
    try:
        # 更新h_config中的样本数设置
        h_config = CONFIG['h_config'].copy()
        h_config['min_num_samples'] = CONFIG['min_samples']
        h_config['max_num_samples'] = CONFIG['max_samples']

        # 重要：确保数据生成在CPU上进行，避免设备混合问题
        h_config['device'] = 'cpu'

        # 获取不可观测节点设置的描述（用于输出目录名）
        node_unobserved_str = str(CONFIG['node_unobserved']).replace('.', '_')

        # 生成数据集
        print(f"Generating {CONFIG['num_datasets']} datasets with node_unobserved={CONFIG['node_unobserved']}...")
        datasets = generate_datasets(
            num_dataset=CONFIG['num_datasets'],
            h_config=h_config,
            intervention_type=CONFIG['intervention_type'],
            intervention_node_type=CONFIG['intervention_node_type'],
            intervention_value_method=CONFIG['intervention_value_method'],
            custom_dag_type=CONFIG['custom_dag_type'],
            node_unobserved=CONFIG['node_unobserved'],
            seed=CONFIG['seed']
        )

        # 创建结果目录
        output_dir = os.path.join(CONFIG['output_dir'], f"node_unobserved_{node_unobserved_str}")
        os.makedirs(output_dir, exist_ok=True)

        # 逐个处理数据集
        for i, dataset in enumerate(datasets):
            try:
                print(f"\nProcessing dataset {i+1}/{len(datasets)}")

                # 提取数据 - 首先确保所有数据都在CPU上
                dataset_name = dataset[0]

                # 确保所有张量都先在CPU上，避免混合设备问题
                if isinstance(dataset[1], torch.Tensor):
                    xs_original = dataset[1].cpu()
                else:
                    xs_original = torch.tensor(dataset[1], device='cpu')

                if isinstance(dataset[2], torch.Tensor):
                    ys_original = dataset[2].cpu()
                else:
                    ys_original = torch.tensor(dataset[2], device='cpu')

                if isinstance(dataset[3], torch.Tensor):
                    xs_intervention = dataset[3].cpu()
                else:
                    xs_intervention = torch.tensor(dataset[3], device='cpu')

                if isinstance(dataset[4], torch.Tensor):
                    ys_intervention = dataset[4].cpu()
                else:
                    ys_intervention = torch.tensor(dataset[4], device='cpu')

                scm = dataset[5]

                # 转换为numpy进行后续处理
                xs_original_np = xs_original.numpy()
                ys_original_np = ys_original.numpy()
                xs_intervention_np = xs_intervention.numpy()
                ys_intervention_np = ys_intervention.numpy()

                # 按照指定方式分割数据
                eval_position = int(xs_original_np.shape[0] * 0.5)
                train_xs, train_ys = xs_original_np[0:eval_position], ys_original_np[0:eval_position]
                test_xs_original, test_ys_original = xs_original_np[eval_position:], ys_original_np[eval_position:]
                test_xs_intervention, test_ys_intervention = xs_intervention_np[eval_position:], ys_intervention_np[eval_position:]

                # 获取特征名称
                if hasattr(scm, 'selected_features'):
                    feature_names = [f"feature{node}" for node in scm.selected_features]
                else:
                    feature_names = [f"feature{i}" for i in range(xs_original_np.shape[1])]

                # 训练和可视化
                train_and_visualize(
                    train_xs, train_ys.ravel(),
                    test_xs_original, test_ys_original.ravel(),
                    test_xs_intervention, test_ys_intervention.ravel(),
                    feature_names, scm,
                    dataset_name, i, output_dir
                )
            except Exception as e:
                print(f"Error processing dataset {i+1}: {e}")
                import traceback
                traceback.print_exc()
                continue
    except Exception as e:
        print(f"Error in generate_and_process_datasets: {e}")
        import traceback
        traceback.print_exc()
        raise

def train_and_visualize(X_train, y_train, X_test_orig, y_test_orig, X_test_intv, y_test_intv,
                        feature_names, scm, dataset_name, dataset_index, output_dir):
    """
    训练TabPFN回归器并可视化原始和干预测试集的SHAP值
    """
    try:
        # 初始化并训练模型 - 现在可以使用GPU
        print(f"Training TabPFN regressor for dataset {dataset_name}...")
        model = TabPFNRegressor(device=str(device))
        model.fit(X_train, y_train)

        # 评估模型在原始测试集上的性能
        y_pred_orig = model.predict(X_test_orig)
        r2_orig = r2_score(y_test_orig, y_pred_orig)
        print(f"R² score on original test set: {r2_orig:.4f}")

        # 评估模型在干预测试集上的性能
        y_pred_intv = model.predict(X_test_intv)
        r2_intv = r2_score(y_test_intv, y_pred_intv)
        print(f"R² score on intervened test set: {r2_intv:.4f}")

        # 只有当原始测试集的R²大于阈值时才进行SHAP分析
        if r2_orig > CONFIG['r2_threshold']:
            # 获取SCM中的实际节点名称
            if hasattr(scm, 'nodes'):
                # 如果SCM有nodes属性，使用它来获取节点名称
                node_names = [str(node) for node in scm.nodes]
                # 确保只使用与特征数量相匹配的节点名称
                if hasattr(scm, 'selected_features'):
                    # 如果有selected_features，使用它们来获取正确的节点名称
                    feature_names = [str(scm.nodes[node]) for node in scm.selected_features]
                else:
                    # 否则，使用前X个节点名称，其中X是特征数量
                    feature_names = node_names[:X_train.shape[1]]

            print(f"Using feature names: {feature_names}")

            # 计算原始测试集的SHAP值
            print("Calculating SHAP values for original test set...")
            n_samples_shap = min(CONFIG['max_shap_samples'], X_test_orig.shape[0])

            shap_values_orig = interpretability.shap.get_shap_values(
                estimator=model,
                test_x=X_test_orig[:n_samples_shap],
                attribute_names=feature_names,
                algorithm="permutation",
            )

            # 计算干预测试集的SHAP值
            print("Calculating SHAP values for intervened test set...")
            shap_values_intv = interpretability.shap.get_shap_values(
                estimator=model,
                test_x=X_test_intv[:n_samples_shap],
                attribute_names=feature_names,
                algorithm="permutation",
            )

            # 创建可视化
            plt.figure(figsize=(20, 10))

            # 绘制原始测试集的SHAP值
            plt.subplot(1, 2, 1)
            fig1 = interpretability.shap.plot_shap(shap_values_orig)
            plt.title(f"Original Test Set (R²={r2_orig:.4f})")

            # 绘制干预测试集的SHAP值
            plt.subplot(1, 2, 2)
            fig2 = interpretability.shap.plot_shap(shap_values_intv)
            plt.title(f"Intervened Test Set (R²={r2_intv:.4f})")

            # 添加干预节点信息
            intervention_nodes = []
            if hasattr(scm, 'intervention_nodes'):
                intervention_nodes = scm.intervention_nodes

            unobserved_nodes = []
            if hasattr(scm, 'unobserved_nodes'):
                unobserved_nodes = scm.unobserved_nodes

            plt.suptitle(f"Dataset: {dataset_name} - node_unobserved={CONFIG['node_unobserved']}\n"
                        f"Intervention Nodes: {intervention_nodes}\n"
                        f"Unobserved Nodes: {unobserved_nodes}", fontsize=14)


            # plt.savefig(f"{output_dir}/dataset_{dataset_index}_shap.png", dpi=300, bbox_inches='tight')
            plt.show()
            plt.close()

            print(f"SHAP visualization saved for dataset {dataset_name}")
        else:
            print(f"Skipping SHAP visualization as R² on original test set ({r2_orig:.4f}) is below {CONFIG['r2_threshold']}")
    except Exception as e:
        print(f"Error in train_and_visualize: {e}")
        import traceback
        traceback.print_exc()
        raise

def main():
    """主函数"""
    print(f"{'='*50}")
    print(f"Processing {CONFIG['num_datasets']} datasets with node_unobserved={CONFIG['node_unobserved']}")
    print(f"R² threshold: {CONFIG['r2_threshold']}, Max SHAP samples: {CONFIG['max_shap_samples']}")
    print(f"Using device: {device}")
    print(f"{'='*50}")

    try:
        # 生成并处理数据集
        generate_and_process_datasets()
        print("Processing completed successfully!")
    except Exception as e:
        print(f"Error processing datasets: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
