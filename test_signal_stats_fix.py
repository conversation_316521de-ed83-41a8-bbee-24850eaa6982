#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的信号统计保留功能
验证蒙特卡洛计算时signal_var和signal_mean保留真实数据生成的信号统计
"""

import torch
import numpy as np
from multi_scm_model import SCM
from data_generator import generate_dag

def test_signal_stats_preservation():
    """测试信号统计保留功能"""
    
    print("=== 测试信号统计保留功能 ===")
    
    # 基础配置
    config = {
        'n_nodes_list': [1, 1],  # 1个根节点，1个子节点
        'num_samples': 300,
        'noise_std': 0.1,
        'init_std': 0.1,
        'device': 'cpu',
        'sample_std': False,
        'model_type': 'ANM',
        'noise_type': 'gaussian',
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        
        # SNR配置
        'use_snr_method': True,
        'snr_config': {
            'base_snr': 5.0,
            'depth_decay': 1.0,
            'complexity_penalty': 0.0
        },
        
        # 蒙特卡洛配置
        'use_monte_carlo_precompute': True,
        'monte_carlo_samples': 1000,  # 使用不同的样本数来确保有差异
        'monte_carlo_root_seed': 999,
        'monte_carlo_noise_seed': 888,
        'monte_carlo_resample': False
    }
    
    # 生成DAG
    dag, root_nodes, layer_nodes = generate_dag(config['n_nodes_list'], [lambda: 1], seed=42)
    
    print(f"DAG节点: {list(dag.nodes())}")
    print(f"DAG边: {list(dag.edges())}")
    
    # 创建SCM模型
    scm = SCM(config, custom_dag=(dag, root_nodes, layer_nodes, None, None), seed=42)
    
    print(f"\n配置验证:")
    print(f"use_monte_carlo_precompute: {scm.use_monte_carlo_precompute}")
    print(f"monte_carlo_samples: {scm.monte_carlo_samples}")
    print(f"num_samples (实际数据): {scm.num_samples}")
    
    # 生成扰动数据
    print(f"\n开始生成扰动数据...")
    try:
        (x_orig, y_orig), (x_pert, y_pert) = scm.perturbation(
            perturbation_type='counterfactual',
            perturbation_node_type='single_parent',
            perturbation_value_method='sample'
        )
        
        print(f"\n数据生成成功:")
        print(f"原始数据形状: X={x_orig.shape}, Y={y_orig.shape}")
        print(f"扰动数据形状: X={x_pert.shape}, Y={y_pert.shape}")
        
        # 检查SNR信息
        if hasattr(scm, '_snr_collection') and scm._snr_collection:
            print(f"\n=== SNR信息验证 ===")
            for node in scm._snr_collection.get('signal_var', {}):
                signal_var = scm._snr_collection['signal_var'][node]
                signal_mean = scm._snr_collection['signal_mean'][node]
                noise_std = scm._snr_collection.get('configured_noise_std', {}).get(node, 'N/A')
                target_snr = scm._snr_collection.get('target_snr', {}).get(node, 'N/A')
                
                print(f"节点{node}:")
                print(f"  信号方差 (应为真实数据): {signal_var:.6f}")
                print(f"  信号均值 (应为真实数据): {signal_mean:.6f}")
                print(f"  配置噪声std (来自蒙特卡洛): {noise_std}")
                print(f"  目标SNR: {target_snr}")
                
                # 验证实际SNR
                if noise_std != 'N/A':
                    actual_snr = signal_var / (float(noise_std) ** 2)
                    print(f"  实际SNR: {actual_snr:.6f}")
        
        # 验证数据统计
        print(f"\n=== 数据统计验证 ===")
        print(f"原始数据X统计: 均值={x_orig.mean():.6f}, 标准差={x_orig.std():.6f}")
        print(f"原始数据Y统计: 均值={y_orig.mean():.6f}, 标准差={y_orig.std():.6f}")
        
    except Exception as e:
        print(f"数据生成失败: {e}")
        import traceback
        traceback.print_exc()

def compare_with_traditional_method():
    """与传统方法比较"""
    
    print(f"\n=== 与传统方法比较 ===")
    
    base_config = {
        'n_nodes_list': [1, 1],
        'num_samples': 200,
        'noise_std': 0.15,
        'init_std': 0.1,
        'device': 'cpu',
        'sample_std': False,
        'model_type': 'ANM',
        'noise_type': 'gaussian',
        'root_distribution': 'gaussian',
        'root_mean': 0.0,
        'root_std': 1.0,
        'use_snr_method': True,
        'snr_config': {
            'base_snr': 8.0,
            'depth_decay': 1.0,
            'complexity_penalty': 0.0
        }
    }
    
    # 生成DAG
    dag, root_nodes, layer_nodes = generate_dag(base_config['n_nodes_list'], [lambda: 1], seed=123)
    
    results = {}
    
    # 测试传统方法
    print(f"\n--- 传统方法 ---")
    config1 = base_config.copy()
    config1['use_monte_carlo_precompute'] = False
    
    scm1 = SCM(config1, custom_dag=(dag, root_nodes, layer_nodes, None, None), seed=123)
    
    try:
        (x_orig_1, y_orig_1), (x_pert_1, y_pert_1) = scm1.perturbation(
            perturbation_type='statistical',
            perturbation_node_type='all_parents',
            perturbation_value_method='mean'
        )
        
        if hasattr(scm1, '_snr_collection') and scm1._snr_collection:
            for node in scm1._snr_collection.get('signal_var', {}):
                results[f'traditional_{node}'] = {
                    'signal_var': scm1._snr_collection['signal_var'][node],
                    'signal_mean': scm1._snr_collection['signal_mean'][node],
                    'noise_std': scm1._snr_collection.get('configured_noise_std', {}).get(node, 0)
                }
        print("传统方法成功")
    except Exception as e:
        print(f"传统方法失败: {e}")
    
    # 测试蒙特卡洛方法
    print(f"\n--- 蒙特卡洛方法 ---")
    config2 = base_config.copy()
    config2.update({
        'use_monte_carlo_precompute': True,
        'monte_carlo_samples': 2000,
        'monte_carlo_root_seed': 456,
        'monte_carlo_noise_seed': 789,
        'monte_carlo_resample': False
    })
    
    scm2 = SCM(config2, custom_dag=(dag, root_nodes, layer_nodes, None, None), seed=123)
    
    try:
        (x_orig_2, y_orig_2), (x_pert_2, y_pert_2) = scm2.perturbation(
            perturbation_type='statistical',
            perturbation_node_type='all_parents',
            perturbation_value_method='mean'
        )
        
        if hasattr(scm2, '_snr_collection') and scm2._snr_collection:
            for node in scm2._snr_collection.get('signal_var', {}):
                results[f'monte_carlo_{node}'] = {
                    'signal_var': scm2._snr_collection['signal_var'][node],
                    'signal_mean': scm2._snr_collection['signal_mean'][node],
                    'noise_std': scm2._snr_collection.get('configured_noise_std', {}).get(node, 0)
                }
        print("蒙特卡洛方法成功")
    except Exception as e:
        print(f"蒙特卡洛方法失败: {e}")
    
    # 比较结果
    print(f"\n=== 结果比较 ===")
    for key, value in results.items():
        print(f"{key}: signal_var={value['signal_var']:.6f}, signal_mean={value['signal_mean']:.6f}, noise_std={value['noise_std']:.6f}")

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    test_signal_stats_preservation()
    compare_with_traditional_method()
