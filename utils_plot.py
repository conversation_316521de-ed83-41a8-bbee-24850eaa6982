"""
utils_plot.py - 可视化工具库

该文件提供了实验结果的可视化功能，支持多种图表类型和统计分析：

主要功能：
1. 性能比较图表：R²比较、模型性能对比等
2. 特征重要性可视化：排列重要性图表、节点重要性分析
3. SNR分析图表：信噪比对比、噪声分析等
4. 统计图表：箱线图、散点图、分布图等
5. 图表管理：统一的样式、颜色、布局管理

核心类：
- PlotManager: 绘图参数和样式管理
- ImportancePlotter: 特征重要性可视化
- R2Plotter: R²性能比较可视化
- SNRPlotter: SNR分析可视化

可视化特性：
- 统一的颜色方案和样式
- 自动的图例和标注
- 支持多种文件格式输出
- 响应式布局设计
- 中英文标签支持
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import pandas as pd
import networkx as nx
from utils_scm import get_exclusive_node_relationships


class PlotManager:
    """统一管理绘图参数、颜色、布局等"""
    
    # 固定的模型顺序
    PREFERRED_MODEL_ORDER = ['ols', 'lasso', 'catboost', 'xgboost', 'lightgbm', 
                            'tabpfn_default', 'tabpfn_mse', 'tabpfn_muzero']
    
    # 颜色映射
    COLORS = {
        'markov': {'markov': 'lightcoral', 'others': 'lightblue'},
        'detailed': {'parents': 'lightcoral', 'children': 'lightgreen', 
                    'spouses': 'plum', 'others': 'lightblue'},
        'r2_default': ['lightblue', 'lightcoral', 'lightgreen', 'plum', 'lightyellow', 'lightpink']
    }
    
    # 布局参数
    LAYOUT_PARAMS = {
        'markov': {'base_width': 3, 'width_multiplier': 1.5, 'min_width': 8, 
                  'base_box_spacing': 1.2, 'spacing_reduction': 0.05},
        'detailed': {'base_width': 4, 'width_multiplier': 2.0, 'min_width': 12,
                    'base_box_spacing': 1.0, 'spacing_reduction': 0.03}
    }
    
    @staticmethod
    def get_available_models(data_dict):
        """获取数据中可用的模型类型，按固定顺序排列"""
        all_models = set()
        if isinstance(data_dict, dict):
            for dataset_data in data_dict.values():
                if isinstance(dataset_data, dict):
                    all_models.update(dataset_data.keys())
        return [model for model in PlotManager.PREFERRED_MODEL_ORDER if model in all_models]
    
    @staticmethod
    def add_annotations(ax, box_data, positions, global_max, annotate_all_datasets=True,
                       annotate_effective_datasets=True, effective_data=None, font_size=8,
                       eff_equ_all=False):
        """统一的标注逻辑"""
        if not box_data or not global_max:
            return

        annotation_offset = global_max * 0.12

        # 如果eff_equ_all=True，则不进行有效数据集标注（避免重复标注）
        should_annotate_effective = annotate_effective_datasets and not eff_equ_all

        for k, values in enumerate(box_data):
            if not values:
                continue

            max_val = np.max(values)
            x_pos = positions[k] if isinstance(positions, list) else k + 1

            # 标注所有数据集的统计信息
            if annotate_all_datasets:
                mean_val = np.mean(values)
                std_val = np.std(values)
                y_pos = max_val + annotation_offset * 0.4
                ax.text(x_pos, y_pos, f'{mean_val:.3f}±{std_val:.3f}',
                       ha='center', va='bottom', fontsize=font_size, color='blue',
                       bbox=dict(boxstyle='round,pad=0.1', facecolor='white',
                               alpha=0.95, edgecolor='lightgray'))

            # 标注有效数据集的统计信息（仅当eff_equ_all=False时）
            if should_annotate_effective and effective_data and k < len(effective_data):
                eff_values = effective_data[k]
                if eff_values:
                    eff_mean_val = np.mean(eff_values)
                    eff_std_val = np.std(eff_values)
                    eff_y_pos = max_val + annotation_offset * (0.8 if annotate_all_datasets else 0.4)
                    ax.text(x_pos, eff_y_pos, f'Eff: {eff_mean_val:.3f}±{eff_std_val:.3f}',
                           ha='center', va='bottom', fontsize=font_size-1, color='red',
                           bbox=dict(boxstyle='round,pad=0.1', facecolor='lightyellow',
                                   alpha=0.95, edgecolor='orange'))

        # 调整y轴上限以容纳注释
        current_ylim = ax.get_ylim()
        multiplier = 1.4 if (annotate_all_datasets and should_annotate_effective) else 1.0
        new_ymax = global_max + annotation_offset * multiplier
        ax.set_ylim(current_ylim[0], max(current_ylim[1], new_ymax))


class DataProcessor:
    """统一数据处理逻辑"""
    


    @staticmethod
    def collect_node_type_data_for_config(config_type, config_model_data, scm_objects, preferred_model_order):
        """收集指定配置下所有数据集的节点类型数据"""
        markov_vs_others_data = {}
        detailed_types_data = {}

        available_models = [model for model in preferred_model_order if model in config_model_data]

        for model_type in available_models:
            markov_vs_others_data[model_type] = {'markov': [], 'others': []}
            detailed_types_data[model_type] = {'parents': [], 'children': [], 'spouses': [], 'others': []}

            model_data = config_model_data[model_type]

            for dataset_idx, feature_importance in model_data.items():
                if dataset_idx not in scm_objects:
                    continue

                dataset_scm_configs = scm_objects[dataset_idx]
                if config_type not in dataset_scm_configs:
                    continue

                scm_info = dataset_scm_configs[config_type]
                target_node = scm_info.get('selected_target', None)

                if not target_node or 'dag_nodes' not in scm_info or 'dag_edges' not in scm_info:
                    continue

                # 构建图并获取节点关系
                temp_dag = nx.DiGraph()
                temp_dag.add_nodes_from(scm_info['dag_nodes'])
                temp_dag.add_edges_from(scm_info['dag_edges'])

                if target_node not in temp_dag.nodes():
                    continue

                relationships = get_exclusive_node_relationships(temp_dag, target_node)

                # 分类节点并收集特征重要性
                for feature_name, importance_value in feature_importance.items():
                    if feature_name == target_node:
                        continue

                    # 确定节点类型
                    if feature_name in relationships['parents']:
                        node_type = 'parents'
                        is_markov = True
                    elif feature_name in relationships['children']:
                        node_type = 'children'
                        is_markov = True
                    elif feature_name in relationships['spouses']:
                        node_type = 'spouses'
                        is_markov = True
                    else:
                        node_type = 'others'
                        is_markov = False

                    detailed_types_data[model_type][node_type].append(importance_value)

                    if is_markov:
                        markov_vs_others_data[model_type]['markov'].append(importance_value)
                    else:
                        markov_vs_others_data[model_type]['others'].append(importance_value)

        return {
            'markov_vs_others': markov_vs_others_data,
            'detailed_types': detailed_types_data
        }


class ImportancePlotter:
    """特征重要性绘图类"""

    def __init__(self, plot_manager=None):
        self.plot_manager = plot_manager or PlotManager()

    def plot_permutation_importance_comparison(self, importance_results, image_dir, scm_objects=None,
                                             custom_functions_configs=None, results=None, r2_threshold=0.5,
                                             annotate_all_datasets=True, annotate_effective_datasets=True,
                                             effective_datasets=None):
        """按节点类型分组绘制不同SNR配置不同模型下permutation_importance的箱线图"""
        if not importance_results or scm_objects is None:
            print("没有特征重要性数据或SCM对象，跳过permutation importance对比图生成")
            return

        # 使用传入的有效数据集或计算新的
        if effective_datasets is None:
            effective_datasets = {}
            if results is not None:
                # 如果没有传入预计算的有效数据集，则抛出错误提示应该从主调用文件传入
                raise ValueError("effective_datasets 参数为 None，应该从主调用文件中预计算并传入")

        # 按配置类型分组收集数据
        config_data, config_data_effective = self._collect_importance_data(
            importance_results, effective_datasets)

        if not config_data:
            print("没有有效的配置数据")
            return

        # 为每个配置生成单独的图
        for config_type in sorted(config_data.keys()):
            config_name = config_type
            if custom_functions_configs and config_type in custom_functions_configs:
                config_name = custom_functions_configs[config_type]['name']

            # 收集节点类型数据
            node_type_data = DataProcessor.collect_node_type_data_for_config(
                config_type, config_data[config_type], scm_objects, self.plot_manager.PREFERRED_MODEL_ORDER)

            node_type_data_effective = None
            if config_type in config_data_effective:
                node_type_data_effective = DataProcessor.collect_node_type_data_for_config(
                    config_type, config_data_effective[config_type], scm_objects, self.plot_manager.PREFERRED_MODEL_ORDER)

            if not node_type_data:
                print(f"配置 {config_name} 没有有效的节点类型数据，跳过")
                continue

            # 生成两种类型的图
            self._plot_config_node_type_importance(node_type_data, config_name, image_dir,
                                                  node_type_data_effective, effective_datasets, config_type,
                                                  annotate_all_datasets, annotate_effective_datasets)

        print("已完成所有配置的按节点类型分组的特征重要性可视化")

    def _plot_config_node_type_importance(self, node_type_data, config_name, image_dir,
                                         node_type_data_effective=None, effective_datasets=None, config_type=None,
                                         annotate_all_datasets=True, annotate_effective_datasets=True):
        """
        为单个配置绘制按节点类型分组的特征重要性图
        分别保存两个独立的图：马尔科夫 vs 其他，以及详细的四种类型
        """
        markov_data = node_type_data['markov_vs_others']
        detailed_data = node_type_data['detailed_types']

        # 获取有效数据集的数据（如果提供）
        markov_data_effective = node_type_data_effective['markov_vs_others'] if node_type_data_effective else None
        detailed_data_effective = node_type_data_effective['detailed_types'] if node_type_data_effective else None

        # 获取可用的模型类型
        available_models = list(markov_data.keys())
        if not available_models:
            return

        # 图1: 马尔科夫节点 vs 其他节点
        self._plot_node_type_boxplot(markov_data, markov_data_effective, config_name, config_type, image_dir,
                                    'markov', effective_datasets, annotate_all_datasets, annotate_effective_datasets)

        # 图2: 详细的四种节点类型
        self._plot_node_type_boxplot(detailed_data, detailed_data_effective, config_name, config_type, image_dir,
                                    'detailed', effective_datasets, annotate_all_datasets, annotate_effective_datasets)

    def _collect_importance_data(self, importance_results, effective_datasets):
        """收集特征重要性数据"""
        config_data = {}
        config_data_effective = {}

        for result in importance_results:
            config_type = result.get('use_snr', 'unknown')
            model_type = result.get('model_type', 'unknown')
            dataset_name = result.get('dataset', 'unknown')

            # 从dataset_name中提取dataset_idx
            try:
                dataset_idx = int(dataset_name.split('_')[1])
            except:
                continue

            if config_type not in config_data:
                config_data[config_type] = {}
                config_data_effective[config_type] = {}
            if model_type not in config_data[config_type]:
                config_data[config_type][model_type] = {}
                config_data_effective[config_type][model_type] = {}
            if dataset_idx not in config_data[config_type][model_type]:
                config_data[config_type][model_type][dataset_idx] = {}
            if dataset_idx not in config_data_effective[config_type][model_type]:
                config_data_effective[config_type][model_type][dataset_idx] = {}

            # 提取permutation importance
            if 'importance' in result and 'permutation' in result['importance']:
                perm_imp = result['importance']['permutation']
                config_data[config_type][model_type][dataset_idx].update(perm_imp)

                # 如果是有效数据集，也添加到有效数据集
                if (config_type in effective_datasets and
                    'datasets' in effective_datasets[config_type] and
                    dataset_name in effective_datasets[config_type]['datasets']):
                    config_data_effective[config_type][model_type][dataset_idx].update(perm_imp)

        return config_data, config_data_effective

    def _plot_node_type_boxplot(self, data, data_effective, config_name, config_type, image_dir,
                               plot_type, effective_datasets, annotate_all_datasets, annotate_effective_datasets):
        """绘制节点类型箱线图"""
        if not data:
            return

        # 获取配置参数
        config = self.plot_manager.LAYOUT_PARAMS[plot_type]
        colors = self.plot_manager.COLORS[plot_type]

        # 确定节点类别
        if plot_type == 'markov':
            node_categories = ['markov', 'others']
            title_suffix = 'Markov vs Others'
            filename_suffix = 'markov_vs_others'
        else:  # detailed
            node_categories = ['parents', 'children', 'spouses', 'others']
            title_suffix = 'Detailed Node Types'
            filename_suffix = 'detailed_types'

        # 获取可用模型
        available_models = list(data.keys())
        num_models = len(available_models)

        if num_models == 0:
            return

        # 计算图形尺寸
        fig_width = max(config['min_width'], config['base_width'] + num_models * config['width_multiplier'])
        fig_height = 8

        plt.figure(figsize=(fig_width, fig_height))

        # 准备绘图数据
        all_box_data, all_box_labels, all_box_colors = [], [], []
        model_positions = {}
        separator_positions = []
        current_position = 1

        # 动态计算间距
        box_spacing = max(0.8, config['base_box_spacing'] - num_models * config['spacing_reduction'])

        for model_type in available_models:
            start_pos = current_position
            model_data = data[model_type]

            for node_category in node_categories:
                if model_data[node_category]:  # 只有当有数据时才添加
                    all_box_data.append(model_data[node_category])
                    all_box_labels.append(node_category)
                    all_box_colors.append(colors[node_category])
                    current_position += box_spacing

            if current_position > start_pos:
                model_positions[model_type] = (start_pos, current_position - box_spacing)
                if model_type != available_models[-1]:  # 不是最后一个模型
                    separator_positions.append(current_position - box_spacing/2)

        if all_box_data:
            # 绘制箱线图
            positions = [i * box_spacing + 1 for i in range(len(all_box_data))]
            bp = plt.boxplot(all_box_data, positions=positions, labels=all_box_labels,
                           patch_artist=True, showmeans=True, meanline=True, widths=0.6)

            # 设置颜色
            for patch, color in zip(bp['boxes'], all_box_colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)

            # 计算全局最大值用于标注
            all_max_values = [np.max(values) for values in all_box_data if values]
            if all_max_values:
                global_max = max(all_max_values)

                # 准备有效数据集数据用于标注
                effective_box_data = []
                if data_effective:
                    for model_type in available_models:
                        for node_category in node_categories:
                            if data[model_type][node_category]: 
                                effective_box_data.append(data_effective[model_type][node_category] if data_effective[model_type][node_category] else [])

                # 获取当前配置的eff_equ_all状态
                eff_equ_all = False
                if effective_datasets and config_type in effective_datasets:
                    eff_equ_all = effective_datasets[config_type].get('eff_equ_all', False)

                # 添加标注
                self.plot_manager.add_annotations(
                    plt.gca(), all_box_data, positions, global_max,
                    annotate_all_datasets, annotate_effective_datasets, effective_box_data,
                    eff_equ_all=eff_equ_all)

            # 绘制分隔线
            y_min, y_max = plt.ylim()
            for sep_pos in separator_positions:
                plt.axvline(x=sep_pos, ymin=0, ymax=1, color='gray', linestyle='--', linewidth=1.2, alpha=0.6)

            # 添加模型名称
            for model_type, (start, end) in model_positions.items():
                center_x = (start + end) / 2
                plt.text(center_x, y_min - (y_max - y_min) * 0.1, model_type,
                        ha='center', va='top', fontsize=10, fontweight='bold',
                        bbox=dict(boxstyle='round,pad=0.15', facecolor='lightgray', alpha=0.7))

        plt.ylabel('Permutation Importance', fontsize=12)

        # 设置标题
        title = f'{config_name} - {title_suffix}'
        if annotate_effective_datasets and effective_datasets and config_type in effective_datasets:
            eff_count = len(effective_datasets[config_type].get('datasets', set()))
            eff_equ_all = effective_datasets[config_type].get('eff_equ_all', False)
            # 只有当eff_equ_all=False时才显示有效数据集信息，避免重复标注
            if not eff_equ_all:
                title += f' (Eff: {eff_count})'


        plt.title(title, fontsize=14, weight='bold')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # 保存文件
        safe_config_name = config_type.replace('.', '_').replace(' ', '_')
        filename = f'permutation_importance_{safe_config_name}_{filename_suffix}.png'
        plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight')
        plt.close()


class R2Plotter:
    """R2性能对比绘图类"""

    def __init__(self, plot_manager=None):
        self.plot_manager = plot_manager or PlotManager()

    def _parse_config_snr(self, config_str):
        """解析配置字符串，提取parent_snr, child_snr, other_snr"""
        try:
            # 配置格式: "parent_snr_2_child_snr_2_other_snr_0.2"
            parts = config_str.split('_')
            parent_snr = child_snr = other_snr = 0

            for i, part in enumerate(parts):
                if part == 'snr' and i > 0 and i + 1 < len(parts):
                    # 检查前一个部分来确定是哪种SNR
                    prev_part = parts[i - 1]
                    if prev_part == 'parent':
                        parent_snr = float(parts[i + 1])
                    elif prev_part == 'child':
                        child_snr = float(parts[i + 1])
                    elif prev_part == 'other':
                        other_snr = float(parts[i + 1])

            return parent_snr, child_snr, other_snr
        except Exception as e:
            # 如果解析失败，返回默认值
            print(f"Warning: Failed to parse config '{config_str}': {e}")
            return 0, 0, 0

    def _sort_configs_by_snr(self, config_types):
        """按parent_snr, child_snr, other_snr排序配置"""
        config_with_snr = []
        for config in config_types:
            parent_snr, child_snr, other_snr = self._parse_config_snr(config)
            config_with_snr.append((config, parent_snr, child_snr, other_snr))

        # 按parent_snr, child_snr, other_snr排序
        config_with_snr.sort(key=lambda x: (x[1], x[2], x[3]))
        return [item[0] for item in config_with_snr]

    def _generate_color_scheme(self, config_types):
        """为配置生成颜色方案"""

        # 按SNR排序配置
        sorted_configs = self._sort_configs_by_snr(config_types)

        # 获取所有unique的parent_snr值并排序
        parent_snrs = []
        for config in sorted_configs:
            parent_snr, _, _ = self._parse_config_snr(config)
            if parent_snr not in parent_snrs:
                parent_snrs.append(parent_snr)
        parent_snrs.sort()  # 确保按数值大小排序

        # 为每个parent_snr分配指定的基础颜色
        parent_color_mapping = {
            2.0: 'blue',
            4.0: 'green',
            10.0: 'purple',
            50.0: 'yellow',
            100.0: 'red'
        }

        color_map = {}

        for config in sorted_configs:
            parent_snr, child_snr, other_snr = self._parse_config_snr(config)

            # 获取该parent_snr对应的基础颜色（确保数据类型匹配）
            base_color = parent_color_mapping.get(float(parent_snr), 'gray')

            # 计算同一parent_snr组内的配置，用于确定深浅程度
            same_parent_configs = [c for c in sorted_configs if self._parse_config_snr(c)[0] == parent_snr]

            if len(same_parent_configs) > 1:
                # 获取该parent_snr组内的child_snr和other_snr范围
                child_snrs = [self._parse_config_snr(c)[1] for c in same_parent_configs]
                other_snrs = [self._parse_config_snr(c)[2] for c in same_parent_configs]

                min_child, max_child = min(child_snrs), max(child_snrs)
                min_other, max_other = min(other_snrs), max(other_snrs)

                # 计算当前配置在组内的相对位置（0到1之间）
                if max_child > min_child:
                    child_ratio = (child_snr - min_child) / (max_child - min_child)
                else:
                    child_ratio = 0

                if max_other > min_other:
                    other_ratio = (other_snr - min_other) / (max_other - min_other)
                else:
                    other_ratio = 0

                # 综合child_snr和other_snr的影响，计算透明度（0.4到1.0之间，增大范围）
                alpha = 0.4 + 0.6 * ((child_ratio + other_ratio) / 2)
            else:
                # 如果只有一个配置，使用中等透明度
                alpha = 0.7

            # 使用浅色基调和透明度生成最终颜色（RGBA格式）
            if base_color == 'blue':
                # 浅蓝色基调 (173, 216, 230) -> Light Blue
                color_map[config] = (173/255, 216/255, 230/255, alpha)
            elif base_color == 'green':
                # 浅绿色基调 (144, 238, 144) -> Light Green
                color_map[config] = (144/255, 238/255, 144/255, alpha)
            elif base_color == 'purple':
                # 浅紫色基调 (221, 160, 221) -> Plum
                color_map[config] = (221/255, 160/255, 221/255, alpha)
            elif base_color == 'yellow':
                # 浅黄色基调 (255, 255, 224) -> Light Yellow
                color_map[config] = (255/255, 255/255, 224/255, alpha)
            elif base_color == 'red':
                # 浅红色基调 (255, 182, 193) -> Light Pink
                color_map[config] = (255/255, 182/255, 193/255, alpha)
            else:  # gray for unknown parent_snr
                # 浅灰色基调 (192, 192, 192) -> Silver
                color_map[config] = (192/255, 192/255, 192/255, alpha)

        return color_map, sorted_configs

    def _add_child_snr_separators(self, sorted_configs, positions, box_data):
        """在SNR变化处添加竖直分隔线"""
        import matplotlib.pyplot as plt

        if len(sorted_configs) <= 1:
            return

        # 分别存储两种类型的分隔线位置
        child_snr_separators = []  # child_snr变化的分隔线（浅色）
        parent_snr_separators = []  # parent_snr变化的分隔线（深色）

        prev_parent_snr = None
        prev_child_snr = None

        for i, config in enumerate(sorted_configs):
            if i < len(box_data):  # 只处理有数据的配置
                parent_snr, child_snr, _ = self._parse_config_snr(config)

                if prev_parent_snr is not None and prev_child_snr is not None:
                    if i > 0:
                        separator_x = (positions[i-1] + positions[i]) / 2

                        # 判断分隔线类型
                        if parent_snr != prev_parent_snr:
                            # parent_snr发生变化，添加深色分隔线
                            parent_snr_separators.append(separator_x)
                        elif child_snr != prev_child_snr:
                            # parent_snr相同但child_snr发生变化，添加浅色分隔线
                            child_snr_separators.append(separator_x)

                prev_parent_snr = parent_snr
                prev_child_snr = child_snr

        # 绘制深色分隔线（parent_snr变化）
        for sep_x in parent_snr_separators:
            plt.axvline(x=sep_x, color='gray', linestyle='-', alpha=0.8, linewidth=2)

        # 绘制浅色分隔线（child_snr变化）
        for sep_x in child_snr_separators:
            plt.axvline(x=sep_x, color='gray', linestyle='--', alpha=0.6, linewidth=1)

    def plot_r2_comparison(self, r2_results, image_dir, r2_threshold=0.5,
                          annotate_all_datasets=True, annotate_effective_datasets=True,
                          effective_datasets=None):
        """自适应R²性能对比图生成函数"""
        if not r2_results:
            return

        # 检查是否是custom_functions模式
        is_custom_functions_mode = isinstance(r2_results[0]['use_snr'], str)
        if not is_custom_functions_mode:
            return

        # 按数据集和模型分组
        datasets = {}
        config_types = set()

        for r in r2_results:
            dataset_name = r['dataset']
            model_type = r['model_type']
            config_type = r['use_snr']
            config_types.add(config_type)

            if dataset_name not in datasets:
                datasets[dataset_name] = {}
            if model_type not in datasets[dataset_name]:
                datasets[dataset_name][model_type] = {}

            datasets[dataset_name][model_type][config_type] = r

        config_types = sorted(list(config_types))

        # 使用传入的有效数据集或计算新的
        if effective_datasets is None:
            # 如果没有传入预计算的有效数据集，则抛出错误提示应该从主调用文件传入
            raise ValueError("effective_datasets 参数为 None，应该从主调用文件中预计算并传入")

        # 生成图表
        self._plot_r2_comparison_generic(datasets, config_types, effective_datasets, image_dir,
                                       annotate_all_datasets, annotate_effective_datasets,
                                       group_by='model')
        self._plot_r2_comparison_generic(datasets, config_types, effective_datasets, image_dir,
                                       annotate_all_datasets, annotate_effective_datasets,
                                       group_by='config')

    def _plot_r2_comparison_generic(self, datasets, config_types, effective_datasets, image_dir,
                                  annotate_all_datasets, annotate_effective_datasets, group_by='model'):
        """通用的R²对比绘图函数

        Args:
            group_by: 'model' 或 'config'，控制分组方式
                - 'model': 按模型分组，每个模型每个指标一张图，使用颜色编码配置
                - 'config': 按配置分组，每个配置一张图，x轴为模型类型
        """
        model_types = self.plot_manager.get_available_models(datasets)
        metrics = ['r2_train', 'r2_test', 'r2_intv']
        metric_names = ['Train R²', 'Test R²', 'Intervention R²']

        if group_by == 'model':
            # 每个模型每个指标单独一张图
            self._plot_by_model_with_color_coding(datasets, config_types, effective_datasets,
                                                image_dir, annotate_all_datasets,
                                                annotate_effective_datasets, model_types,
                                                metrics, metric_names)
        else:
            # 按配置分组绘图
            self._plot_by_config_traditional(datasets, config_types, effective_datasets,
                                           image_dir, annotate_all_datasets,
                                           annotate_effective_datasets, model_types,
                                           metrics, metric_names)

    def _plot_by_model_with_color_coding(self, datasets, config_types, effective_datasets,
                                       image_dir, annotate_all_datasets, annotate_effective_datasets,
                                       model_types, metrics, metric_names):
        """按模型分组的绘图方法：每个指标单独一张图，使用颜色编码"""
        import matplotlib.pyplot as plt

        # 生成颜色方案和排序后的配置
        color_map, sorted_configs = self._generate_color_scheme(config_types)

        for model_type in model_types:
            for metric, metric_name in zip(metrics, metric_names):
                # 根据配置数量动态调整图表宽度
                num_configs = len(sorted_configs)
                fig_width = max(16, num_configs * 1.2)  # 最小16英寸，每个配置至少1.2英寸
                plt.figure(figsize=(fig_width, 8))

                # 收集数据
                box_data = []
                box_colors = []
                legend_labels = []

                for config in sorted_configs:
                    values = []
                    for dataset_name in datasets:
                        if (model_type in datasets[dataset_name] and
                            config in datasets[dataset_name][model_type]):
                            value = datasets[dataset_name][model_type][config].get(metric, None)
                            if value is not None:
                                values.append(value)

                    if values:  # 只有当有数据时才添加
                        box_data.append(values)
                        box_colors.append(color_map[config])

                        # 生成图例标签，根据eff_equ_all状态决定是否显示有效数据集个数
                        if config in effective_datasets:
                            effective_data = effective_datasets[config]
                            eff_count = len(effective_data.get('datasets', set()))
                            eff_equ_all = effective_data.get('eff_equ_all', False)

                            if eff_equ_all:
                                # 所有数据集都有效，不显示个数
                                legend_labels.append(f'{config}')
                            else:
                                # 部分数据集有效，显示有效数据集个数
                                legend_labels.append(f'{config} ({eff_count})')
                        else:
                            # 配置不在有效数据集中
                            legend_labels.append(f'{config} (0)')

                if box_data:
                    # 绘制箱线图，增加间距
                    spacing = 1.5  # 增加箱子间距
                    positions = [i * spacing for i in range(1, len(box_data) + 1)]
                    bp = plt.boxplot(box_data, positions=positions, patch_artist=True,
                                   showmeans=True, meanline=True, widths=0.8)

                    # 设置颜色
                    for patch, color in zip(bp['boxes'], box_colors):
                        if len(color) == 4:  # RGBA格式
                            patch.set_facecolor(color[:3])  # RGB部分
                            patch.set_alpha(color[3])       # Alpha部分
                        else:  # RGB格式（向后兼容）
                            patch.set_facecolor(color)
                            patch.set_alpha(0.7)

                    # 添加child_snr变化处的竖直分隔线
                    self._add_child_snr_separators(sorted_configs, positions, box_data)

                    # 计算全局最大值用于标注
                    all_max_values = [max(values) for values in box_data if values]
                    if all_max_values:
                        global_max = max(all_max_values)

                        # 准备有效数据集数据和检查是否需要标注
                        effective_box_data = []
                        any_config_needs_effective_annotation = False

                        config_index = 0
                        for config in sorted_configs:
                            if config_index < len(box_data):  # 只处理实际有数据的配置
                                effective_values = []

                                if config in effective_datasets:
                                    effective_data = effective_datasets[config]
                                    eff_equ_all = effective_data.get('eff_equ_all', False)

                                    if not eff_equ_all:
                                        any_config_needs_effective_annotation = True
                                        # 收集有效数据集的数据
                                        effective_dataset_set = effective_data.get('datasets', set())
                                        for dataset_name in effective_dataset_set:
                                            if (dataset_name in datasets and model_type in datasets[dataset_name]
                                                and config in datasets[dataset_name][model_type]):
                                                value = datasets[dataset_name][model_type][config].get(metric, None)
                                                if value is not None:
                                                    effective_values.append(value)
                                else:
                                    # 配置不在有效数据集中
                                    any_config_needs_effective_annotation = True

                                effective_box_data.append(effective_values)
                                config_index += 1

                        # 只有当有配置需要有效数据集标注时才进行标注
                        should_annotate_effective = annotate_effective_datasets and any_config_needs_effective_annotation

                        # 添加标注
                        self.plot_manager.add_annotations(
                            plt.gca(), box_data, positions, global_max,
                            annotate_all_datasets, should_annotate_effective, effective_box_data,
                            eff_equ_all=not any_config_needs_effective_annotation)

                    # 设置图表属性
                    plt.ylabel(f'{metric_name} Score')
                    plt.title(f'{model_type.upper()} - {metric_name}')
                    plt.grid(True, alpha=0.3)
                    plt.ylim(0, 1.1)

                    # 设置x轴标签显示parent_snr和child_snr值
                    x_labels = []
                    for config in sorted_configs:
                        if config in [sorted_configs[i] for i in range(len(box_data))]:  # 只为有数据的配置创建标签
                            parent_snr, child_snr, other_snr = self._parse_config_snr(config)
                            x_labels.append(f'P:{parent_snr}\nC:{child_snr}\nO:{other_snr}')

                    plt.xticks(positions, x_labels, fontsize=9)

                    # 设置横向图例，支持RGBA颜色
                    # legend_patches = []
                    # for color in box_colors:
                    #     if len(color) == 4:  # RGBA格式
                    #         patch = plt.Rectangle((0,0),1,1, facecolor=color[:3], alpha=color[3])
                    #     else:  # RGB格式
                    #         patch = plt.Rectangle((0,0),1,1, facecolor=color, alpha=0.7)
                    #     legend_patches.append(patch)

                    # # 横向图例，可以多行显示
                    # plt.legend(legend_patches, legend_labels,
                    #          loc='upper center', bbox_to_anchor=(0.5, -0.15),
                    #          ncol=min(5, len(legend_labels)), fontsize=8)

                    # 调整布局，为下方图例留出空间
                    plt.tight_layout()
                    # plt.subplots_adjust(bottom=0.25)

                    # 保存文件
                    filename = f'custom_functions_r2_by_model_{model_type}_{metric}.png'
                    plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight')
                    plt.close()

    def _plot_by_config_traditional(self, datasets, config_types, effective_datasets,
                                  image_dir, annotate_all_datasets, annotate_effective_datasets,
                                  model_types, metrics, metric_names):
        """按配置分组绘图方法"""
        import matplotlib.pyplot as plt

        for outer_item in config_types:
            plt.figure(figsize=(15, 6))

            for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
                ax = plt.subplot(1, 3, i + 1)

                # 按配置分组：收集当前配置下每个模型的数据
                data_dict = {model_type: [] for model_type in model_types}

                current_config = outer_item
                for dataset_data in datasets.values():
                    for model_type in model_types:
                        if (model_type in dataset_data and
                            current_config in dataset_data[model_type]):
                            value = dataset_data[model_type][current_config].get(metric, None)
                            if value is not None:
                                data_dict[model_type].append(value)

                # 绘制箱线图
                box_data = [data_dict[model_type] for model_type in model_types if data_dict[model_type]]
                box_labels = [model_type for model_type in model_types if data_dict[model_type]]

                if box_data:
                    bp = ax.boxplot(box_data, labels=box_labels, patch_artist=True, showmeans=True)

                    # 设置颜色
                    for j, patch in enumerate(bp['boxes']):
                        patch.set_facecolor(self.plot_manager.COLORS['r2_default'][j % len(self.plot_manager.COLORS['r2_default'])])
                        patch.set_alpha(0.7)

                    # 计算全局最大值用于标注
                    all_max_values = [np.max(values) for values in box_data if values]
                    if all_max_values:
                        global_max = max(all_max_values)

                        # 准备有效数据集数据
                        effective_box_data = self._collect_effective_data_for_annotation(
                            box_labels, datasets, effective_datasets, metric, 'config', outer_item)

                        # 获取当前配置的eff_equ_all状态
                        eff_equ_all = False
                        if outer_item in effective_datasets:
                            eff_equ_all = effective_datasets[outer_item].get('eff_equ_all', False)

                        # 添加标注
                        self.plot_manager.add_annotations(
                            ax, box_data, list(range(1, len(box_data)+1)), global_max,
                            annotate_all_datasets, annotate_effective_datasets, effective_box_data,
                            eff_equ_all=eff_equ_all)

                ax.set_ylabel('R² Score')
                ax.set_title(f'{metric_name} Comparison')
                ax.grid(True, alpha=0.3)
                ax.set_ylim(0, 1.1)
                plt.setp(ax.get_xticklabels(), ha='center')

            # 设置总标题和文件名 - 按配置分组
            config_display = outer_item.replace('_', ' ')
            main_title = f'Model Performance Comparison - {config_display}'
            safe_config_name = outer_item.replace('.', '_').replace(' ', '_')
            filename = f'custom_functions_r2_by_config_{safe_config_name}.png'
            if annotate_effective_datasets and outer_item in effective_datasets:
                eff_count = len(effective_datasets[outer_item].get('datasets', set()))
                eff_equ_all = effective_datasets[outer_item].get('eff_equ_all', False)
                # 只有当eff_equ_all=False时才显示有效数据集信息
                if not eff_equ_all:
                    main_title += f' (Eff: {eff_count})'

            plt.suptitle(main_title, fontsize=16, weight='bold')
            plt.tight_layout()

            # 保存文件
            plt.savefig(os.path.join(image_dir, filename), dpi=300, bbox_inches='tight')
            plt.close()

    def _collect_effective_data_for_annotation(self, box_labels, datasets, effective_datasets,
                                             metric, group_by, outer_item):
        """收集用于标注的有效数据集数据"""
        effective_box_data = []

        if group_by == 'model':
            # 按模型分组：box_labels是配置类型
            current_model = outer_item
            for config in box_labels:
                effective_values = []
                if config in effective_datasets:
                    # effective_datasets[config] 是一个字典，包含datasets集合
                    effective_dataset_set = effective_datasets[config].get('datasets', set())
                    for dataset_name in effective_dataset_set:
                        if (dataset_name in datasets and current_model in datasets[dataset_name]
                            and config in datasets[dataset_name][current_model]):
                            value = datasets[dataset_name][current_model][config].get(metric, None)
                            if value is not None:
                                effective_values.append(value)
                effective_box_data.append(effective_values)
        else:
            # 按配置分组：box_labels是模型类型
            current_config = outer_item
            for model in box_labels:
                effective_values = []
                if current_config in effective_datasets:
                    # effective_datasets[current_config] 是一个字典，包含datasets集合
                    effective_dataset_set = effective_datasets[current_config].get('datasets', set())
                    for dataset_name in effective_dataset_set:
                        if (dataset_name in datasets and model in datasets[dataset_name]
                            and current_config in datasets[dataset_name][model]):
                            value = datasets[dataset_name][model][current_config].get(metric, None)
                            if value is not None:
                                effective_values.append(value)
                effective_box_data.append(effective_values)

        return effective_box_data






