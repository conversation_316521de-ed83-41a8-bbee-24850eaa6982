# 种子配置系统使用指南

## 概述

本系统实现了一个灵活的随机种子管理机制，确保数据集生成的可重现性。系统支持四种不同的种子配置模式，可以精确控制不同组件的随机行为。

## 种子系统的四种配置模式

### 1. 完全随机模式
```python
seed=None, seeds_config=None
```
- **行为**: 所有随机操作使用系统随机种子
- **用途**: 需要完全随机的数据集时使用
- **特点**: 每次运行结果都不同

### 2. 全局种子模式
```python
seed=42, seeds_config=None
```
- **行为**: 每个数据集使用 `seed + 类型偏移量 + 数据集索引` 作为种子
- **用途**: 需要整体可重现但数据集间有差异时使用
- **特点**: 相同配置下结果完全可重现，不同数据集有不同的随机行为

### 3. 种子配置模式
```python
seed=None, seeds_config={'dag_generation': [100, 101], 'scm_model': [200, 201]}
```
- **行为**: 使用指定的种子列表，未指定的类型使用随机种子
- **用途**: 需要精确控制特定组件的随机行为时使用
- **特点**: 可以为不同数据集的不同组件指定具体种子

### 4. 混合模式
```python
seed=1000, seeds_config={'dag_generation': [2000, 2001], 'scm_model': None}
```
- **行为**: 优先使用种子列表，未指定的类型使用全局种子+偏移量
- **用途**: 需要部分精确控制，部分使用全局种子时使用
- **特点**: 结合了种子配置和全局种子的优势

## 支持的种子类型

| 种子类型 | 偏移量 | 控制范围 |
|---------|--------|----------|
| `dag_generation` | +100000 | DAG图结构生成 |
| `scm_model` | +200000 | SCM模型内部随机操作(节点赋值函数、噪声生成等) |
| `config_generation` | +300000 | 配置参数采样(节点数、噪声标准差等) |
| `data_sampling` | +400000 | 数据采样过程 |

## 使用示例

### 基础用法

```python
from multi_data_generator import generate_datasets

# 示例1: 使用全局种子
datasets = generate_datasets(
    num_dataset=3,
    h_config=your_config,
    seed=42,
    seeds_config=None
)

# 示例2: 使用种子配置
seeds_config = {
    'dag_generation': [100, 101, 102],  # 为3个数据集指定DAG种子
    'scm_model': [200, 201, 202],       # 为3个数据集指定SCM种子
    'config_generation': None,          # 配置生成使用随机种子
    'data_sampling': [400, 401, 402]   # 为3个数据集指定采样种子
}
datasets = generate_datasets(
    num_dataset=3,
    h_config=your_config,
    seed=None,
    seeds_config=seeds_config
)
```

### 高级用法

```python
# 混合模式：部分使用种子列表，部分使用全局种子
mixed_seeds_config = {
    'dag_generation': [500, 501],  # 为DAG生成指定具体种子
    'scm_model': None,             # SCM模型使用全局种子+偏移
    # config_generation和data_sampling未指定，使用全局种子+偏移
}
datasets = generate_datasets(
    num_dataset=2,
    h_config=your_config,
    seed=1000,
    seeds_config=mixed_seeds_config
)
```

## 种子分配规则

### 当种子列表长度不足时

1. **仅使用seeds_config时**: 使用固定种子(1000000+类型偏移量)生成递增种子
2. **混合模式时**: 使用全局种子+类型偏移量+额外索引生成种子

### 种子计算公式

- **全局种子模式**: `seed + 类型偏移量 + 数据集索引`
- **种子不足时(仅seeds_config)**: `1000000 + 类型偏移量 + (数据集索引 - 种子列表长度)`
- **种子不足时(混合模式)**: `seed + 类型偏移量 + (数据集索引 - 种子列表长度)`

### 种子差异特点

- **相邻种子差异**: 简单递增模式，相邻种子差异为1
- **不同类型种子差异**: 通过大偏移量(10万级别)确保不同类型种子不冲突
- **相关系数**: 测试显示相邻种子的随机序列相关系数平均约0.035，属于可接受的低相关性

## 可重现性验证

系统提供了内置的可重现性测试功能：

```python
from multi_data_generator import test_seed_reproducibility

# 测试全局种子的可重现性
test_seed_reproducibility(h_config, test_seed=42, test_seeds_config=None)

# 测试种子配置的可重现性
test_seeds_config = {
    'dag_generation': [100],
    'scm_model': [200],
    'config_generation': [300],
    'data_sampling': [400]
}
test_seed_reproducibility(h_config, test_seed=None, test_seeds_config=test_seeds_config)
```

## 最佳实践

1. **开发阶段**: 使用全局种子模式确保结果可重现
2. **实验对比**: 使用种子配置模式精确控制变量
3. **生产环境**: 根据需求选择合适的模式
4. **调试问题**: 使用相同种子配置重现问题

## 注意事项

1. 种子值建议使用正整数
2. 不同种子类型的偏移量已预设，无需手动计算
3. 种子列表可以为空列表，等同于None
4. 建议为重要实验保存种子配置以便后续重现

## 文件保存命名

当使用时间戳保存数据文件时，相同的种子配置在不同时间运行会产生相同的数据但保存在不同的时间戳目录中，避免了数据覆盖问题。
