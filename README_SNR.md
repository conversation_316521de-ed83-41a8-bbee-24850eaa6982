# SNR-based Noise Calculation for Causal Data Generation

## 概述

本项目在现有的因果数据生成框架基础上，新增了基于信噪比(SNR)的噪声方差计算功能。通过控制信噪比来确保生成的因果数据既具有足够的复杂性，又能被机器学习模型有效学习。

## 主要特性

### 1. 核心功能
- **SNR-based噪声计算**: 基于图结构深度和复杂度的智能噪声方差计算
- **SNR一致性验证**: 自动验证目标SNR与实际SNR的匹配度
- **特征关系分类**: 按图结构关系对特征进行分类（父节点、子节点、配偶节点、其他节点）
- **相关性和互信息分析**: 计算各类特征与目标变量的相关系数和互信息
- **向后兼容**: 新功能作为可选项，不影响现有代码
- **理论指导+经验校正**: 混合方案确保噪声设计的合理性
- **参数敏感性分析**: 分析不同参数对SNR的影响
- **全面数据质量评估**: 集成所有分析功能的综合质量评估

### 2. 新增模块
- `noise_snr_calculator.py`: SNR计算和验证核心模块
- `data_quality_analyzer.py`: 数据质量和图复杂度分析模块，包含特征分类和相关性分析
- `example_snr_noise.py`: 完整使用示例
- `test_snr_integration.py`: 集成测试脚本
- `test_snr_feature_analysis.py`: SNR验证和特征分析专项测试脚本

## 快速开始

### 1. 基本使用

```python
from multi_data_generator import generate_datasets

# 配置SNR参数
h_config = {
    'device': 'cpu',
    'min_num_node': 8,
    'max_num_node': 15,
    'num_layers': 3,
    # ... 其他现有配置 ...
    
    # 启用SNR方法
    'use_snr_method': True,
    'snr_config': {
        'base_snr': 8.0,                    # 根节点基准SNR
        'depth_decay': 0.85,                # 深度衰减因子
        'min_snr': [11.0, 9.0, 7.0, 5.0, 3.0],  # 各深度最小SNR
        'max_snr': [12.0, 10.0, 8.0, 6.0, 4.0], # 各深度最大SNR
        'complexity_weight': 0.3,           # 复杂度权重
        'calibration_samples': 1000,        # 校正样本数
        'enable_empirical_correction': True, # 启用经验校正
        'min_signal_var': 0.1               # 最小信号方差
    }
}

# 生成数据集
datasets = generate_datasets(
    num_dataset=5,
    h_config=h_config,
    custom_dag_type='random_ER',
    seed=42,
    allow_skip=True
)
```

### 2. 全面数据质量分析（包含SNR验证和特征分类）

```python
from data_quality_analyzer import EnhancedDataQualityAnalyzer

# 创建增强的数据质量分析器
enhanced_analyzer = EnhancedDataQualityAnalyzer()

# 确定特征节点和目标节点
all_nodes = list(scm.dag.nodes())
target_node = all_nodes[-1]  # 假设最后一个节点是目标节点
feature_nodes = all_nodes[:-1]  # 其他节点作为特征节点

# 进行全面分析
comprehensive_report = enhanced_analyzer.comprehensive_analysis(
    scm, x_data, y_data, feature_nodes, target_node
)
print(comprehensive_report)
```

### 3. 基础数据质量分析

```python
from data_quality_analyzer import GraphComplexityAnalyzer, DataQualityEvaluator

# 分析图结构复杂度
complexity_analyzer = GraphComplexityAnalyzer()
complexity_report = complexity_analyzer.generate_complexity_report(scm.dag)
print(complexity_report)

# 评估数据质量（包含特征分类分析）
quality_evaluator = DataQualityEvaluator()
quality_report = quality_evaluator.generate_quality_report(
    x_data, y_data, scm.dag, target_node, feature_nodes
)
print(quality_report)
```

### 4. SNR验证和敏感性分析

```python
from noise_snr_calculator import SNRValidator, ParameterSensitivityAnalyzer

# 获取SCM内置的SNR验证报告
snr_report = scm.get_snr_validation_report()
print(snr_report)

# 检查SNR验证结果
if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results:
    summary = scm.snr_validation_results['summary']
    print(f"SNR控制相对误差: {summary.get('mean_relative_error', 0):.4f}")

# 参数敏感性分析
noise_calculator = NoiseVarianceCalculator(snr_config)
sensitivity_analyzer = ParameterSensitivityAnalyzer(noise_calculator)
results = sensitivity_analyzer.single_parameter_analysis(
    scm.dag, scm.assignment, 'base_snr', [4.0, 6.0, 8.0, 10.0, 12.0]
)
```

### 5. 特征分类和相关性分析

```python
from data_quality_analyzer import DataQualityEvaluator

quality_evaluator = DataQualityEvaluator()

# 特征分类
feature_classification = quality_evaluator.classify_features_by_relationship(
    scm.dag, target_node, feature_nodes
)
print("特征分类结果:")
print(f"直接父节点: {feature_classification['direct_parents']}")
print(f"直接子节点: {feature_classification['direct_children']}")
print(f"配偶节点: {feature_classification['spouse_nodes']}")
print(f"其他节点: {feature_classification['other_nodes']}")

# 相关性分析
correlation_results = quality_evaluator.calculate_feature_target_correlations(
    x_data, y_data, feature_nodes, feature_classification
)

# 互信息分析
mutual_info_results = quality_evaluator.calculate_feature_target_mutual_info(
    x_data, y_data, feature_nodes, feature_classification
)
```

## 配置参数详解

### SNR配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `base_snr` | float | 8.0 | 根节点基准SNR |
| `depth_decay` | float | 0.85 | 深度衰减因子 |
| `min_snr` | List[float] | [11.0, 9.0, 7.0, 5.0, 3.0] | 各深度最小SNR限制 |
| `max_snr` | List[float] | [12.0, 10.0, 8.0, 6.0, 4.0] | 各深度最大SNR限制 |
| `complexity_weight` | float | 0.3 | 复杂度权重 |
| `calibration_samples` | int | 1000 | 校正样本数 |
| `enable_empirical_correction` | bool | True | 是否启用经验校正 |
| `min_signal_var` | float | 0.1 | 最小信号方差 |

### 复杂度级别预设

```python
# 简单级别 - 高SNR，易学习
simple_config = {
    'base_snr': 10.0,
    'depth_decay': 0.9,
    'min_snr': [12.0, 10.0, 8.0, 6.0, 4.0],
    'max_snr': [15.0, 12.0, 10.0, 8.0, 6.0],
    'complexity_weight': 0.2
}

# 中等级别 - 平衡的SNR
medium_config = {
    'base_snr': 8.0,
    'depth_decay': 0.85,
    'min_snr': [11.0, 9.0, 7.0, 5.0, 3.0],
    'max_snr': [12.0, 10.0, 8.0, 6.0, 4.0],
    'complexity_weight': 0.3
}

# 复杂级别 - 低SNR，挑战性
complex_config = {
    'base_snr': 6.0,
    'depth_decay': 0.8,
    'min_snr': [9.0, 7.0, 5.0, 3.0, 2.0],
    'max_snr': [10.0, 8.0, 6.0, 4.0, 3.0],
    'complexity_weight': 0.4
}
```

## 算法原理

### 1. SNR计算公式

对于深度为d的节点，目标SNR计算为：

```
target_snr = base_snr * (depth_decay ^ (d + 1))
target_snr = clip(target_snr, min_snr[d], max_snr[d])
```

### 2. 复杂度调整

对于非根节点，根据入度调整SNR：

```
complexity_factor = 1 + complexity_weight * log(1 + in_degree)
adjusted_snr = target_snr / complexity_factor
```

### 3. 噪声方差计算

```
noise_variance = signal_variance / adjusted_snr
```

## 文件结构

```
LDM_test/
├── multi_scm_model.py          # 修改：集成SNR功能
├── multi_data_generator.py     # 修改：添加SNR配置支持
├── noise_snr_calculator.py     # 新增：SNR计算核心模块
├── data_quality_analyzer.py    # 新增：数据质量分析模块
├── example_snr_noise.py        # 新增：完整使用示例
├── test_snr_integration.py     # 新增：集成测试脚本
└── README_SNR.md              # 新增：SNR功能文档
```

## 运行示例

### 1. 运行完整示例
```bash
python example_snr_noise.py
```

### 2. 运行集成测试
```bash
python test_snr_integration.py
```

### 3. 运行SNR验证和特征分析测试
```bash
python test_snr_feature_analysis.py
```

### 4. 运行现有示例（验证向后兼容性）
```bash
python multi_data_generator.py
```

## 性能优化建议

1. **校正样本数**: 对于大型图，可以减少`calibration_samples`以提高速度
2. **经验校正**: 对于简单应用，可以禁用`enable_empirical_correction`
3. **批量处理**: 使用相同配置生成多个数据集时，可以复用SNR计算结果

## 故障排除

### 常见问题

1. **导入错误**: 确保所有新增模块在Python路径中
2. **配置错误**: 检查SNR配置参数的合理性
3. **内存不足**: 减少`calibration_samples`或图的规模

### 调试技巧

1. 启用详细日志输出
2. 使用小规模图进行测试
3. 检查生成数据的统计特性

## 贡献指南

1. 保持向后兼容性
2. 添加适当的测试用例
3. 更新文档和示例
4. 遵循现有代码风格

## 许可证

本项目遵循与原项目相同的许可证。
