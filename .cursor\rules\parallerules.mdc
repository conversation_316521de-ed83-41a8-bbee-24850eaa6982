---
description: 
globs: 
alwaysApply: false
---
规则：并行化独立GPU加速实验 (A Rule for Parallelizing GPU Experiments)
核心原则 (Core Principle)
当需要执行大量独立的、计算密集的实验（如超参数搜索、多数据集评估）时，应采用基于多进程的并行化策略。每个独立的实验任务应被封装成一个函数，并由主进程分发给一个工作进程池。为了高效利用并避免多GPU资源冲突，必须在每个工作进程内部动态绑定并隔离其所使用的GPU。

实施步骤 (Implementation Steps)
封装独立任务 (Encapsulate the Task)

将单次实验的完整逻辑（数据加载、模型训练、评估、结果保存等）封装到一个独立的函数中，我们称之为“工作函数” (e.g., run_single_experiment)。

此函数必须接受所有必要的实验参数（如 learning_rate, dataset_id），并且必须包含一个 gpu_id 参数，用于指定该任务应在哪块GPU上运行。

使用进程池管理器 (Use a Process Pool Executor)

在主程序入口（if __name__ == "__main__":）内，使用 concurrent.futures.ProcessPoolExecutor 来创建和管理一个进程池。

max_workers 参数应根据CPU核心数和期望的并行度来设定。

定义并分配GPU资源 (Define and Assign GPU Resources)

在主进程中，创建一个列表，包含所有可用的GPU设备ID (e.g., gpu_ids = [0, 1, 2, 3])。

在循环提交任务时，使用**轮询策略（Modulo Operation）**为每个任务分配一个GPU ID。

assigned_gpu = gpu_ids[task_index % len(gpu_ids)]

在工作进程中隔离GPU (Isolate GPU in Worker Process)

【关键步骤】 在“工作函数”的最开始，必须通过设置环境变量 CUDA_VISIBLE_DEVICES 来将当前进程绑定到指定的GPU上。

import os

os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)

这一步确保了每个子进程只能“看到”并使用分配给它的GPU，从而避免了资源竞争和CUDA错误。

提交任务并管理执行 (Submit and Manage Tasks)

遍历你的实验参数组合。


使用 executor.submit(worker_function, *args, **kwargs) 将封装好的工作函数和其所需参数（包括分配好的 gpu_id）提交给进程池。