parents: ['X9'] -> child: X3
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.0023124443032062156

parents: ['X0', 'X2', 'X4', 'X5', 'X8'] -> child: X6
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.34730017568480565

parents: ['X7'] -> child: X8
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.03619217024417254

parents: ['X0'] -> child: X9
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.24781715088048134

parents: ['X7'] -> child: X10
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.18524534922590685

parents: ['X0', 'X1'] -> child: X11
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.2817472195323099