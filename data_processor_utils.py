import torch
import numpy as np
import random
from torch import nn

class DataProcessor:
    def __init__(self, device='cpu', n_sigma=3, min_lb=-5, max_up=5):
        self.device = device
        self.n_sigma = n_sigma
        self.min_lb = min_lb
        self.max_up = max_up
        self.mean = None
        self.std = None
        self.class_boundaries = None

    @staticmethod
    def torch_nanmean(x, dim=0, return_nanshare=False):
        mask = ~torch.isnan(x)
        return DataProcessor.torch_masked_mean(x, mask, dim=dim, return_share_of_ignored_values=return_nanshare)

    @staticmethod
    def torch_nanstd(x, dim=0):
        mask = ~torch.isnan(x)
        return DataProcessor.torch_masked_std(x, mask, dim=dim)

    @staticmethod
    def torch_masked_mean(x, mask, dim=0, return_share_of_ignored_values=False):
        num = torch.where(mask, torch.ones_like(x), torch.zeros_like(x)).sum(dim=dim)
        value = torch.where(mask, x, torch.zeros_like(x)).sum(dim=dim)
        if return_share_of_ignored_values:
            return value / num, 1. - num / x.shape[dim]
        return value / num

    @staticmethod
    def torch_masked_std(x, mask, dim=0):
        mean = DataProcessor.torch_masked_mean(x, mask, dim=dim)
        centered = torch.where(mask, x - mean, torch.zeros_like(x))
        variance = torch.where(mask, centered ** 2, torch.zeros_like(x)).sum(dim=dim) / mask.sum(dim=dim)
        return torch.sqrt(variance)

    def standardize(self, data, fit=False):
        if fit:
            self.mean = self.torch_nanmean(data, dim=0)
            self.std = self.torch_nanstd(data, dim=0) + 1e-6
        return (data - self.mean) / self.std

    def clip_data(self, data):
        return torch.clip(data, self.min_lb, self.max_up)

    def remove_outliers(self, data, fit=False):
        if fit:
            data_mean, data_std = self.torch_nanmean(data), self.torch_nanstd(data)
            cut_off = data_std * self.n_sigma
            lower, upper = data_mean - cut_off, data_mean + cut_off
            mask = (data <= upper) & (data >= lower) & ~torch.isnan(data)
            data_mean, data_std = self.torch_masked_mean(data, mask), self.torch_masked_std(data, mask)
            cut_off = data_std * self.n_sigma
            self.lower, self.upper = data_mean - cut_off, data_mean + cut_off
        
        X = torch.where(data < self.lower, self.lower - torch.log(1 + self.lower - data), data)
        X = torch.where(data > self.upper, self.upper + torch.log(1 + data - self.upper), X)
        return X

    def discretize(self, x, class_boundaries=None, num_classes=None, ordered_p=0.5):
        if class_boundaries is None:
            if num_classes is None:
                num_classes = self.class_sampler_f(2, 10)()
            class_boundaries = torch.randint(0, x.shape[0], (num_classes - 1,))
            class_boundaries = x[class_boundaries].unsqueeze(1)
        d = (x > class_boundaries).sum(axis=0)
        return d, class_boundaries

    @staticmethod
    def class_sampler_f(min_, max_):
        def s():
            if random.random() > 0.5:
                return round(np.random.uniform(min_, max_))
            return 2
        return s